{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/instrumentation.ts"], "sourcesContent": ["export async function register() {\n  if (process.env.NEXT_RUNTIME === \"nodejs\") {\n    await import(\"./sentry.server.config\");\n  }\n\n  if (process.env.NEXT_RUNTIME === \"edge\") {\n    await import(\"./sentry.edge.config\");\n  }\n}"], "names": [], "mappings": ";;;AAAO,eAAe;IACpB,wCAA2C;QACzC;IACF;IAEA,uCAAyC;;IAEzC;AACF", "debugId": null}}]}