{"version": 3, "sources": [], "sections": [{"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web/sentry.server.config.ts"], "sourcesContent": ["import * as Sentry from \"@sentry/nextjs\";\n\nSentry.init({\n  dsn: process.env.SENTRY_DSN,\n\n  // Set tracesSampleRate to 1.0 to capture 100%\n  // of the transactions for performance monitoring.\n  // We recommend adjusting this value in production\n  tracesSampleRate: 1.0,\n\n  // ...\n\n  // Note: if you want to override the automatic release value, do not set a\n  // `release` value here - use the environment variable `SENTRY_RELEASE`, so\n  // that it will also get attached to your source maps\n});"], "names": [], "mappings": ";AAAA;;AAEA,CAAA,GAAA,iXAAA,CAAA,OAAW,AAAD,EAAE;IACV,KAAK,QAAQ,GAAG,CAAC,UAAU;IAE3B,8CAA8C;IAC9C,kDAAkD;IAClD,kDAAkD;IAClD,kBAAkB;AAOpB", "debugId": null}}]}