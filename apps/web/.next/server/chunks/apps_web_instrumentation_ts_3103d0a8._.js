module.exports = {

"[project]/apps/web/instrumentation.ts [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "register": (()=>register)
});
async function register() {
    if ("TURBOPACK compile-time truthy", 1) {
        await __turbopack_context__.r("[project]/apps/web/sentry.server.config.ts [instrumentation] (ecmascript, async loader)")(__turbopack_context__.i);
    }
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
}
}}),

};

//# sourceMappingURL=apps_web_instrumentation_ts_3103d0a8._.js.map