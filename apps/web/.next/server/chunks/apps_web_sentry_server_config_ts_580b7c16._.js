module.exports = {

"[project]/apps/web/sentry.server.config.ts [instrumentation] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[externals]_node:inspector_be94cfd8._.js",
  "server/chunks/1e02b_@sentry_core_build_cjs_b839f491._.js",
  "server/chunks/195ad_@sentry_node_build_cjs_ead962a8._.js",
  "server/chunks/d486d_@opentelemetry_core_build_esm_a458a933._.js",
  "server/chunks/ffc21_@opentelemetry_semantic-conventions_build_esm_326d840b._.js",
  "server/chunks/44e58_@opentelemetry_semantic-conventions_build_esm_43f2407e._.js",
  "server/chunks/2d6f5_@opentelemetry_sdk-trace-base_build_esm_18781278._.js",
  "server/chunks/46f7a_@opentelemetry_resources_build_esm_a7ee80be._.js",
  "server/chunks/03f1d_@sentry_nextjs_build_cjs_aa3c3c20._.js",
  "server/chunks/node_modules__pnpm_73b779f9._.js",
  "server/chunks/[root-of-the-server]__4d8207c4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/apps/web/sentry.server.config.ts [instrumentation] (ecmascript)");
    });
});
}}),

};