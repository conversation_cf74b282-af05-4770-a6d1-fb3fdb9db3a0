{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/errors/errorThrower.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/hooks/useAuth.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/contexts/AuthContext.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/contexts/IsomorphicClerkContext.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/errors/messages.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/hooks/useAssertWrappedByClerkProvider.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/hooks/utils.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/hooks/useEmailLink.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/hooks/useSignIn.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/hooks/useSignUp.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/hooks/index.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/components/controlComponents.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/contexts/SessionContext.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/components/withClerk.tsx"], "sourcesContent": ["import type { ErrorThrowerOptions } from '@clerk/shared/error';\nimport { buildErrorThrower } from '@clerk/shared/error';\n\nconst errorThrower = buildErrorThrower({ packageName: '@clerk/clerk-react' });\n\nexport { errorThrower };\n\n/**\n * Overrides options of the internal errorThrower (eg setting packageName prefix).\n *\n * @internal\n */\nexport function setErrorThrowerOptions(options: ErrorThrowerOptions) {\n  errorThrower.setMessages(options).setPackageName(options);\n}\n", "import { createCheckAuthorization, resolveAuthState } from '@clerk/shared/authorization';\nimport { eventMethodCalled } from '@clerk/shared/telemetry';\nimport type {\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  JwtPayload,\n  PendingSessionOptions,\n  SignOut,\n  UseAuthReturn,\n} from '@clerk/types';\nimport { useCallback } from 'react';\n\nimport { useAuthContext } from '../contexts/AuthContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { errorThrower } from '../errors/errorThrower';\nimport { invalidStateError } from '../errors/messages';\nimport { useAssertWrappedByClerkProvider } from './useAssertWrappedByClerkProvider';\nimport { createGetToken, createSignOut } from './utils';\n\n/**\n * @inline\n */\ntype UseAuthOptions = Record<string, any> | PendingSessionOptions | undefined | null;\n\n/**\n * The `useAuth()` hook provides access to the current user's authentication state and methods to manage the active session.\n *\n * > [!NOTE]\n * > To access auth data server-side, see the [`Auth` object reference doc](https://clerk.com/docs/references/backend/types/auth-object).\n *\n * <If sdk=\"nextjs\">\n * By default, Next.js opts all routes into static rendering. If you need to opt a route or routes into dynamic rendering because you need to access the authentication data at request time, you can create a boundary by passing the `dynamic` prop to `<ClerkProvider>`. See the [guide on rendering modes](https://clerk.com/docs/references/nextjs/rendering-modes) for more information, including code examples.\n * </If>\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Signed out\", \"Signed in (no active organization)\", \"Signed in (with active organization)\"]\n *\n * @param [initialAuthStateOrOptions] - An object containing the initial authentication state or options for the `useAuth()` hook. If not provided, the hook will attempt to derive the state from the context. `treatPendingAsSignedOut` is a boolean that indicates whether pending sessions are considered as signed out or not. Defaults to `true`.\n *\n * @function\n *\n * @example\n *\n * The following example demonstrates how to use the `useAuth()` hook to access the current auth state, like whether the user is signed in or not. It also includes a basic example for using the `getToken()` method to retrieve a session token for fetching data from an external resource.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/pages/ExternalDataPage.tsx' }}\n * import { useAuth } from '@clerk/clerk-react'\n *\n * export default function ExternalDataPage() {\n *   const { userId, sessionId, getToken, isLoaded, isSignedIn } = useAuth()\n *\n *   const fetchExternalData = async () => {\n *     const token = await getToken()\n *\n *     // Fetch data from an external API\n *     const response = await fetch('https://api.example.com/data', {\n *       headers: {\n *         Authorization: `Bearer ${token}`,\n *       },\n *     })\n *\n *     return response.json()\n *   }\n *\n *   if (!isLoaded) {\n *     return <div>Loading...</div>\n *   }\n *\n *   if (!isSignedIn) {\n *     return <div>Sign in to view this page</div>\n *   }\n *\n *   return (\n *     <div>\n *       <p>\n *         Hello, {userId}! Your current active session is {sessionId}.\n *       </p>\n *       <button onClick={fetchExternalData}>Fetch Data</button>\n *     </div>\n *   )\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../docs/use-auth.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n */\nexport const useAuth = (initialAuthStateOrOptions: UseAuthOptions = {}): UseAuthReturn => {\n  useAssertWrappedByClerkProvider('useAuth');\n\n  const { treatPendingAsSignedOut, ...rest } = initialAuthStateOrOptions ?? {};\n  const initialAuthState = rest as any;\n\n  const authContextFromHook = useAuthContext();\n  let authContext = authContextFromHook;\n\n  if (authContext.sessionId === undefined && authContext.userId === undefined) {\n    authContext = initialAuthState != null ? initialAuthState : {};\n  }\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const getToken: GetToken = useCallback(createGetToken(isomorphicClerk), [isomorphicClerk]);\n  const signOut: SignOut = useCallback(createSignOut(isomorphicClerk), [isomorphicClerk]);\n\n  isomorphicClerk.telemetry?.record(eventMethodCalled('useAuth', { treatPendingAsSignedOut }));\n\n  return useDerivedAuth(\n    {\n      ...authContext,\n      getToken,\n      signOut,\n    },\n    {\n      treatPendingAsSignedOut:\n        treatPendingAsSignedOut ?? isomorphicClerk.__internal_getOption?.('treatPendingAsSignedOut'),\n    },\n  );\n};\n\n/**\n * A hook that derives and returns authentication state and utility functions based on the provided auth object.\n *\n * @param authObject - An object containing authentication-related properties and functions.\n *\n * @returns A derived authentication state with helper methods. If the authentication state is invalid, an error is thrown.\n *\n * @remarks\n * This hook inspects session, user, and organization information to determine the current authentication state.\n * It returns an object that includes various properties such as whether the state is loaded, if a user is signed in,\n * session and user identifiers, organization roles, and a `has` function for authorization checks.\n * Additionally, it provides `signOut` and `getToken` functions if applicable.\n *\n * @example\n * ```tsx\n * const {\n *   isLoaded,\n *   isSignedIn,\n *   userId,\n *   orgId,\n *   has,\n *   signOut,\n *   getToken\n * } = useDerivedAuth(authObject);\n * ```\n */\nexport function useDerivedAuth(\n  authObject: any,\n  { treatPendingAsSignedOut = true }: PendingSessionOptions = {},\n): UseAuthReturn {\n  const { userId, orgId, orgRole, has, signOut, getToken, orgPermissions, factorVerificationAge, sessionClaims } =\n    authObject ?? {};\n\n  const derivedHas = useCallback(\n    (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => {\n      if (has) {\n        return has(params);\n      }\n      return createCheckAuthorization({\n        userId,\n        orgId,\n        orgRole,\n        orgPermissions,\n        factorVerificationAge,\n        features: ((sessionClaims as JwtPayload | undefined)?.fea as string) || '',\n        plans: ((sessionClaims as JwtPayload | undefined)?.pla as string) || '',\n      })(params);\n    },\n    [has, userId, orgId, orgRole, orgPermissions, factorVerificationAge],\n  );\n\n  const payload = resolveAuthState({\n    authObject: {\n      ...authObject,\n      getToken,\n      signOut,\n      has: derivedHas,\n    },\n    options: {\n      treatPendingAsSignedOut,\n    },\n  });\n\n  if (!payload) {\n    return errorThrower.throw(invalidStateError);\n  }\n\n  return payload;\n}\n", "import { createContextAndHook } from '@clerk/shared/react';\nimport type {\n  ActClaim,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  SessionStatusClaim,\n} from '@clerk/types';\n\nexport type AuthContextValue = {\n  userId: string | null | undefined;\n  sessionId: string | null | undefined;\n  sessionStatus: SessionStatusClaim | null | undefined;\n  sessionClaims: JwtPayload | null | undefined;\n  actor: ActClaim | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: OrganizationCustomRoleKey | null | undefined;\n  orgSlug: string | null | undefined;\n  orgPermissions: OrganizationCustomPermissionKey[] | null | undefined;\n  factorVerificationAge: [number, number] | null;\n};\n\nexport const [AuthContext, useAuthContext] = createContextAndHook<AuthContextValue>('AuthContext');\n", "import { ClerkInstanceContext, useClerkInstanceContext } from '@clerk/shared/react';\n\nimport type { IsomorphicClerk } from '../isomorphicClerk';\n\nexport const IsomorphicClerkContext = ClerkInstanceContext;\nexport const useIsomorphicClerkContext = useClerkInstanceContext as unknown as () => IsomorphicClerk;\n", "export const noClerkProviderError = 'You must wrap your application in a <ClerkProvider> component.';\n\nexport const multipleClerkProvidersError =\n  \"You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.\";\n\nexport const multipleChildrenInButtonComponent = (name: string) =>\n  `You've passed multiple children components to <${name}/>. You can only pass a single child component or text.`;\n\nexport const invalidStateError =\n  'Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support';\n\nexport const unsupportedNonBrowserDomainOrProxyUrlFunction =\n  'Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.';\n\nexport const userProfilePageRenderedError =\n  '<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.';\nexport const userProfileLinkRenderedError =\n  '<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.';\n\nexport const organizationProfilePageRenderedError =\n  '<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.';\nexport const organizationProfileLinkRenderedError =\n  '<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.';\n\nexport const customPagesIgnoredComponent = (componentName: string) =>\n  `<${componentName} /> can only accept <${componentName}.Page /> and <${componentName}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`;\n\nexport const customPageWrongProps = (componentName: string) =>\n  `Missing props. <${componentName}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`;\n\nexport const customLinkWrongProps = (componentName: string) =>\n  `Missing props. <${componentName}.Link /> component requires the following props: url, label and labelIcon.`;\n\nexport const useAuthHasRequiresRoleOrPermission =\n  'Missing parameters. `has` from `useAuth` requires a permission or role key to be passed. Example usage: `has({permission: \"org:posts:edit\"`';\n\nexport const noPathProvidedError = (componentName: string) =>\n  `The <${componentName}/> component uses path-based routing by default unless a different routing strategy is provided using the \\`routing\\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \\`path\\` prop. Example: <${componentName} path={'/my-path'} />`;\n\nexport const incompatibleRoutingWithPathProvidedError = (componentName: string) =>\n  `The \\`path\\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \\`routing='path'\\` to the <${componentName}/> component, or drop the \\`path\\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`;\n\nexport const userButtonIgnoredComponent = `<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`;\n\nexport const customMenuItemsIgnoredComponent =\n  '<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.';\n\nexport const userButtonMenuItemsRenderedError =\n  '<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.';\n\nexport const userButtonMenuActionRenderedError =\n  '<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.';\n\nexport const userButtonMenuLinkRenderedError =\n  '<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.';\n\nexport const userButtonMenuItemLinkWrongProps =\n  'Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.';\n\nexport const userButtonMenuItemsActionWrongsProps =\n  'Missing props. <UserButton.Action /> component requires the following props: label.';\n", "import { useAssertWrappedByClerkProvider as useSharedAssertWrappedByClerkProvider } from '@clerk/shared/react';\n\nimport { errorThrower } from '../errors/errorThrower';\n\nexport const useAssertWrappedByClerkProvider = (source: string): void => {\n  useSharedAssertWrappedByClerkProvider(() => {\n    errorThrower.throwMissingClerkProviderError({ source });\n  });\n};\n", "import type { IsomorphicClerk } from '../isomorphicClerk';\n\n/**\n * @internal\n */\nconst clerkLoaded = (isomorphicClerk: IsomorphicClerk) => {\n  return new Promise<void>(resolve => {\n    const handler = (status: string) => {\n      if (['ready', 'degraded'].includes(status)) {\n        resolve();\n        isomorphicClerk.off('status', handler);\n      }\n    };\n\n    // Register the event listener\n    isomorphicClerk.on('status', handler, { notify: true });\n  });\n};\n\n/**\n * @internal\n */\nexport const createGetToken = (isomorphicClerk: IsomorphicClerk) => {\n  return async (options: any) => {\n    await clerkLoaded(isomorphicClerk);\n    if (!isomorphicClerk.session) {\n      return null;\n    }\n    return isomorphicClerk.session.getToken(options);\n  };\n};\n\n/**\n * @internal\n */\nexport const createSignOut = (isomorphicClerk: IsomorphicClerk) => {\n  return async (...args: any) => {\n    await clerkLoaded(isomorphicClerk);\n    return isomorphicClerk.signOut(...args);\n  };\n};\n", "import type {\n  CreateEmailLinkFlowReturn,\n  EmailAddressResource,\n  SignInResource,\n  SignInStartEmailLinkFlowParams,\n  SignUpResource,\n  StartEmailLinkFlowParams,\n} from '@clerk/types';\nimport React from 'react';\n\ntype EmailLinkable = SignUpResource | EmailAddressResource | SignInResource;\ntype UseEmailLinkSignInReturn = CreateEmailLinkFlowReturn<SignInStartEmailLinkFlowParams, SignInResource>;\ntype UseEmailLinkSignUpReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, SignUpResource>;\ntype UseEmailLinkEmailAddressReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, EmailAddressResource>;\n\nfunction useEmailLink(resource: SignInResource): UseEmailLinkSignInReturn;\nfunction useEmailLink(resource: SignUpResource): UseEmailLinkSignUpReturn;\nfunction useEmailLink(resource: EmailAddressResource): UseEmailLinkEmailAddressReturn;\nfunction useEmailLink(\n  resource: EmailLinkable,\n): UseEmailLinkSignInReturn | UseEmailLinkSignUpReturn | UseEmailLinkEmailAddressReturn {\n  const { startEmailLinkFlow, cancelEmailLinkFlow } = React.useMemo(() => resource.createEmailLinkFlow(), [resource]);\n\n  React.useEffect(() => {\n    return cancelEmailLinkFlow;\n  }, []);\n\n  return {\n    startEmailLinkFlow,\n    cancelEmailLinkFlow,\n  } as UseEmailLinkSignInReturn | UseEmailLinkSignUpReturn | UseEmailLinkEmailAddressReturn;\n}\n\nexport { useEmailLink };\n", "import { useClientContext } from '@clerk/shared/react';\nimport { eventMethodCalled } from '@clerk/shared/telemetry';\nimport type { UseSignInReturn } from '@clerk/types';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useAssertWrappedByClerkProvider } from './useAssertWrappedByClerkProvider';\n\n/**\n * The `useSignIn()` hook provides access to the [`SignIn`](https://clerk.com/docs/references/javascript/sign-in) object, which allows you to check the current state of a sign-in attempt and manage the sign-in flow. You can use this to create a [custom sign-in flow](https://clerk.com/docs/custom-flows/overview#sign-in-flow).\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Loaded\"]\n *\n * @example\n * ### Check the current state of a sign-in\n *\n * The following example uses the `useSignIn()` hook to access the [`SignIn`](https://clerk.com/docs/references/javascript/sign-in) object, which contains the current sign-in attempt status and methods to create a new sign-in attempt. The `isLoaded` property is used to handle the loading state.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/pages/SignInPage.tsx' }}\n * import { useSignIn } from '@clerk/clerk-react'\n *\n * export default function SignInPage() {\n *   const { isLoaded, signIn } = useSignIn()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return <div>The current sign-in attempt status is {signIn?.status}.</div>\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../docs/use-sign-in.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n *\n * @example\n * ### Create a custom sign-in flow with `useSignIn()`\n *\n * The `useSignIn()` hook can also be used to build fully custom sign-in flows, if Clerk's prebuilt components don't meet your specific needs or if you require more control over the authentication flow. Different sign-in flows include email and password, email and phone codes, email links, and multifactor (MFA). To learn more about using the `useSignIn()` hook to create custom flows, see the [custom flow guides](https://clerk.com/docs/custom-flows/overview).\n *\n * ```empty```\n */\nexport const useSignIn = (): UseSignInReturn => {\n  useAssertWrappedByClerkProvider('useSignIn');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  isomorphicClerk.telemetry?.record(eventMethodCalled('useSignIn'));\n\n  if (!client) {\n    return { isLoaded: false, signIn: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    signIn: client.signIn,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n", "import { useClientContext } from '@clerk/shared/react';\nimport { eventMethodCalled } from '@clerk/shared/telemetry';\nimport type { UseSignUpReturn } from '@clerk/types';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useAssertWrappedByClerkProvider } from './useAssertWrappedByClerkProvider';\n\n/**\n * The `useSignUp()` hook provides access to the [`SignUp`](https://clerk.com/docs/references/javascript/sign-up) object, which allows you to check the current state of a sign-up attempt and manage the sign-up flow. You can use this to create a [custom sign-up flow](https://clerk.com/docs/custom-flows/overview#sign-up-flow).\n *\n * @unionReturnHeadings\n * [\"Initialization\", \"Loaded\"]\n *\n * @example\n * ### Check the current state of a sign-up\n *\n * The following example uses the `useSignUp()` hook to access the [`SignUp`](https://clerk.com/docs/references/javascript/sign-up) object, which contains the current sign-up attempt status and methods to create a new sign-up attempt. The `isLoaded` property is used to handle the loading state.\n *\n * <Tabs items='React,Next.js'>\n * <Tab>\n *\n * ```tsx {{ filename: 'src/pages/SignUpPage.tsx' }}\n * import { useSignUp } from '@clerk/clerk-react'\n *\n * export default function SignUpPage() {\n *   const { isLoaded, signUp } = useSignUp()\n *\n *   if (!isLoaded) {\n *     // Handle loading state\n *     return null\n *   }\n *\n *   return <div>The current sign-up attempt status is {signUp?.status}.</div>\n * }\n * ```\n *\n * </Tab>\n * <Tab>\n *\n * {@include ../../docs/use-sign-up.md#nextjs-01}\n *\n * </Tab>\n * </Tabs>\n *\n * @example\n * ### Create a custom sign-up flow with `useSignUp()`\n *\n * The `useSignUp()` hook can also be used to build fully custom sign-up flows, if Clerk's prebuilt components don't meet your specific needs or if you require more control over the authentication flow. Different sign-up flows include email and password, email and phone codes, email links, and multifactor (MFA). To learn more about using the `useSignUp()` hook to create custom flows, see the [custom flow guides](https://clerk.com/docs/custom-flows/overview).\n *\n * ```empty```\n */\nexport const useSignUp = (): UseSignUpReturn => {\n  useAssertWrappedByClerkProvider('useSignUp');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  isomorphicClerk.telemetry?.record(eventMethodCalled('useSignUp'));\n\n  if (!client) {\n    return { isLoaded: false, signUp: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    signUp: client.signUp,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n", "export { useAuth } from './useAuth';\nexport { useEmailLink } from './useEmailLink';\nexport { useSignIn } from './useSignIn';\nexport { useSignUp } from './useSignUp';\nexport {\n  useClerk,\n  useOrganization,\n  useOrganizationList,\n  useSessionList,\n  useUser,\n  useSession,\n  useReverification,\n} from '@clerk/shared/react';\n", "import { deprecated } from '@clerk/shared/deprecated';\nimport type {\n  Autocomplete,\n  CheckAuthorizationWithCustomPermissions,\n  HandleOAuthCallbackParams,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  PendingSessionOptions,\n} from '@clerk/types';\nimport React from 'react';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useSessionContext } from '../contexts/SessionContext';\nimport { useAuth } from '../hooks';\nimport { useAssertWrappedByClerkProvider } from '../hooks/useAssertWrappedByClerkProvider';\nimport type { RedirectToSignInProps, RedirectToSignUpProps, WithClerkProp } from '../types';\nimport { withClerk } from './withClerk';\n\nexport const SignedIn = ({ children, treatPendingAsSignedOut }: React.PropsWithChildren<PendingSessionOptions>) => {\n  useAssertWrappedByClerkProvider('SignedIn');\n\n  const { userId } = useAuth({ treatPendingAsSignedOut });\n  if (userId) {\n    return children;\n  }\n  return null;\n};\n\nexport const SignedOut = ({ children, treatPendingAsSignedOut }: React.PropsWithChildren<PendingSessionOptions>) => {\n  useAssertWrappedByClerkProvider('SignedOut');\n\n  const { userId } = useAuth({ treatPendingAsSignedOut });\n  if (userId === null) {\n    return children;\n  }\n  return null;\n};\n\nexport const ClerkLoaded = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('ClerkLoaded');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (!isomorphicClerk.loaded) {\n    return null;\n  }\n  return children;\n};\n\nexport const ClerkLoading = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('ClerkLoading');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.status !== 'loading') {\n    return null;\n  }\n  return children;\n};\n\nexport const ClerkFailed = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('ClerkFailed');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.status !== 'error') {\n    return null;\n  }\n  return children;\n};\n\nexport const ClerkDegraded = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('ClerkDegraded');\n\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.status !== 'degraded') {\n    return null;\n  }\n  return children;\n};\n\nexport type ProtectProps = React.PropsWithChildren<\n  (\n    | {\n        condition?: never;\n        role: OrganizationCustomRoleKey;\n        permission?: never;\n        feature?: never;\n        plan?: never;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        feature?: never;\n        plan?: never;\n        permission: OrganizationCustomPermissionKey;\n      }\n    | {\n        condition: (has: CheckAuthorizationWithCustomPermissions) => boolean;\n        role?: never;\n        permission?: never;\n        feature?: never;\n        plan?: never;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        permission?: never;\n        feature: Autocomplete<`user:${string}` | `org:${string}`>;\n        plan?: never;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        permission?: never;\n        feature?: never;\n        plan: Autocomplete<`user:${string}` | `org:${string}`>;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        permission?: never;\n        feature?: never;\n        plan?: never;\n      }\n  ) & {\n    fallback?: React.ReactNode;\n  } & PendingSessionOptions\n>;\n\n/**\n * Use `<Protect/>` in order to prevent unauthenticated or unauthorized users from accessing the children passed to the component.\n *\n * Examples:\n * ```\n * <Protect permission=\"a_permission_key\" />\n * <Protect role=\"a_role_key\" />\n * <Protect condition={(has) => has({permission:\"a_permission_key\"})} />\n * <Protect condition={(has) => has({role:\"a_role_key\"})} />\n * <Protect fallback={<p>Unauthorized</p>} />\n * ```\n */\nexport const Protect = ({ children, fallback, treatPendingAsSignedOut, ...restAuthorizedParams }: ProtectProps) => {\n  useAssertWrappedByClerkProvider('Protect');\n\n  const { isLoaded, has, userId } = useAuth({ treatPendingAsSignedOut });\n\n  /**\n   * Avoid flickering children or fallback while clerk is loading sessionId or userId\n   */\n  if (!isLoaded) {\n    return null;\n  }\n\n  /**\n   * Fallback to UI provided by user or `null` if authorization checks failed\n   */\n  const unauthorized = fallback ?? null;\n\n  const authorized = children;\n\n  if (!userId) {\n    return unauthorized;\n  }\n\n  /**\n   * Check against the results of `has` called inside the callback\n   */\n  if (typeof restAuthorizedParams.condition === 'function') {\n    if (restAuthorizedParams.condition(has)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  if (\n    restAuthorizedParams.role ||\n    restAuthorizedParams.permission ||\n    restAuthorizedParams.feature ||\n    restAuthorizedParams.plan\n  ) {\n    if (has(restAuthorizedParams)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  /**\n   * If neither of the authorization params are passed behave as the `<SignedIn/>`.\n   * If fallback is present render that instead of rendering nothing.\n   */\n  return authorized;\n};\n\nexport const RedirectToSignIn = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignInProps>) => {\n  const { client, session } = clerk;\n\n  const hasSignedInSessions = client.signedInSessions\n    ? client.signedInSessions.length > 0\n    : // Compat for clerk-js<5.54.0 (which was released with the `signedInSessions` property)\n      client.activeSessions && client.activeSessions.length > 0;\n\n  React.useEffect(() => {\n    if (session === null && hasSignedInSessions) {\n      void clerk.redirectToAfterSignOut();\n    } else {\n      void clerk.redirectToSignIn(props);\n    }\n  }, []);\n\n  return null;\n}, 'RedirectToSignIn');\n\nexport const RedirectToSignUp = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignUpProps>) => {\n  React.useEffect(() => {\n    void clerk.redirectToSignUp(props);\n  }, []);\n\n  return null;\n}, 'RedirectToSignUp');\n\n/**\n * @function\n * @deprecated Use [`redirectToUserProfile()`](https://clerk.com/docs/references/javascript/clerk#redirect-to-user-profile) instead.\n */\nexport const RedirectToUserProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    deprecated('RedirectToUserProfile', 'Use the `redirectToUserProfile()` method instead.');\n    void clerk.redirectToUserProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToUserProfile');\n\n/**\n * @function\n * @deprecated Use [`redirectToOrganizationProfile()`](https://clerk.com/docs/references/javascript/clerk#redirect-to-organization-profile) instead.\n */\nexport const RedirectToOrganizationProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    deprecated('RedirectToOrganizationProfile', 'Use the `redirectToOrganizationProfile()` method instead.');\n    void clerk.redirectToOrganizationProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToOrganizationProfile');\n\n/**\n * @function\n * @deprecated Use [`redirectToCreateOrganization()`](https://clerk.com/docs/references/javascript/clerk#redirect-to-create-organization) instead.\n */\nexport const RedirectToCreateOrganization = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    deprecated('RedirectToCreateOrganization', 'Use the `redirectToCreateOrganization()` method instead.');\n    void clerk.redirectToCreateOrganization();\n  }, []);\n\n  return null;\n}, 'RedirectToCreateOrganization');\n\nexport const AuthenticateWithRedirectCallback = withClerk(\n  ({ clerk, ...handleRedirectCallbackParams }: WithClerkProp<HandleOAuthCallbackParams>) => {\n    React.useEffect(() => {\n      void clerk.handleRedirectCallback(handleRedirectCallbackParams);\n    }, []);\n\n    return null;\n  },\n  'AuthenticateWithRedirectCallback',\n);\n\nexport const MultisessionAppSupport = ({ children }: React.PropsWithChildren<unknown>) => {\n  useAssertWrappedByClerkProvider('MultisessionAppSupport');\n\n  const session = useSessionContext();\n  return <React.Fragment key={session ? session.id : 'no-users'}>{children}</React.Fragment>;\n};\n", "export { SessionContext, useSessionContext } from '@clerk/shared/react';\n", "import type { LoadedClerk, Without } from '@clerk/types';\nimport React from 'react';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useAssertWrappedByClerkProvider } from '../hooks/useAssertWrappedByClerkProvider';\n\nexport const withClerk = <P extends { clerk: LoadedClerk; component?: string }>(\n  Component: React.ComponentType<P>,\n  displayNameOrOptions?: string | { component: string; renderWhileLoading?: boolean },\n) => {\n  const passedDisplayedName =\n    typeof displayNameOrOptions === 'string' ? displayNameOrOptions : displayNameOrOptions?.component;\n  const displayName = passedDisplayedName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n\n  const options = typeof displayNameOrOptions === 'string' ? undefined : displayNameOrOptions;\n\n  const HOC = (props: Without<P, 'clerk'>) => {\n    useAssertWrappedByClerkProvider(displayName || 'withClerk');\n\n    const clerk = useIsomorphicClerkContext();\n\n    if (!clerk.loaded && !options?.renderWhileLoading) {\n      return null;\n    }\n\n    return (\n      <Component\n        {...(props as P)}\n        component={displayName}\n        clerk={clerk}\n      />\n    );\n  };\n  HOC.displayName = `withClerk(${displayName})`;\n  return HOC;\n};\n"], "names": ["eventMethodCalled", "eventMethodCalled", "useClientContext", "eventMethodCalled", "useClientContext", "eventMethodCalled", "React", "React", "React", "React"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAS,yBAAyB;;;;ACDlC,SAAS,0BAA0B,wBAAwB;AAC3D,SAAS,yBAAyB;;AASlC,SAAS,mBAAmB;;ACV5B,SAAS,4BAA4B;;;ASArC,SAAS,kBAAkB;;AXG3B,IAAM,8TAAe,oBAAA,EAAkB;IAAE,aAAa;AAAqB,CAAC;AASrE,SAAS,uBAAuB,OAAA,EAA8B;IACnE,aAAa,WAAA,CAAY,OAAO,EAAE,cAAA,CAAe,OAAO;AAC1D;;;;;AEQO,IAAM,CAAC,aAAa,cAAc,CAAA,GAAI,mUAAA,EAAuC,aAAa;;AClB1F,IAAM,iUAAyB,uBAAA;AAC/B,IAAM,oUAA4B,0BAAA;;ACHlC,IAAM,8BACX;AAEK,IAAM,oCAAoC,CAAC,OAChD,CAAA,+CAAA,EAAkD,IAAI,CAAA,uDAAA,CAAA;AAEjD,IAAM,oBACX;AAEK,IAAM,gDACX;AAEK,IAAM,+BACX;AACK,IAAM,+BACX;AAEK,IAAM,uCACX;AACK,IAAM,uCACX;AAEK,IAAM,8BAA8B,CAAC,gBAC1C,CAAA,CAAA,EAAI,aAAa,CAAA,qBAAA,EAAwB,aAAa,CAAA,cAAA,EAAiB,aAAa,CAAA,yJAAA,CAAA;AAE/E,IAAM,uBAAuB,CAAC,gBACnC,CAAA,gBAAA,EAAmB,aAAa,CAAA,+HAAA,CAAA;AAE3B,IAAM,uBAAuB,CAAC,gBACnC,CAAA,gBAAA,EAAmB,aAAa,CAAA,0EAAA,CAAA;AAK3B,IAAM,sBAAsB,CAAC,gBAClC,CAAA,KAAA,EAAQ,aAAa,CAAA,kQAAA,EAAqQ,aAAa,CAAA,qBAAA,CAAA;AAElS,IAAM,2CAA2C,CAAC,gBACvD,CAAA,kJAAA,EAAqJ,aAAa,CAAA,0IAAA,CAAA;AAE7J,IAAM,6BAA6B,CAAA,2QAAA,CAAA;AAEnC,IAAM,kCACX;AAEK,IAAM,mCACX;AAEK,IAAM,oCACX;AAEK,IAAM,kCACX;AAEK,IAAM,mCACX;AAEK,IAAM,uCACX;;ACxDK,IAAM,kCAAkC,CAAC,WAAyB;IACvE,CAAA,GAAA,uSAAA,CAAA,kCAAA,EAAsC,MAAM;QAC1C,aAAa,8BAAA,CAA+B;YAAE;QAAO,CAAC;IACxD,CAAC;AACH;;ACHA,IAAM,cAAc,CAAC,oBAAqC;IACxD,OAAO,IAAI,QAAc,CAAA,YAAW;QAClC,MAAM,UAAU,CAAC,WAAmB;YAClC,IAAI;gBAAC;gBAAS,UAAU;aAAA,CAAE,QAAA,CAAS,MAAM,GAAG;gBAC1C,QAAQ;gBACR,gBAAgB,GAAA,CAAI,UAAU,OAAO;YACvC;QACF;QAGA,gBAAgB,EAAA,CAAG,UAAU,SAAS;YAAE,QAAQ;QAAK,CAAC;IACxD,CAAC;AACH;AAKO,IAAM,iBAAiB,CAAC,oBAAqC;IAClE,OAAO,OAAO,YAAiB;QAC7B,MAAM,YAAY,eAAe;QACjC,IAAI,CAAC,gBAAgB,OAAA,EAAS;YAC5B,OAAO;QACT;QACA,OAAO,gBAAgB,OAAA,CAAQ,QAAA,CAAS,OAAO;IACjD;AACF;AAKO,IAAM,gBAAgB,CAAC,oBAAqC;IACjE,OAAO,OAAA,GAAU,SAAc;QAC7B,MAAM,YAAY,eAAe;QACjC,OAAO,gBAAgB,OAAA,CAAQ,GAAG,IAAI;IACxC;AACF;;ALsDO,IAAM,UAAU,CAAC,4BAA4C,CAAC,CAAA,KAAqB;IA9F1F,IAAA,IAAA;IA+FE,gCAAgC,SAAS;IAEzC,MAAM,EAAE,uBAAA,EAAyB,GAAG,KAAK,CAAA,GAAI,6BAAA,OAAA,4BAA6B,CAAC;IAC3E,MAAM,mBAAmB;IAEzB,MAAM,sBAAsB,eAAe;IAC3C,IAAI,cAAc;IAElB,IAAI,YAAY,SAAA,KAAc,KAAA,KAAa,YAAY,MAAA,KAAW,KAAA,GAAW;QAC3E,cAAc,oBAAoB,OAAO,mBAAmB,CAAC;IAC/D;IAEA,MAAM,kBAAkB,0BAA0B;IAClD,MAAM,eAAqB,6ZAAA,EAAY,eAAe,eAAe,GAAG;QAAC,eAAe;KAAC;IACzF,MAAM,6ZAAmB,cAAA,EAAY,cAAc,eAAe,GAAG;QAAC,eAAe;KAAC;IAEtF,CAAA,KAAA,gBAAgB,SAAA,KAAhB,OAAA,KAAA,IAAA,GAA2B,MAAA,gTAAO,oBAAA,EAAkB,WAAW;QAAE;IAAwB,CAAC;IAE1F,OAAO,eACL;QACE,GAAG,WAAA;QACH;QACA;IACF,GACA;QACE,yBACE,2BAAA,OAAA,0BAAA,CAA2B,KAAA,gBAAgB,oBAAA,KAAhB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,iBAAuC;IACtE;AAEJ;AA4BO,SAAS,eACd,UAAA,EACA,EAAE,0BAA0B,IAAA,CAAK,CAAA,GAA2B,CAAC,CAAA,EAC9C;IACf,MAAM,EAAE,MAAA,EAAQ,KAAA,EAAO,OAAA,EAAS,GAAA,EAAK,OAAA,EAAS,QAAA,EAAU,cAAA,EAAgB,qBAAA,EAAuB,aAAA,CAAc,CAAA,GAC3G,cAAA,OAAA,aAAc,CAAC;IAEjB,MAAM,gaAAa,cAAA,EACjB,CAAC,WAAmE;QAClE,IAAI,KAAK;YACP,OAAO,IAAI,MAAM;QACnB;QACA,QAAO,yUAAA,EAAyB;YAC9B;YACA;YACA;YACA;YACA;YACA,UAAA,CAAY,iBAAA,OAAA,KAAA,IAAA,cAA0C,GAAA,KAAkB;YACxE,OAAA,CAAS,iBAAA,OAAA,KAAA,IAAA,cAA0C,GAAA,KAAkB;QACvE,CAAC,EAAE,MAAM;IACX,GACA;QAAC;QAAK;QAAQ;QAAO;QAAS;QAAgB,qBAAqB;KAAA;IAGrE,MAAM,UAAU,kUAAA,EAAiB;QAC/B,YAAY;YACV,GAAG,UAAA;YACH;YACA;YACA,KAAK;QACP;QACA,SAAS;YACP;QACF;IACF,CAAC;IAED,IAAI,CAAC,SAAS;QACZ,OAAO,aAAa,KAAA,CAAM,iBAAiB;IAC7C;IAEA,OAAO;AACT;;AMhLA,SAAS,aACP,QAAA,EACsF;IACtF,MAAM,EAAE,kBAAA,EAAoB,mBAAA,CAAoB,CAAA,kZAAI,UAAA,CAAM,OAAA,CAAQ,IAAM,SAAS,mBAAA,CAAoB,GAAG;QAAC,QAAQ;KAAC;IAElH,8YAAA,CAAA,UAAA,CAAM,SAAA,CAAU,MAAM;QACpB,OAAO;IACT,GAAG,CAAC,CAAC;IAEL,OAAO;QACL;QACA;IACF;AACF;;;ACoBO,IAAM,YAAY,MAAuB;IAnDhD,IAAA;IAoDE,gCAAgC,WAAW;IAE3C,MAAM,kBAAkB,0BAA0B;IAClD,MAAM,SAAS,+TAAA,CAAiB;IAEhC,CAAA,KAAA,gBAAgB,SAAA,KAAhB,OAAA,KAAA,IAAA,GAA2B,MAAA,gTAAOC,oBAAAA,EAAkB,WAAW;IAE/D,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,UAAU;YAAO,QAAQ,KAAA;YAAW,WAAW,KAAA;QAAU;IACpE;IAEA,OAAO;QACL,UAAU;QACV,QAAQ,OAAO,MAAA;QACf,WAAW,gBAAgB,SAAA;IAC7B;AACF;;;ACjBO,IAAM,YAAY,MAAuB;IAnDhD,IAAA;IAoDE,gCAAgC,WAAW;IAE3C,MAAM,kBAAkB,0BAA0B;IAClD,MAAM,oTAASG,oBAAAA,CAAiB;IAEhC,CAAA,KAAA,gBAAgB,SAAA,KAAhB,OAAA,KAAA,IAAA,GAA2B,MAAA,gTAAOC,oBAAAA,EAAkB,WAAW;IAE/D,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,UAAU;YAAO,QAAQ,KAAA;YAAW,WAAW,KAAA;QAAU;IACpE;IAEA,OAAO;QACL,UAAU;QACV,QAAQ,OAAO,MAAA;QACf,WAAW,gBAAgB,SAAA;IAC7B;AACF;;;;;;AI9DO,IAAM,YAAY,CACvB,WACA,yBACG;IACH,MAAM,sBACJ,OAAO,yBAAyB,WAAW,uBAAuB,wBAAA,OAAA,KAAA,IAAA,qBAAsB,SAAA;IAC1F,MAAM,cAAc,uBAAuB,UAAU,WAAA,IAAe,UAAU,IAAA,IAAQ;IACtF,UAAU,WAAA,GAAc;IAExB,MAAM,UAAU,OAAO,yBAAyB,WAAW,KAAA,IAAY;IAEvE,MAAM,MAAM,CAAC,UAA+B;QAC1C,gCAAgC,eAAe,WAAW;QAE1D,MAAM,QAAQ,0BAA0B;QAExC,IAAI,CAAC,MAAM,MAAA,IAAU,CAAA,CAAC,WAAA,OAAA,KAAA,IAAA,QAAS,kBAAA,GAAoB;YACjD,OAAO;QACT;QAEA,OACE,aAAA,kZAAAG,UAAAA,CAAA,aAAA,CAAC,WAAA;YACE,GAAI,KAAA;YACL,WAAW;YACX;QAAA;IAGN;IACA,IAAI,WAAA,GAAc,CAAA,UAAA,EAAa,WAAW,CAAA,CAAA,CAAA;IAC1C,OAAO;AACT;;AFlBO,IAAM,WAAW,CAAC,EAAE,QAAA,EAAU,uBAAA,CAAwB,CAAA,KAAsD;IACjH,gCAAgC,UAAU;IAE1C,MAAM,EAAE,MAAA,CAAO,CAAA,GAAI,QAAQ;QAAE;IAAwB,CAAC;IACtD,IAAI,QAAQ;QACV,OAAO;IACT;IACA,OAAO;AACT;AAEO,IAAM,YAAY,CAAC,EAAE,QAAA,EAAU,uBAAA,CAAwB,CAAA,KAAsD;IAClH,gCAAgC,WAAW;IAE3C,MAAM,EAAE,MAAA,CAAO,CAAA,GAAI,QAAQ;QAAE;IAAwB,CAAC;IACtD,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IACA,OAAO;AACT;AAEO,IAAM,cAAc,CAAC,EAAE,QAAA,CAAS,CAAA,KAAwC;IAC7E,gCAAgC,aAAa;IAE7C,MAAM,kBAAkB,0BAA0B;IAClD,IAAI,CAAC,gBAAgB,MAAA,EAAQ;QAC3B,OAAO;IACT;IACA,OAAO;AACT;AAEO,IAAM,eAAe,CAAC,EAAE,QAAA,CAAS,CAAA,KAAwC;IAC9E,gCAAgC,cAAc;IAE9C,MAAM,kBAAkB,0BAA0B;IAClD,IAAI,gBAAgB,MAAA,KAAW,WAAW;QACxC,OAAO;IACT;IACA,OAAO;AACT;AAEO,IAAM,cAAc,CAAC,EAAE,QAAA,CAAS,CAAA,KAAwC;IAC7E,gCAAgC,aAAa;IAE7C,MAAM,kBAAkB,0BAA0B;IAClD,IAAI,gBAAgB,MAAA,KAAW,SAAS;QACtC,OAAO;IACT;IACA,OAAO;AACT;AAEO,IAAM,gBAAgB,CAAC,EAAE,QAAA,CAAS,CAAA,KAAwC;IAC/E,gCAAgC,eAAe;IAE/C,MAAM,kBAAkB,0BAA0B;IAClD,IAAI,gBAAgB,MAAA,KAAW,YAAY;QACzC,OAAO;IACT;IACA,OAAO;AACT;AA+DO,IAAM,UAAU,CAAC,EAAE,QAAA,EAAU,QAAA,EAAU,uBAAA,EAAyB,GAAG,qBAAqB,CAAA,KAAoB;IACjH,gCAAgC,SAAS;IAEzC,MAAM,EAAE,QAAA,EAAU,GAAA,EAAK,MAAA,CAAO,CAAA,GAAI,QAAQ;QAAE;IAAwB,CAAC;IAKrE,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAKA,MAAM,eAAe,YAAA,OAAA,WAAY;IAEjC,MAAM,aAAa;IAEnB,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAKA,IAAI,OAAO,qBAAqB,SAAA,KAAc,YAAY;QACxD,IAAI,qBAAqB,SAAA,CAAU,GAAG,GAAG;YACvC,OAAO;QACT;QACA,OAAO;IACT;IAEA,IACE,qBAAqB,IAAA,IACrB,qBAAqB,UAAA,IACrB,qBAAqB,OAAA,IACrB,qBAAqB,IAAA,EACrB;QACA,IAAI,IAAI,oBAAoB,GAAG;YAC7B,OAAO;QACT;QACA,OAAO;IACT;IAMA,OAAO;AACT;AAEO,IAAM,mBAAmB,UAAU,CAAC,EAAE,KAAA,EAAO,GAAG,MAAM,CAAA,KAA4C;IACvG,MAAM,EAAE,MAAA,EAAQ,OAAA,CAAQ,CAAA,GAAI;IAE5B,MAAM,sBAAsB,OAAO,gBAAA,GAC/B,OAAO,gBAAA,CAAiB,MAAA,GAAS,IAAA,uFAAA;IAEjC,OAAO,cAAA,IAAkB,OAAO,cAAA,CAAe,MAAA,GAAS;mZAE5DC,UAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,IAAI,YAAY,QAAQ,qBAAqB;YAC3C,KAAK,MAAM,sBAAA,CAAuB;QACpC,OAAO;YACL,KAAK,MAAM,gBAAA,CAAiB,KAAK;QACnC;IACF,GAAG,CAAC,CAAC;IAEL,OAAO;AACT,GAAG,kBAAkB;AAEd,IAAM,mBAAmB,UAAU,CAAC,EAAE,KAAA,EAAO,GAAG,MAAM,CAAA,KAA4C;mZACvGA,UAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,KAAK,MAAM,gBAAA,CAAiB,KAAK;IACnC,GAAG,CAAC,CAAC;IAEL,OAAO;AACT,GAAG,kBAAkB;AAMd,IAAM,wBAAwB,UAAU,CAAC,EAAE,KAAA,CAAM,CAAA,KAAM;mZAC5DA,UAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,CAAA,GAAA,0SAAA,CAAA,aAAA,EAAW,yBAAyB,mDAAmD;QACvF,KAAK,MAAM,qBAAA,CAAsB;IACnC,GAAG,CAAC,CAAC;IAEL,OAAO;AACT,GAAG,uBAAuB;AAMnB,IAAM,gCAAgC,UAAU,CAAC,EAAE,KAAA,CAAM,CAAA,KAAM;mZACpEA,UAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,CAAA,GAAA,0SAAA,CAAA,aAAA,EAAW,iCAAiC,2DAA2D;QACvG,KAAK,MAAM,6BAAA,CAA8B;IAC3C,GAAG,CAAC,CAAC;IAEL,OAAO;AACT,GAAG,+BAA+B;AAM3B,IAAM,+BAA+B,UAAU,CAAC,EAAE,KAAA,CAAM,CAAA,KAAM;mZACnEA,UAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,CAAA,GAAA,0SAAA,CAAA,aAAA,EAAW,gCAAgC,0DAA0D;QACrG,KAAK,MAAM,4BAAA,CAA6B;IAC1C,GAAG,CAAC,CAAC;IAEL,OAAO;AACT,GAAG,8BAA8B;AAE1B,IAAM,mCAAmC,UAC9C,CAAC,EAAE,KAAA,EAAO,GAAG,6BAA6B,CAAA,KAAgD;mZACxFA,UAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,KAAK,MAAM,sBAAA,CAAuB,4BAA4B;IAChE,GAAG,CAAC,CAAC;IAEL,OAAO;AACT,GACA;AAGK,IAAM,yBAAyB,CAAC,EAAE,QAAA,CAAS,CAAA,KAAwC;IACxF,gCAAgC,wBAAwB;IAExD,MAAM,sTAAU,oBAAA,CAAkB;IAClC,OAAO,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAACA,UAAAA,CAAM,QAAA,EAAN;QAAe,KAAK,UAAU,QAAQ,EAAA,GAAK;IAAA,GAAa,QAAS;AAC3E", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/polyfills.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/index.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/components/uiComponents.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/utils/childrenUtils.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/utils/isConstructor.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/utils/useMaxAllowedInstancesGuard.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/utils/useCustomElementPortal.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/utils/useCustomPages.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/utils/componentValidation.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/utils/useCustomMenuItems.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/utils/useWaitForComponentMount.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/components/ClerkHostRenderer.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/components/SignInButton.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/components/SignUpButton.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/components/SignOutButton.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/components/SignInWithMetamaskButton.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/contexts/ClerkProvider.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/contexts/ClerkContextProvider.tsx", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/isomorphicClerk.ts"], "sourcesContent": ["/**\n * Vite does not define `global` by default\n * One workaround is to use the `define` config prop\n * https://vitejs.dev/config/#define\n * We are solving this in the SDK level to reduce setup steps.\n */\nif (typeof window !== 'undefined' && !window.global) {\n  window.global = typeof global === 'undefined' ? window : global;\n}\n\nexport {};\n", "import './polyfills';\n\nimport { setClerkJsLoadingErrorPackageName } from '@clerk/shared/loadClerkJsScript';\n\nimport { setErrorThrowerOptions } from './errors/errorThrower';\n\nexport * from './components';\nexport * from './contexts';\n\nexport * from './hooks';\nexport type { BrowserClerk, ClerkProp, HeadlessBrowserClerk, ClerkProviderProps } from './types';\n\nsetErrorThrowerOptions({ packageName: PACKAGE_NAME });\nsetClerkJsLoadingErrorPackageName(PACKAGE_NAME);\n", "import { logErrorInDevMode } from '@clerk/shared/utils';\nimport type {\n  APIKeysProps,\n  CreateOrganizationProps,\n  GoogleOneTapProps,\n  OrganizationListProps,\n  OrganizationProfileProps,\n  OrganizationSwitcherProps,\n  PricingTableProps,\n  SignInProps,\n  SignUpProps,\n  UserButtonProps,\n  UserProfileProps,\n  WaitlistProps,\n  Without,\n} from '@clerk/types';\nimport type { PropsWithChildren, ReactNode } from 'react';\nimport React, { createContext, createElement, useContext } from 'react';\n\nimport {\n  organizationProfileLinkRenderedError,\n  organizationProfilePageRenderedError,\n  userButtonMenuActionRenderedError,\n  userButtonMenuItemsRenderedError,\n  userButtonMenuLinkRenderedError,\n  userProfileLinkRenderedError,\n  userProfilePageRenderedError,\n} from '../errors/messages';\nimport type {\n  CustomPortalsRendererProps,\n  MountProps,\n  OrganizationProfileLinkProps,\n  OrganizationProfilePageProps,\n  UserButtonActionProps,\n  UserButtonLinkProps,\n  UserProfileLinkProps,\n  UserProfilePageProps,\n  WithClerkProp,\n} from '../types';\nimport {\n  useOrganizationProfileCustomPages,\n  useSanitizedChildren,\n  useUserButtonCustomMenuItems,\n  useUserProfileCustomPages,\n} from '../utils';\nimport { useWaitForComponentMount } from '../utils/useWaitForComponentMount';\nimport { ClerkHostRenderer } from './ClerkHostRenderer';\nimport { withClerk } from './withClerk';\n\ntype FallbackProp = {\n  /**\n   * An optional element to render while the component is mounting.\n   */\n  fallback?: ReactNode;\n};\n\ntype UserProfileExportType = typeof _UserProfile & {\n  Page: typeof UserProfilePage;\n  Link: typeof UserProfileLink;\n};\n\ntype UserButtonExportType = typeof _UserButton & {\n  UserProfilePage: typeof UserProfilePage;\n  UserProfileLink: typeof UserProfileLink;\n  MenuItems: typeof MenuItems;\n  Action: typeof MenuAction;\n  Link: typeof MenuLink;\n  /**\n   * The `<Outlet />` component can be used in conjunction with `asProvider` in order to control rendering\n   * of the `<UserButton />` without affecting its configuration or any custom pages that could be mounted\n   * @experimental This API is experimental and may change at any moment.\n   */\n  __experimental_Outlet: typeof UserButtonOutlet;\n};\n\ntype UserButtonPropsWithoutCustomPages = Without<\n  UserButtonProps,\n  'userProfileProps' | '__experimental_asStandalone'\n> & {\n  userProfileProps?: Pick<UserProfileProps, 'additionalOAuthScopes' | 'appearance'>;\n  /**\n   * Adding `asProvider` will defer rendering until the `<Outlet />` component is mounted.\n   * This API is experimental and may change at any moment.\n   * @experimental\n   * @default undefined\n   */\n  __experimental_asProvider?: boolean;\n};\n\ntype OrganizationProfileExportType = typeof _OrganizationProfile & {\n  Page: typeof OrganizationProfilePage;\n  Link: typeof OrganizationProfileLink;\n};\n\ntype OrganizationSwitcherExportType = typeof _OrganizationSwitcher & {\n  OrganizationProfilePage: typeof OrganizationProfilePage;\n  OrganizationProfileLink: typeof OrganizationProfileLink;\n  /**\n   * The `<Outlet />` component can be used in conjunction with `asProvider` in order to control rendering\n   * of the `<OrganizationSwitcher />` without affecting its configuration or any custom pages that could be mounted\n   * @experimental This API is experimental and may change at any moment.\n   */\n  __experimental_Outlet: typeof OrganizationSwitcherOutlet;\n};\n\ntype OrganizationSwitcherPropsWithoutCustomPages = Without<\n  OrganizationSwitcherProps,\n  'organizationProfileProps' | '__experimental_asStandalone'\n> & {\n  organizationProfileProps?: Pick<OrganizationProfileProps, 'appearance'>;\n  /**\n   * Adding `asProvider` will defer rendering until the `<Outlet />` component is mounted.\n   * This API is experimental and may change at any moment.\n   * @experimental\n   * @default undefined\n   */\n  __experimental_asProvider?: boolean;\n};\n\nconst CustomPortalsRenderer = (props: CustomPortalsRendererProps) => {\n  return (\n    <>\n      {props?.customPagesPortals?.map((portal, index) => createElement(portal, { key: index }))}\n      {props?.customMenuItemsPortals?.map((portal, index) => createElement(portal, { key: index }))}\n    </>\n  );\n};\n\nexport const SignIn = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<SignInProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountSignIn}\n            unmount={clerk.unmountSignIn}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'SignIn', renderWhileLoading: true },\n);\n\nexport const SignUp = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<SignUpProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountSignUp}\n            unmount={clerk.unmountSignUp}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'SignUp', renderWhileLoading: true },\n);\n\nexport function UserProfilePage({ children }: PropsWithChildren<UserProfilePageProps>) {\n  logErrorInDevMode(userProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function UserProfileLink({ children }: PropsWithChildren<UserProfileLinkProps>) {\n  logErrorInDevMode(userProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _UserProfile = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<Without<UserProfileProps, 'customPages'>> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        <ClerkHostRenderer\n          component={component}\n          mount={clerk.mountUserProfile}\n          unmount={clerk.unmountUserProfile}\n          updateProps={(clerk as any).__unstable__updateProps}\n          props={{ ...props, customPages }}\n          rootProps={rendererRootProps}\n        >\n          <CustomPortalsRenderer customPagesPortals={customPagesPortals} />\n        </ClerkHostRenderer>\n      </>\n    );\n  },\n  { component: 'UserProfile', renderWhileLoading: true },\n);\n\nexport const UserProfile: UserProfileExportType = Object.assign(_UserProfile, {\n  Page: UserProfilePage,\n  Link: UserProfileLink,\n});\n\nconst UserButtonContext = createContext<MountProps>({\n  mount: () => {},\n  unmount: () => {},\n  updateProps: () => {},\n});\n\nconst _UserButton = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<UserButtonPropsWithoutCustomPages> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider,\n    });\n    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });\n    const { customMenuItems, customMenuItemsPortals } = useUserButtonCustomMenuItems(props.children);\n    const sanitizedChildren = useSanitizedChildren(props.children);\n\n    const passableProps = {\n      mount: clerk.mountUserButton,\n      unmount: clerk.unmountUserButton,\n      updateProps: (clerk as any).__unstable__updateProps,\n      props: { ...props, userProfileProps, customMenuItems },\n    };\n    const portalProps = {\n      customPagesPortals: customPagesPortals,\n      customMenuItemsPortals: customMenuItemsPortals,\n    };\n\n    return (\n      <UserButtonContext.Provider value={passableProps}>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            {...passableProps}\n            hideRootHtmlElement={!!props.__experimental_asProvider}\n            rootProps={rendererRootProps}\n          >\n            {/*This mimics the previous behaviour before asProvider existed*/}\n            {props.__experimental_asProvider ? sanitizedChildren : null}\n            <CustomPortalsRenderer {...portalProps} />\n          </ClerkHostRenderer>\n        )}\n      </UserButtonContext.Provider>\n    );\n  },\n  { component: 'UserButton', renderWhileLoading: true },\n);\n\nexport function MenuItems({ children }: PropsWithChildren) {\n  logErrorInDevMode(userButtonMenuItemsRenderedError);\n  return <>{children}</>;\n}\n\nexport function MenuAction({ children }: PropsWithChildren<UserButtonActionProps>) {\n  logErrorInDevMode(userButtonMenuActionRenderedError);\n  return <>{children}</>;\n}\n\nexport function MenuLink({ children }: PropsWithChildren<UserButtonLinkProps>) {\n  logErrorInDevMode(userButtonMenuLinkRenderedError);\n  return <>{children}</>;\n}\n\nexport function UserButtonOutlet(outletProps: Without<UserButtonProps, 'userProfileProps'>) {\n  const providerProps = useContext(UserButtonContext);\n\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps,\n    },\n  } satisfies MountProps;\n\n  return <ClerkHostRenderer {...portalProps} />;\n}\n\nexport const UserButton: UserButtonExportType = Object.assign(_UserButton, {\n  UserProfilePage,\n  UserProfileLink,\n  MenuItems,\n  Action: MenuAction,\n  Link: MenuLink,\n  __experimental_Outlet: UserButtonOutlet,\n});\n\nexport function OrganizationProfilePage({ children }: PropsWithChildren<OrganizationProfilePageProps>) {\n  logErrorInDevMode(organizationProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function OrganizationProfileLink({ children }: PropsWithChildren<OrganizationProfileLinkProps>) {\n  logErrorInDevMode(organizationProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _OrganizationProfile = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<Without<OrganizationProfileProps, 'customPages'>> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountOrganizationProfile}\n            unmount={clerk.unmountOrganizationProfile}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={{ ...props, customPages }}\n            rootProps={rendererRootProps}\n          >\n            <CustomPortalsRenderer customPagesPortals={customPagesPortals} />\n          </ClerkHostRenderer>\n        )}\n      </>\n    );\n  },\n  { component: 'OrganizationProfile', renderWhileLoading: true },\n);\n\nexport const OrganizationProfile: OrganizationProfileExportType = Object.assign(_OrganizationProfile, {\n  Page: OrganizationProfilePage,\n  Link: OrganizationProfileLink,\n});\n\nexport const CreateOrganization = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<CreateOrganizationProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountCreateOrganization}\n            unmount={clerk.unmountCreateOrganization}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'CreateOrganization', renderWhileLoading: true },\n);\n\nconst OrganizationSwitcherContext = createContext<MountProps>({\n  mount: () => {},\n  unmount: () => {},\n  updateProps: () => {},\n});\n\nconst _OrganizationSwitcher = withClerk(\n  ({\n    clerk,\n    component,\n    fallback,\n    ...props\n  }: WithClerkProp<PropsWithChildren<OrganizationSwitcherPropsWithoutCustomPages> & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children, {\n      allowForAnyChildren: !!props.__experimental_asProvider,\n    });\n    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });\n    const sanitizedChildren = useSanitizedChildren(props.children);\n\n    const passableProps = {\n      mount: clerk.mountOrganizationSwitcher,\n      unmount: clerk.unmountOrganizationSwitcher,\n      updateProps: (clerk as any).__unstable__updateProps,\n      props: { ...props, organizationProfileProps },\n      rootProps: rendererRootProps,\n      component,\n    };\n\n    /**\n     * Prefetch organization list\n     */\n    clerk.__experimental_prefetchOrganizationSwitcher();\n\n    return (\n      <OrganizationSwitcherContext.Provider value={passableProps}>\n        <>\n          {shouldShowFallback && fallback}\n          {clerk.loaded && (\n            <ClerkHostRenderer\n              {...passableProps}\n              hideRootHtmlElement={!!props.__experimental_asProvider}\n            >\n              {/*This mimics the previous behaviour before asProvider existed*/}\n              {props.__experimental_asProvider ? sanitizedChildren : null}\n              <CustomPortalsRenderer customPagesPortals={customPagesPortals} />\n            </ClerkHostRenderer>\n          )}\n        </>\n      </OrganizationSwitcherContext.Provider>\n    );\n  },\n  { component: 'OrganizationSwitcher', renderWhileLoading: true },\n);\n\nexport function OrganizationSwitcherOutlet(\n  outletProps: Without<OrganizationSwitcherProps, 'organizationProfileProps'>,\n) {\n  const providerProps = useContext(OrganizationSwitcherContext);\n\n  const portalProps = {\n    ...providerProps,\n    props: {\n      ...providerProps.props,\n      ...outletProps,\n    },\n  } satisfies MountProps;\n\n  return <ClerkHostRenderer {...portalProps} />;\n}\n\nexport const OrganizationSwitcher: OrganizationSwitcherExportType = Object.assign(_OrganizationSwitcher, {\n  OrganizationProfilePage,\n  OrganizationProfileLink,\n  __experimental_Outlet: OrganizationSwitcherOutlet,\n});\n\nexport const OrganizationList = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<OrganizationListProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountOrganizationList}\n            unmount={clerk.unmountOrganizationList}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'OrganizationList', renderWhileLoading: true },\n);\n\nexport const GoogleOneTap = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<GoogleOneTapProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            open={clerk.openGoogleOneTap}\n            close={clerk.closeGoogleOneTap}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'GoogleOneTap', renderWhileLoading: true },\n);\n\nexport const Waitlist = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<WaitlistProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountWaitlist}\n            unmount={clerk.unmountWaitlist}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'Waitlist', renderWhileLoading: true },\n);\n\nexport const PricingTable = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<PricingTableProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountPricingTable}\n            unmount={clerk.unmountPricingTable}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'PricingTable', renderWhileLoading: true },\n);\n\n/**\n * @experimental\n * This component is in early access and may change in future releases.\n */\nexport const APIKeys = withClerk(\n  ({ clerk, component, fallback, ...props }: WithClerkProp<APIKeysProps & FallbackProp>) => {\n    const mountingStatus = useWaitForComponentMount(component);\n    const shouldShowFallback = mountingStatus === 'rendering' || !clerk.loaded;\n\n    const rendererRootProps = {\n      ...(shouldShowFallback && fallback && { style: { display: 'none' } }),\n    };\n\n    return (\n      <>\n        {shouldShowFallback && fallback}\n        {clerk.loaded && (\n          <ClerkHostRenderer\n            component={component}\n            mount={clerk.mountApiKeys}\n            unmount={clerk.unmountApiKeys}\n            updateProps={(clerk as any).__unstable__updateProps}\n            props={props}\n            rootProps={rendererRootProps}\n          />\n        )}\n      </>\n    );\n  },\n  { component: 'ApiKeys', renderWhileLoading: true },\n);\n", "import React from 'react';\n\nimport { errorThrower } from '../errors/errorThrower';\nimport { multipleChildrenInButtonComponent } from '../errors/messages';\n\nexport const assertSingleChild =\n  (children: React.ReactNode) =>\n  (name: 'SignInButton' | 'SignUpButton' | 'SignOutButton' | 'SignInWithMetamaskButton') => {\n    try {\n      return React.Children.only(children);\n    } catch {\n      return errorThrower.throw(multipleChildrenInButtonComponent(name));\n    }\n  };\n\nexport const normalizeWithDefaultValue = (children: React.ReactNode | undefined, defaultText: string) => {\n  if (!children) {\n    children = defaultText;\n  }\n  if (typeof children === 'string') {\n    children = <button>{children}</button>;\n  }\n  return children;\n};\n\nexport const safeExecute =\n  (cb: unknown) =>\n  (...args: any) => {\n    if (cb && typeof cb === 'function') {\n      return cb(...args);\n    }\n  };\n", "export function isConstructor<T>(f: any): f is T {\n  return typeof f === 'function';\n}\n", "import React from 'react';\n\nimport { errorThrower } from '../errors/errorThrower';\n\nconst counts = new Map<string, number>();\n\nexport function useMaxAllowedInstancesGuard(name: string, error: string, maxCount = 1): void {\n  React.useEffect(() => {\n    const count = counts.get(name) || 0;\n    if (count == maxCount) {\n      return errorThrower.throw(error);\n    }\n    counts.set(name, count + 1);\n\n    return () => {\n      counts.set(name, (counts.get(name) || 1) - 1);\n    };\n  }, []);\n}\n\nexport function withMaxAllowedInstancesGuard<P>(\n  WrappedComponent: React.ComponentType<P>,\n  name: string,\n  error: string,\n): React.ComponentType<P> {\n  const displayName = WrappedComponent.displayName || WrappedComponent.name || name || 'Component';\n  const Hoc = (props: P) => {\n    useMaxAllowedInstancesGuard(name, error);\n    return <WrappedComponent {...(props as any)} />;\n  };\n  Hoc.displayName = `withMaxAllowedInstancesGuard(${displayName})`;\n  return Hoc;\n}\n", "import React, { useState } from 'react';\nimport { createPortal } from 'react-dom';\n\nexport type UseCustomElementPortalParams = {\n  component: React.ReactNode;\n  id: number;\n};\n\nexport type UseCustomElementPortalReturn = {\n  portal: () => React.JSX.Element;\n  mount: (node: Element) => void;\n  unmount: () => void;\n  id: number;\n};\n\n// This function takes a component as prop, and returns functions that mount and unmount\n// the given component into a given node\nexport const useCustomElementPortal = (elements: UseCustomElementPortalParams[]) => {\n  const initialState = Array(elements.length).fill(null);\n  const [nodes, setNodes] = useState<(Element | null)[]>(initialState);\n\n  return elements.map((el, index) => ({\n    id: el.id,\n    mount: (node: Element) => setNodes(prevState => prevState.map((n, i) => (i === index ? node : n))),\n    unmount: () => setNodes(prevState => prevState.map((n, i) => (i === index ? null : n))),\n    portal: () => <>{nodes[index] ? createPortal(el.component, nodes[index]) : null}</>,\n  }));\n};\n", "import { logErrorInDevMode } from '@clerk/shared/utils';\nimport type { CustomPage } from '@clerk/types';\nimport type { ReactElement } from 'react';\nimport React from 'react';\n\nimport {\n  MenuItems,\n  OrganizationProfileLink,\n  OrganizationProfilePage,\n  UserProfileLink,\n  UserProfilePage,\n} from '../components/uiComponents';\nimport { customLinkWrongProps, customPagesIgnoredComponent, customPageWrongProps } from '../errors/messages';\nimport type { UserProfilePageProps } from '../types';\nimport { isThatComponent } from './componentValidation';\nimport type { UseCustomElementPortalParams, UseCustomElementPortalReturn } from './useCustomElementPortal';\nimport { useCustomElementPortal } from './useCustomElementPortal';\n\nexport const useUserProfileCustomPages = (\n  children: React.ReactNode | React.ReactNode[],\n  options?: UseCustomPagesOptions,\n) => {\n  const reorderItemsLabels = ['account', 'security'];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: UserProfileLink,\n      PageComponent: UserProfilePage,\n      MenuItemsComponent: MenuItems,\n      componentName: 'UserProfile',\n    },\n    options,\n  );\n};\n\nexport const useOrganizationProfileCustomPages = (\n  children: React.ReactNode | React.ReactNode[],\n  options?: UseCustomPagesOptions,\n) => {\n  const reorderItemsLabels = ['general', 'members'];\n  return useCustomPages(\n    {\n      children,\n      reorderItemsLabels,\n      LinkComponent: OrganizationProfileLink,\n      PageComponent: OrganizationProfilePage,\n      componentName: 'OrganizationProfile',\n    },\n    options,\n  );\n};\n\ntype UseCustomPagesParams = {\n  children: React.ReactNode | React.ReactNode[];\n  LinkComponent: any;\n  PageComponent: any;\n  MenuItemsComponent?: any;\n  reorderItemsLabels: string[];\n  componentName: string;\n};\n\ntype UseCustomPagesOptions = {\n  allowForAnyChildren: boolean;\n};\n\ntype CustomPageWithIdType = UserProfilePageProps & { children?: React.ReactNode };\n\n/**\n * Exclude any children that is used for identifying Custom Pages or Custom Items.\n * Passing:\n * ```tsx\n *  <UserProfile.Page/>\n *  <OrganizationProfile.Link/>\n *  <MyComponent>\n *  <UserButton.MenuItems/>\n * ```\n * Gives back\n * ```tsx\n * <MyComponent>\n * ````\n */\nexport const useSanitizedChildren = (children: React.ReactNode) => {\n  const sanitizedChildren: React.ReactNode[] = [];\n\n  const excludedComponents: any[] = [\n    OrganizationProfileLink,\n    OrganizationProfilePage,\n    MenuItems,\n    UserProfilePage,\n    UserProfileLink,\n  ];\n\n  React.Children.forEach(children, child => {\n    if (!excludedComponents.some(component => isThatComponent(child, component))) {\n      sanitizedChildren.push(child);\n    }\n  });\n\n  return sanitizedChildren;\n};\n\nconst useCustomPages = (params: UseCustomPagesParams, options?: UseCustomPagesOptions) => {\n  const { children, LinkComponent, PageComponent, MenuItemsComponent, reorderItemsLabels, componentName } = params;\n  const { allowForAnyChildren = false } = options || {};\n  const validChildren: CustomPageWithIdType[] = [];\n\n  React.Children.forEach(children, child => {\n    if (\n      !isThatComponent(child, PageComponent) &&\n      !isThatComponent(child, LinkComponent) &&\n      !isThatComponent(child, MenuItemsComponent)\n    ) {\n      if (child && !allowForAnyChildren) {\n        logErrorInDevMode(customPagesIgnoredComponent(componentName));\n      }\n      return;\n    }\n\n    const { props } = child as ReactElement;\n\n    const { children, label, url, labelIcon } = props;\n\n    if (isThatComponent(child, PageComponent)) {\n      if (isReorderItem(props, reorderItemsLabels)) {\n        // This is a reordering item\n        validChildren.push({ label });\n      } else if (isCustomPage(props)) {\n        // this is a custom page\n        validChildren.push({ label, labelIcon, children, url });\n      } else {\n        logErrorInDevMode(customPageWrongProps(componentName));\n        return;\n      }\n    }\n\n    if (isThatComponent(child, LinkComponent)) {\n      if (isExternalLink(props)) {\n        // This is an external link\n        validChildren.push({ label, labelIcon, url });\n      } else {\n        logErrorInDevMode(customLinkWrongProps(componentName));\n        return;\n      }\n    }\n  });\n\n  const customPageContents: UseCustomElementPortalParams[] = [];\n  const customPageLabelIcons: UseCustomElementPortalParams[] = [];\n  const customLinkLabelIcons: UseCustomElementPortalParams[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isCustomPage(cp)) {\n      customPageContents.push({ component: cp.children, id: index });\n      customPageLabelIcons.push({ component: cp.labelIcon, id: index });\n      return;\n    }\n    if (isExternalLink(cp)) {\n      customLinkLabelIcons.push({ component: cp.labelIcon, id: index });\n    }\n  });\n\n  const customPageContentsPortals = useCustomElementPortal(customPageContents);\n  const customPageLabelIconsPortals = useCustomElementPortal(customPageLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n\n  const customPages: CustomPage[] = [];\n  const customPagesPortals: React.ComponentType[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isReorderItem(cp, reorderItemsLabels)) {\n      customPages.push({ label: cp.label });\n      return;\n    }\n    if (isCustomPage(cp)) {\n      const {\n        portal: contentPortal,\n        mount,\n        unmount,\n      } = customPageContentsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customPageLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mount, unmount, mountIcon, unmountIcon });\n      customPagesPortals.push(contentPortal);\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n    if (isExternalLink(cp)) {\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customLinkLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mountIcon, unmountIcon });\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n  });\n\n  return { customPages, customPagesPortals };\n};\n\nconst isReorderItem = (childProps: any, validItems: string[]): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !url && !labelIcon && validItems.some(v => v === label);\n};\n\nconst isCustomPage = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !!children && !!url && !!labelIcon && !!label;\n};\n\nconst isExternalLink = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !!url && !!labelIcon && !!label;\n};\n", "import React from 'react';\n\nexport const isThatComponent = (v: any, component: React.ReactNode): v is React.ReactNode => {\n  return !!v && React.isValidElement(v) && (v as React.ReactElement)?.type === component;\n};\n", "import { logErrorInDevMode } from '@clerk/shared/utils';\nimport type { CustomMenuItem } from '@clerk/types';\nimport type { ReactElement } from 'react';\nimport React from 'react';\n\nimport { MenuAction, MenuItems, MenuLink, UserProfileLink, UserProfilePage } from '../components/uiComponents';\nimport {\n  customMenuItemsIgnoredComponent,\n  userButtonIgnoredComponent,\n  userButtonMenuItemLinkWrongProps,\n  userButtonMenuItemsActionWrongsProps,\n} from '../errors/messages';\nimport type { UserButtonActionProps, UserButtonLinkProps } from '../types';\nimport { isThatComponent } from './componentValidation';\nimport type { UseCustomElementPortalParams, UseCustomElementPortalReturn } from './useCustomElementPortal';\nimport { useCustomElementPortal } from './useCustomElementPortal';\n\nexport const useUserButtonCustomMenuItems = (children: React.ReactNode | React.ReactNode[]) => {\n  const reorderItemsLabels = ['manageAccount', 'signOut'];\n  return useCustomMenuItems({\n    children,\n    reorderItemsLabels,\n    MenuItemsComponent: MenuItems,\n    MenuActionComponent: MenuAction,\n    MenuLinkComponent: MenuLink,\n    UserProfileLinkComponent: UserProfileLink,\n    UserProfilePageComponent: UserProfilePage,\n  });\n};\n\ntype UseCustomMenuItemsParams = {\n  children: React.ReactNode | React.ReactNode[];\n  MenuItemsComponent?: any;\n  MenuActionComponent?: any;\n  MenuLinkComponent?: any;\n  UserProfileLinkComponent?: any;\n  UserProfilePageComponent?: any;\n  reorderItemsLabels: string[];\n};\n\ntype CustomMenuItemType = UserButtonActionProps | UserButtonLinkProps;\n\nconst useCustomMenuItems = ({\n  children,\n  MenuItemsComponent,\n  MenuActionComponent,\n  MenuLinkComponent,\n  UserProfileLinkComponent,\n  UserProfilePageComponent,\n  reorderItemsLabels,\n}: UseCustomMenuItemsParams) => {\n  const validChildren: CustomMenuItemType[] = [];\n  const customMenuItems: CustomMenuItem[] = [];\n  const customMenuItemsPortals: React.ComponentType[] = [];\n\n  React.Children.forEach(children, child => {\n    if (\n      !isThatComponent(child, MenuItemsComponent) &&\n      !isThatComponent(child, UserProfileLinkComponent) &&\n      !isThatComponent(child, UserProfilePageComponent)\n    ) {\n      if (child) {\n        logErrorInDevMode(userButtonIgnoredComponent);\n      }\n      return;\n    }\n\n    // Ignore UserProfileLinkComponent and UserProfilePageComponent\n    if (isThatComponent(child, UserProfileLinkComponent) || isThatComponent(child, UserProfilePageComponent)) {\n      return;\n    }\n\n    // Menu items children\n    const { props } = child as ReactElement;\n\n    React.Children.forEach(props.children, child => {\n      if (!isThatComponent(child, MenuActionComponent) && !isThatComponent(child, MenuLinkComponent)) {\n        if (child) {\n          logErrorInDevMode(customMenuItemsIgnoredComponent);\n        }\n\n        return;\n      }\n\n      const { props } = child as ReactElement;\n\n      const { label, labelIcon, href, onClick, open } = props;\n\n      if (isThatComponent(child, MenuActionComponent)) {\n        if (isReorderItem(props, reorderItemsLabels)) {\n          // This is a reordering item\n          validChildren.push({ label });\n        } else if (isCustomMenuItem(props)) {\n          const baseItem = {\n            label,\n            labelIcon,\n          };\n\n          if (onClick !== undefined) {\n            validChildren.push({\n              ...baseItem,\n              onClick,\n            });\n          } else if (open !== undefined) {\n            validChildren.push({\n              ...baseItem,\n              open: open.startsWith('/') ? open : `/${open}`,\n            });\n          } else {\n            // Handle the case where neither onClick nor open is defined\n            logErrorInDevMode('Custom menu item must have either onClick or open property');\n            return;\n          }\n        } else {\n          logErrorInDevMode(userButtonMenuItemsActionWrongsProps);\n          return;\n        }\n      }\n\n      if (isThatComponent(child, MenuLinkComponent)) {\n        if (isExternalLink(props)) {\n          validChildren.push({ label, labelIcon, href });\n        } else {\n          logErrorInDevMode(userButtonMenuItemLinkWrongProps);\n          return;\n        }\n      }\n    });\n  });\n\n  const customMenuItemLabelIcons: UseCustomElementPortalParams[] = [];\n  const customLinkLabelIcons: UseCustomElementPortalParams[] = [];\n  validChildren.forEach((mi, index) => {\n    if (isCustomMenuItem(mi)) {\n      customMenuItemLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n    if (isExternalLink(mi)) {\n      customLinkLabelIcons.push({ component: mi.labelIcon, id: index });\n    }\n  });\n\n  const customMenuItemLabelIconsPortals = useCustomElementPortal(customMenuItemLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n\n  validChildren.forEach((mi, index) => {\n    if (isReorderItem(mi, reorderItemsLabels)) {\n      customMenuItems.push({\n        label: mi.label,\n      });\n    }\n    if (isCustomMenuItem(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customMenuItemLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      const menuItem: CustomMenuItem = {\n        label: mi.label,\n        mountIcon,\n        unmountIcon,\n      };\n\n      if ('onClick' in mi) {\n        menuItem.onClick = mi.onClick;\n      } else if ('open' in mi) {\n        menuItem.open = mi.open;\n      }\n      customMenuItems.push(menuItem);\n      customMenuItemsPortals.push(iconPortal);\n    }\n    if (isExternalLink(mi)) {\n      const {\n        portal: iconPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customLinkLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customMenuItems.push({\n        label: mi.label,\n        href: mi.href,\n        mountIcon,\n        unmountIcon,\n      });\n      customMenuItemsPortals.push(iconPortal);\n    }\n  });\n\n  return { customMenuItems, customMenuItemsPortals };\n};\n\nconst isReorderItem = (childProps: any, validItems: string[]): boolean => {\n  const { children, label, onClick, labelIcon } = childProps;\n  return !children && !onClick && !labelIcon && validItems.some(v => v === label);\n};\n\nconst isCustomMenuItem = (childProps: any): childProps is UserButtonActionProps => {\n  const { label, labelIcon, onClick, open } = childProps;\n  return !!labelIcon && !!label && (typeof onClick === 'function' || typeof open === 'string');\n};\n\nconst isExternalLink = (childProps: any): childProps is UserButtonLinkProps => {\n  const { label, href, labelIcon } = childProps;\n  return !!href && !!labelIcon && !!label;\n};\n", "import { useEffect, useRef, useState } from 'react';\n\n/**\n * Used to detect when a Clerk component has been added to the DOM.\n */\nfunction waitForElementChildren(options: { selector?: string; root?: HTMLElement | null; timeout?: number }) {\n  const { root = document?.body, selector, timeout = 0 } = options;\n\n  return new Promise<void>((resolve, reject) => {\n    if (!root) {\n      reject(new Error('No root element provided'));\n      return;\n    }\n\n    let elementToWatch: HTMLElement | null = root;\n    if (selector) {\n      elementToWatch = root?.querySelector(selector);\n    }\n\n    // Check if the element already has child nodes\n    const isElementAlreadyPresent = elementToWatch?.childElementCount && elementToWatch.childElementCount > 0;\n    if (isElementAlreadyPresent) {\n      resolve();\n      return;\n    }\n\n    // Set up a MutationObserver to detect when the element has children\n    const observer = new MutationObserver(mutationsList => {\n      for (const mutation of mutationsList) {\n        if (mutation.type === 'childList') {\n          if (!elementToWatch && selector) {\n            elementToWatch = root?.querySelector(selector);\n          }\n\n          if (elementToWatch?.childElementCount && elementToWatch.childElementCount > 0) {\n            observer.disconnect();\n            resolve();\n            return;\n          }\n        }\n      }\n    });\n\n    observer.observe(root, { childList: true, subtree: true });\n\n    // Set up an optional timeout to reject the promise if the element never gets child nodes\n    if (timeout > 0) {\n      setTimeout(() => {\n        observer.disconnect();\n        reject(new Error(`Timeout waiting for element children`));\n      }, timeout);\n    }\n  });\n}\n\n/**\n * Detect when a Clerk component has mounted by watching DOM updates to an element with a `data-clerk-component=\"${component}\"` property.\n */\nexport function useWaitForComponentMount(component?: string) {\n  const watcherRef = useRef<Promise<void>>();\n  const [status, setStatus] = useState<'rendering' | 'rendered' | 'error'>('rendering');\n\n  useEffect(() => {\n    if (!component) {\n      throw new Error('Clerk: no component name provided, unable to detect mount.');\n    }\n\n    if (typeof window !== 'undefined' && !watcherRef.current) {\n      watcherRef.current = waitForElementChildren({ selector: `[data-clerk-component=\"${component}\"]` })\n        .then(() => {\n          setStatus('rendered');\n        })\n        .catch(() => {\n          setStatus('error');\n        });\n    }\n  }, [component]);\n\n  return status;\n}\n", "import { without } from '@clerk/shared/object';\nimport { isDeeplyEqual } from '@clerk/shared/react';\nimport type { PropsWithChildren } from 'react';\nimport React from 'react';\n\nimport type { MountProps, OpenProps } from '../types';\n\nconst isMountProps = (props: any): props is MountProps => {\n  return 'mount' in props;\n};\n\nconst isOpenProps = (props: any): props is OpenProps => {\n  return 'open' in props;\n};\n\nconst stripMenuItemIconHandlers = (\n  menuItems?: Array<{\n    mountIcon?: (el: HTMLDivElement) => void;\n    unmountIcon?: (el: HTMLDivElement) => void;\n    [key: string]: any;\n  }>,\n) => {\n  return menuItems?.map(({ mountIcon, unmountIcon, ...rest }) => rest);\n};\n\n// README: <ClerkHostRenderer/> should be a class pure component in order for mount and unmount\n// lifecycle props to be invoked correctly. Replacing the class component with a\n// functional component wrapped with a React.memo is not identical to the original\n// class implementation due to React intricacies such as the useEffect’s cleanup\n// seems to run AFTER unmount, while componentWillUnmount runs BEFORE.\n\n// More information can be found at https://clerk.slack.com/archives/C015S0BGH8R/p1624891993016300\n\n// The function Portal implementation is commented out for future reference.\n\n// const Portal = React.memo(({ props, mount, unmount }: MountProps) => {\n//   const portalRef = React.createRef<HTMLDivElement>();\n\n//   useEffect(() => {\n//     if (portalRef.current) {\n//       mount(portalRef.current, props);\n//     }\n//     return () => {\n//       if (portalRef.current) {\n//         unmount(portalRef.current);\n//       }\n//     };\n//   }, []);\n\n//   return <div ref={portalRef} />;\n// });\n\n// Portal.displayName = 'ClerkPortal';\n\n/**\n * Used to orchestrate mounting of Clerk components in a host React application.\n * Components are rendered into a specific DOM node using mount/unmount methods provided by the Clerk class.\n */\nexport class ClerkHostRenderer extends React.PureComponent<\n  PropsWithChildren<\n    (MountProps | OpenProps) & {\n      component?: string;\n      hideRootHtmlElement?: boolean;\n      rootProps?: JSX.IntrinsicElements['div'];\n    }\n  >\n> {\n  private rootRef = React.createRef<HTMLDivElement>();\n\n  componentDidUpdate(_prevProps: Readonly<MountProps | OpenProps>) {\n    if (!isMountProps(_prevProps) || !isMountProps(this.props)) {\n      return;\n    }\n\n    // Remove children and customPages from props before comparing\n    // children might hold circular references which deepEqual can't handle\n    // and the implementation of customPages relies on props getting new references\n    const prevProps = without(_prevProps.props, 'customPages', 'customMenuItems', 'children');\n    const newProps = without(this.props.props, 'customPages', 'customMenuItems', 'children');\n\n    // instead, we simply use the length of customPages to determine if it changed or not\n    const customPagesChanged = prevProps.customPages?.length !== newProps.customPages?.length;\n    const customMenuItemsChanged = prevProps.customMenuItems?.length !== newProps.customMenuItems?.length;\n\n    // Strip out mountIcon and unmountIcon handlers since they're always generated as new function references,\n    // which would cause unnecessary re-renders in deep equality checks\n    const prevMenuItemsWithoutHandlers = stripMenuItemIconHandlers(_prevProps.props.customMenuItems);\n    const newMenuItemsWithoutHandlers = stripMenuItemIconHandlers(this.props.props.customMenuItems);\n\n    if (\n      !isDeeplyEqual(prevProps, newProps) ||\n      !isDeeplyEqual(prevMenuItemsWithoutHandlers, newMenuItemsWithoutHandlers) ||\n      customPagesChanged ||\n      customMenuItemsChanged\n    ) {\n      if (this.rootRef.current) {\n        this.props.updateProps({ node: this.rootRef.current, props: this.props.props });\n      }\n    }\n  }\n\n  componentDidMount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.mount(this.rootRef.current, this.props.props);\n      }\n\n      if (isOpenProps(this.props)) {\n        this.props.open(this.props.props);\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.rootRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.unmount(this.rootRef.current);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.close();\n      }\n    }\n  }\n\n  render() {\n    const { hideRootHtmlElement = false } = this.props;\n    const rootAttributes = {\n      ref: this.rootRef,\n      ...this.props.rootProps,\n      ...(this.props.component && { 'data-clerk-component': this.props.component }),\n    };\n\n    return (\n      <>\n        {!hideRootHtmlElement && <div {...rootAttributes} />}\n        {this.props.children}\n      </>\n    );\n  }\n}\n", "import type { SignInButtonProps, SignInProps } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<React.PropsWithChildren<SignInButtonProps>>) => {\n    const {\n      signUpFallbackRedirectUrl,\n      forceRedirectUrl,\n      fallbackRedirectUrl,\n      signUpForceRedirectUrl,\n      mode,\n      initialValues,\n      withSignUp,\n      oauthFlow,\n      ...rest\n    } = props;\n    children = normalizeWithDefaultValue(children, 'Sign in');\n    const child = assertSingleChild(children)('SignInButton');\n\n    const clickHandler = () => {\n      const opts: SignInProps = {\n        forceRedirectUrl,\n        fallbackRedirectUrl,\n        signUpFallbackRedirectUrl,\n        signUpForceRedirectUrl,\n        initialValues,\n        withSignUp,\n        oauthFlow,\n      };\n\n      if (mode === 'modal') {\n        return clerk.openSignIn({ ...opts, appearance: props.appearance });\n      }\n      return clerk.redirectToSignIn({\n        ...opts,\n        signInFallbackRedirectUrl: fallbackRedirectUrl,\n        signInForceRedirectUrl: forceRedirectUrl,\n      });\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      if (child && typeof child === 'object' && 'props' in child) {\n        await safeExecute(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignInButton', renderWhileLoading: true },\n);\n", "import type { SignUpButtonProps, SignUpProps } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignUpButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<React.PropsWithChildren<SignUpButtonProps>>) => {\n    const {\n      fallbackRedirectUrl,\n      forceRedirectUrl,\n      signInFallbackRedirectUrl,\n      signInForceRedirectUrl,\n      mode,\n      unsafeMetadata,\n      initialValues,\n      oauthFlow,\n      ...rest\n    } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign up');\n    const child = assertSingleChild(children)('SignUpButton');\n\n    const clickHandler = () => {\n      const opts: SignUpProps = {\n        fallbackRedirectUrl,\n        forceRedirectUrl,\n        signInFallbackRedirectUrl,\n        signInForceRedirectUrl,\n        unsafeMetadata,\n        initialValues,\n        oauthFlow,\n      };\n\n      if (mode === 'modal') {\n        return clerk.openSignUp({ ...opts, appearance: props.appearance });\n      }\n\n      return clerk.redirectToSignUp({\n        ...opts,\n        signUpFallbackRedirectUrl: fallbackRedirectUrl,\n        signUpForceRedirectUrl: forceRedirectUrl,\n      });\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      if (child && typeof child === 'object' && 'props' in child) {\n        await safeExecute(child.props.onClick)(e);\n      }\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignUpButton', renderWhileLoading: true },\n);\n", "import type { SignOutOptions } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport type SignOutButtonProps = {\n  redirectUrl?: string;\n  signOutOptions?: SignOutOptions;\n  children?: React.ReactNode;\n};\n\nexport const SignOutButton = withClerk(\n  ({ clerk, children, ...props }: React.PropsWithChildren<WithClerkProp<SignOutButtonProps>>) => {\n    const { redirectUrl = '/', signOutOptions, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign out');\n    const child = assertSingleChild(children)('SignOutButton');\n\n    const clickHandler = () => clerk.signOut({ redirectUrl, ...signOutOptions });\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignOutButton', renderWhileLoading: true },\n);\n", "import React from 'react';\n\nimport type { SignInWithMetamaskButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInWithMetamaskButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<SignInWithMetamaskButtonProps>) => {\n    const { redirectUrl, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign in with Metamask');\n    const child = assertSingleChild(children)('SignInWithMetamaskButton');\n\n    // TODO: Properly fix this code\n    // eslint-disable-next-line @typescript-eslint/require-await\n    const clickHandler = async () => {\n      async function authenticate() {\n        await clerk.authenticateWithMetamask({ redirectUrl: redirectUrl || undefined });\n      }\n      void authenticate();\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  { component: 'SignInWithMetamask', renderWhileLoading: true },\n);\n", "import { isPublishable<PERSON><PERSON> } from '@clerk/shared/keys';\nimport React from 'react';\n\nimport { errorThrower } from '../errors/errorThrower';\nimport { multipleClerkProvidersError } from '../errors/messages';\nimport type { ClerkProviderProps } from '../types';\nimport { withMaxAllowedInstancesGuard } from '../utils';\nimport { ClerkContextProvider } from './ClerkContextProvider';\n\nfunction ClerkProviderBase(props: ClerkProviderProps) {\n  const { initialState, children, __internal_bypassMissingPublishableKey, ...restIsomorphicClerkOptions } = props;\n  const { publishableKey = '', Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;\n\n  if (!userInitialisedClerk && !__internal_bypassMissingPublishableKey) {\n    if (!publishableKey) {\n      errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !isPublishableKey(publishableKey)) {\n      errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });\n    }\n  }\n\n  return (\n    <ClerkContextProvider\n      initialState={initialState}\n      isomorphicClerkOptions={restIsomorphicClerkOptions}\n    >\n      {children}\n    </ClerkContextProvider>\n  );\n}\n\nconst ClerkProvider = withMaxAllowedInstancesGuard(ClerkProviderBase, 'ClerkProvider', multipleClerkProvidersError);\n\nClerkProvider.displayName = 'ClerkProvider';\n\nexport { ClerkProvider };\n", "import { deriveState } from '@clerk/shared/deriveState';\nimport { ClientContext, OrganizationProvider, SessionContext, UserContext } from '@clerk/shared/react';\nimport type { ClientResource, InitialState, Resources } from '@clerk/types';\nimport React from 'react';\n\nimport { IsomorphicClerk } from '../isomorphicClerk';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { AuthContext } from './AuthContext';\nimport { IsomorphicClerkContext } from './IsomorphicClerkContext';\n\ntype ClerkContextProvider = {\n  isomorphicClerkOptions: IsomorphicClerkOptions;\n  initialState: InitialState | undefined;\n  children: React.ReactNode;\n};\n\nexport type ClerkContextProviderState = Resources;\n\nexport function ClerkContextProvider(props: ClerkContextProvider) {\n  const { isomorphicClerkOptions, initialState, children } = props;\n  const { isomorphicClerk: clerk, clerkStatus } = useLoadedIsomorphicClerk(isomorphicClerkOptions);\n\n  const [state, setState] = React.useState<ClerkContextProviderState>({\n    client: clerk.client as ClientResource,\n    session: clerk.session,\n    user: clerk.user,\n    organization: clerk.organization,\n  });\n\n  React.useEffect(() => {\n    return clerk.addListener(e => setState({ ...e }));\n  }, []);\n\n  const derivedState = deriveState(clerk.loaded, state, initialState);\n  const clerkCtx = React.useMemo(\n    () => ({ value: clerk }),\n    [\n      // Only update the clerk reference on status change\n      clerkStatus,\n    ],\n  );\n  const clientCtx = React.useMemo(() => ({ value: state.client }), [state.client]);\n\n  const {\n    sessionId,\n    sessionStatus,\n    sessionClaims,\n    session,\n    userId,\n    user,\n    orgId,\n    actor,\n    organization,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n  } = derivedState;\n\n  const authCtx = React.useMemo(() => {\n    const value = {\n      sessionId,\n      sessionStatus,\n      sessionClaims,\n      userId,\n      actor,\n      orgId,\n      orgRole,\n      orgSlug,\n      orgPermissions,\n      factorVerificationAge,\n    };\n    return { value };\n  }, [sessionId, sessionStatus, userId, actor, orgId, orgRole, orgSlug, factorVerificationAge, sessionClaims?.__raw]);\n\n  const sessionCtx = React.useMemo(() => ({ value: session }), [sessionId, session]);\n  const userCtx = React.useMemo(() => ({ value: user }), [userId, user]);\n  const organizationCtx = React.useMemo(() => {\n    const value = {\n      organization: organization,\n    };\n    return { value };\n  }, [orgId, organization]);\n\n  return (\n    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk\n    <IsomorphicClerkContext.Provider value={clerkCtx}>\n      <ClientContext.Provider value={clientCtx}>\n        <SessionContext.Provider value={sessionCtx}>\n          <OrganizationProvider {...organizationCtx.value}>\n            <AuthContext.Provider value={authCtx}>\n              <UserContext.Provider value={userCtx}>{children}</UserContext.Provider>\n            </AuthContext.Provider>\n          </OrganizationProvider>\n        </SessionContext.Provider>\n      </ClientContext.Provider>\n    </IsomorphicClerkContext.Provider>\n  );\n}\n\nconst useLoadedIsomorphicClerk = (options: IsomorphicClerkOptions) => {\n  const isomorphicClerkRef = React.useRef(IsomorphicClerk.getOrCreateInstance(options));\n  const [clerkStatus, setClerkStatus] = React.useState(isomorphicClerkRef.current.status);\n\n  React.useEffect(() => {\n    void isomorphicClerkRef.current.__unstable__updateProps({ appearance: options.appearance });\n  }, [options.appearance]);\n\n  React.useEffect(() => {\n    void isomorphicClerkRef.current.__unstable__updateProps({ options });\n  }, [options.localization]);\n\n  React.useEffect(() => {\n    isomorphicClerkRef.current.on('status', setClerkStatus);\n    return () => {\n      if (isomorphicClerkRef.current) {\n        isomorphicClerkRef.current.off('status', setClerkStatus);\n      }\n      IsomorphicClerk.clearInstance();\n    };\n  }, []);\n\n  return { isomorphicClerk: isomorphicClerkRef.current, clerkStatus };\n};\n", "import { inBrowser } from '@clerk/shared/browser';\nimport { clerkEvents, createClerkEventBus } from '@clerk/shared/clerkEventBus';\nimport { loadClerkJsScript } from '@clerk/shared/loadClerkJsScript';\nimport { handleValueOrFn } from '@clerk/shared/utils';\nimport type {\n  __internal_CheckoutProps,\n  __internal_OAuthConsentProps,\n  __internal_PlanDetailsProps,\n  __internal_UserVerificationModalProps,\n  __internal_UserVerificationProps,\n  APIKeysNamespace,\n  APIKeysProps,\n  AuthenticateWithCoinbaseWalletParams,\n  AuthenticateWithGoogleOneTapParams,\n  AuthenticateWithMetamaskParams,\n  AuthenticateWithOKXWalletParams,\n  Clerk,\n  ClerkAuthenticateWithWeb3Params,\n  ClerkOptions,\n  ClerkStatus,\n  ClientResource,\n  CommerceBillingNamespace,\n  CreateOrganizationParams,\n  CreateOrganizationProps,\n  DomainOrProxyUrl,\n  GoogleOneTapProps,\n  HandleEmailLinkVerificationParams,\n  HandleOAuthCallbackParams,\n  JoinWaitlistParams,\n  ListenerCallback,\n  LoadedClerk,\n  NextTaskParams,\n  OrganizationListProps,\n  OrganizationProfileProps,\n  OrganizationResource,\n  OrganizationSwitcherProps,\n  PricingTableProps,\n  RedirectOptions,\n  SetActiveParams,\n  SignInProps,\n  SignInRedirectOptions,\n  SignInResource,\n  SignUpProps,\n  SignUpRedirectOptions,\n  SignUpResource,\n  UnsubscribeCallback,\n  UserButtonProps,\n  UserProfileProps,\n  WaitlistProps,\n  WaitlistResource,\n  Without,\n} from '@clerk/types';\n\nimport { errorThrower } from './errors/errorThrower';\nimport { unsupportedNonBrowserDomainOrProxyUrlFunction } from './errors/messages';\nimport type {\n  BrowserClerk,\n  BrowserClerkConstructor,\n  ClerkProp,\n  HeadlessBrowserClerk,\n  HeadlessBrowserClerkConstructor,\n  IsomorphicClerkOptions,\n} from './types';\nimport { isConstructor } from './utils';\n\nif (typeof globalThis.__BUILD_DISABLE_RHC__ === 'undefined') {\n  globalThis.__BUILD_DISABLE_RHC__ = false;\n}\n\nconst SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport interface Global {\n  Clerk?: HeadlessBrowserClerk | BrowserClerk;\n}\n\ndeclare const global: Global;\n\ntype GenericFunction<TArgs = never> = (...args: TArgs[]) => unknown;\n\ntype MethodName<T> = {\n  [P in keyof T]: T[P] extends GenericFunction ? P : never;\n}[keyof T];\n\ntype MethodCallback = () => Promise<unknown> | unknown;\n\ntype WithVoidReturn<F extends (...args: any) => any> = (\n  ...args: Parameters<F>\n) => ReturnType<F> extends Promise<infer T> ? Promise<T | void> : ReturnType<F> | void;\ntype WithVoidReturnFunctions<T> = {\n  [K in keyof T]: T[K] extends (...args: any) => any ? WithVoidReturn<T[K]> : T[K];\n};\n\ntype IsomorphicLoadedClerk = Without<\n  WithVoidReturnFunctions<LoadedClerk>,\n  | 'client'\n  | '__internal_addNavigationListener'\n  | '__internal_getCachedResources'\n  | '__internal_reloadInitialResources'\n  | 'billing'\n  | 'apiKeys'\n  | '__internal_setComponentNavigationContext'\n  | '__internal_setActiveInProgress'\n> & {\n  client: ClientResource | undefined;\n  billing: CommerceBillingNamespace | undefined;\n  apiKeys: APIKeysNamespace | undefined;\n};\n\nexport class IsomorphicClerk implements IsomorphicLoadedClerk {\n  private readonly mode: 'browser' | 'server';\n  private readonly options: IsomorphicClerkOptions;\n  private readonly Clerk: ClerkProp;\n  private clerkjs: BrowserClerk | HeadlessBrowserClerk | null = null;\n  private preopenOneTap?: null | GoogleOneTapProps = null;\n  private preopenUserVerification?: null | __internal_UserVerificationProps = null;\n  private preopenSignIn?: null | SignInProps = null;\n  private preopenCheckout?: null | __internal_CheckoutProps = null;\n  private preopenPlanDetails?: null | __internal_PlanDetailsProps = null;\n  private preopenSignUp?: null | SignUpProps = null;\n  private preopenUserProfile?: null | UserProfileProps = null;\n  private preopenOrganizationProfile?: null | OrganizationProfileProps = null;\n  private preopenCreateOrganization?: null | CreateOrganizationProps = null;\n  private preOpenWaitlist?: null | WaitlistProps = null;\n  private premountSignInNodes = new Map<HTMLDivElement, SignInProps | undefined>();\n  private premountSignUpNodes = new Map<HTMLDivElement, SignUpProps | undefined>();\n  private premountUserProfileNodes = new Map<HTMLDivElement, UserProfileProps | undefined>();\n  private premountUserButtonNodes = new Map<HTMLDivElement, UserButtonProps | undefined>();\n  private premountOrganizationProfileNodes = new Map<HTMLDivElement, OrganizationProfileProps | undefined>();\n  private premountCreateOrganizationNodes = new Map<HTMLDivElement, CreateOrganizationProps | undefined>();\n  private premountOrganizationSwitcherNodes = new Map<HTMLDivElement, OrganizationSwitcherProps | undefined>();\n  private premountOrganizationListNodes = new Map<HTMLDivElement, OrganizationListProps | undefined>();\n  private premountMethodCalls = new Map<MethodName<BrowserClerk>, MethodCallback>();\n  private premountWaitlistNodes = new Map<HTMLDivElement, WaitlistProps | undefined>();\n  private premountPricingTableNodes = new Map<HTMLDivElement, PricingTableProps | undefined>();\n  private premountApiKeysNodes = new Map<HTMLDivElement, APIKeysProps | undefined>();\n  private premountOAuthConsentNodes = new Map<HTMLDivElement, __internal_OAuthConsentProps | undefined>();\n  // A separate Map of `addListener` method calls to handle multiple listeners.\n  private premountAddListenerCalls = new Map<\n    ListenerCallback,\n    {\n      unsubscribe: UnsubscribeCallback;\n      nativeUnsubscribe?: UnsubscribeCallback;\n    }\n  >();\n  private loadedListeners: Array<() => void> = [];\n\n  #status: ClerkStatus = 'loading';\n  #domain: DomainOrProxyUrl['domain'];\n  #proxyUrl: DomainOrProxyUrl['proxyUrl'];\n  #publishableKey: string;\n  #eventBus = createClerkEventBus();\n\n  get publishableKey(): string {\n    return this.#publishableKey;\n  }\n\n  get loaded(): boolean {\n    return this.clerkjs?.loaded || false;\n  }\n\n  get status(): ClerkStatus {\n    /**\n     * If clerk-js is not available the returned value can either be \"loading\" or \"error\".\n     */\n    if (!this.clerkjs) {\n      return this.#status;\n    }\n    return (\n      this.clerkjs?.status ||\n      /**\n       * Support older clerk-js versions.\n       * If clerk-js is available but `.status` is missing it we need to fallback to `.loaded`.\n       * Since \"degraded\" an \"error\" did not exist before,\n       * map \"loaded\" to \"ready\" and \"not loaded\" to \"loading\".\n       */\n      (this.clerkjs.loaded ? 'ready' : 'loading')\n    );\n  }\n\n  static #instance: IsomorphicClerk | null | undefined;\n\n  static getOrCreateInstance(options: IsomorphicClerkOptions) {\n    // During SSR: a new instance should be created for every request\n    // During CSR: use the cached instance for the whole lifetime of the app\n    // Also will recreate the instance if the provided Clerk instance changes\n    // This method should be idempotent in both scenarios\n    if (\n      !inBrowser() ||\n      !this.#instance ||\n      (options.Clerk && this.#instance.Clerk !== options.Clerk) ||\n      // Allow hot swapping PKs on the client\n      this.#instance.publishableKey !== options.publishableKey\n    ) {\n      this.#instance = new IsomorphicClerk(options);\n    }\n    return this.#instance;\n  }\n\n  static clearInstance() {\n    this.#instance = null;\n  }\n\n  get domain(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#domain, new URL(window.location.href), '');\n    }\n    if (typeof this.#domain === 'function') {\n      return errorThrower.throw(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#domain || '';\n  }\n\n  get proxyUrl(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use proxy as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#proxyUrl, new URL(window.location.href), '');\n    }\n    if (typeof this.#proxyUrl === 'function') {\n      return errorThrower.throw(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#proxyUrl || '';\n  }\n\n  /**\n   * Accesses private options from the `Clerk` instance and defaults to\n   * `IsomorphicClerk` options when in SSR context.\n   *  @internal\n   */\n  public __internal_getOption<K extends keyof ClerkOptions>(key: K): ClerkOptions[K] | undefined {\n    return this.clerkjs?.__internal_getOption ? this.clerkjs?.__internal_getOption(key) : this.options[key];\n  }\n\n  constructor(options: IsomorphicClerkOptions) {\n    const { Clerk = null, publishableKey } = options || {};\n    this.#publishableKey = publishableKey;\n    this.#proxyUrl = options?.proxyUrl;\n    this.#domain = options?.domain;\n    this.options = options;\n    this.Clerk = Clerk;\n    this.mode = inBrowser() ? 'browser' : 'server';\n\n    if (!this.options.sdkMetadata) {\n      this.options.sdkMetadata = SDK_METADATA;\n    }\n    this.#eventBus.emit(clerkEvents.Status, 'loading');\n    this.#eventBus.prioritizedOn(clerkEvents.Status, status => (this.#status = status));\n\n    if (this.#publishableKey) {\n      void this.loadClerkJS();\n    }\n  }\n\n  get sdkMetadata() {\n    return this.clerkjs?.sdkMetadata || this.options.sdkMetadata || undefined;\n  }\n\n  get instanceType() {\n    return this.clerkjs?.instanceType;\n  }\n\n  get frontendApi() {\n    return this.clerkjs?.frontendApi || '';\n  }\n\n  get isStandardBrowser() {\n    return this.clerkjs?.isStandardBrowser || this.options.standardBrowser || false;\n  }\n\n  get isSatellite() {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.options.isSatellite, new URL(window.location.href), false);\n    }\n    if (typeof this.options.isSatellite === 'function') {\n      return errorThrower.throw(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return false;\n  }\n\n  buildSignInUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignInUrl(opts) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignInUrl', callback);\n    }\n  };\n\n  buildSignUpUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignUpUrl(opts) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignUpUrl', callback);\n    }\n  };\n\n  buildAfterSignInUrl = (...args: Parameters<Clerk['buildAfterSignInUrl']>): string | void => {\n    const callback = () => this.clerkjs?.buildAfterSignInUrl(...args) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterSignInUrl', callback);\n    }\n  };\n\n  buildAfterSignUpUrl = (...args: Parameters<Clerk['buildAfterSignUpUrl']>): string | void => {\n    const callback = () => this.clerkjs?.buildAfterSignUpUrl(...args) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterSignUpUrl', callback);\n    }\n  };\n\n  buildAfterSignOutUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildAfterSignOutUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterSignOutUrl', callback);\n    }\n  };\n\n  buildNewSubscriptionRedirectUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildNewSubscriptionRedirectUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildNewSubscriptionRedirectUrl', callback);\n    }\n  };\n\n  buildAfterMultiSessionSingleSignOutUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildAfterMultiSessionSingleSignOutUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildAfterMultiSessionSingleSignOutUrl', callback);\n    }\n  };\n\n  buildUserProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildUserProfileUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUserProfileUrl', callback);\n    }\n  };\n\n  buildCreateOrganizationUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildCreateOrganizationUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildCreateOrganizationUrl', callback);\n    }\n  };\n\n  buildOrganizationProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildOrganizationProfileUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildOrganizationProfileUrl', callback);\n    }\n  };\n\n  buildWaitlistUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildWaitlistUrl() || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildWaitlistUrl', callback);\n    }\n  };\n\n  buildUrlWithAuth = (to: string): string | void => {\n    const callback = () => this.clerkjs?.buildUrlWithAuth(to) || '';\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUrlWithAuth', callback);\n    }\n  };\n\n  handleUnauthenticated = async () => {\n    const callback = () => this.clerkjs?.handleUnauthenticated();\n    if (this.clerkjs && this.loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('handleUnauthenticated', callback);\n    }\n  };\n\n  #waitForClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk> {\n    return new Promise<HeadlessBrowserClerk | BrowserClerk>(resolve => {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      this.addOnLoaded(() => resolve(this.clerkjs!));\n    });\n  }\n\n  async loadClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk | undefined> {\n    if (this.mode !== 'browser' || this.loaded) {\n      return;\n    }\n\n    // Store frontendAPI value on window as a fallback. This value can be used as a\n    // fallback during ClerkJS hot loading in case ClerkJS fails to find the\n    // \"data-clerk-frontend-api\" attribute on its script tag.\n\n    // This can happen when the DOM is altered completely during client rehydration.\n    // For example, in Remix with React 18 the document changes completely via `hydrateRoot(document)`.\n\n    // For more information refer to:\n    // - https://github.com/remix-run/remix/issues/2947\n    // - https://github.com/facebook/react/issues/24430\n    if (typeof window !== 'undefined') {\n      window.__clerk_publishable_key = this.#publishableKey;\n      window.__clerk_proxy_url = this.proxyUrl;\n      window.__clerk_domain = this.domain;\n    }\n\n    try {\n      if (this.Clerk) {\n        // Set a fixed Clerk version\n        let c: ClerkProp;\n\n        if (isConstructor<BrowserClerkConstructor | HeadlessBrowserClerkConstructor>(this.Clerk)) {\n          // Construct a new Clerk object if a constructor is passed\n          c = new this.Clerk(this.#publishableKey, {\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n          } as any);\n\n          this.beforeLoad(c);\n          await c.load(this.options);\n        } else {\n          // Otherwise use the instantiated Clerk object\n          c = this.Clerk;\n          if (!c.loaded) {\n            this.beforeLoad(c);\n            await c.load(this.options);\n          }\n        }\n\n        global.Clerk = c;\n      } else if (!__BUILD_DISABLE_RHC__) {\n        // Hot-load latest ClerkJS from Clerk CDN\n        if (!global.Clerk) {\n          await loadClerkJsScript({\n            ...this.options,\n            publishableKey: this.#publishableKey,\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n            nonce: this.options.nonce,\n          });\n        }\n\n        if (!global.Clerk) {\n          throw new Error('Failed to download latest ClerkJS. Contact <EMAIL>.');\n        }\n\n        this.beforeLoad(global.Clerk);\n        await global.Clerk.load(this.options);\n      }\n\n      if (global.Clerk?.loaded) {\n        return this.hydrateClerkJS(global.Clerk);\n      }\n      return;\n    } catch (err) {\n      const error = err as Error;\n      this.#eventBus.emit(clerkEvents.Status, 'error');\n      console.error(error.stack || error.message || error);\n      return;\n    }\n  }\n\n  public on: Clerk['on'] = (...args) => {\n    // Support older clerk-js versions.\n    if (this.clerkjs?.on) {\n      return this.clerkjs.on(...args);\n    } else {\n      this.#eventBus.on(...args);\n    }\n  };\n\n  public off: Clerk['off'] = (...args) => {\n    // Support older clerk-js versions.\n    if (this.clerkjs?.off) {\n      return this.clerkjs.off(...args);\n    } else {\n      this.#eventBus.off(...args);\n    }\n  };\n\n  /**\n   * @deprecated Please use `addStatusListener`. This api will be removed in the next major.\n   */\n  public addOnLoaded = (cb: () => void) => {\n    this.loadedListeners.push(cb);\n    /**\n     * When IsomorphicClerk is loaded execute the callback directly\n     */\n    if (this.loaded) {\n      this.emitLoaded();\n    }\n  };\n\n  /**\n   * @deprecated Please use `__internal_setStatus`. This api will be removed in the next major.\n   */\n  public emitLoaded = () => {\n    this.loadedListeners.forEach(cb => cb());\n    this.loadedListeners = [];\n  };\n\n  private beforeLoad = (clerkjs: BrowserClerk | HeadlessBrowserClerk | undefined) => {\n    if (!clerkjs) {\n      throw new Error('Failed to hydrate latest Clerk JS');\n    }\n  };\n\n  private hydrateClerkJS = (clerkjs: BrowserClerk | HeadlessBrowserClerk | undefined) => {\n    if (!clerkjs) {\n      throw new Error('Failed to hydrate latest Clerk JS');\n    }\n\n    this.clerkjs = clerkjs;\n\n    this.premountMethodCalls.forEach(cb => cb());\n    this.premountAddListenerCalls.forEach((listenerHandlers, listener) => {\n      listenerHandlers.nativeUnsubscribe = clerkjs.addListener(listener);\n    });\n\n    this.#eventBus.internal.retrieveListeners('status')?.forEach(listener => {\n      // Since clerkjs exists it will call `this.clerkjs.on('status', listener)`\n      this.on('status', listener, { notify: true });\n    });\n\n    if (this.preopenSignIn !== null) {\n      clerkjs.openSignIn(this.preopenSignIn);\n    }\n\n    if (this.preopenCheckout !== null) {\n      clerkjs.__internal_openCheckout(this.preopenCheckout);\n    }\n\n    if (this.preopenPlanDetails !== null) {\n      clerkjs.__internal_openPlanDetails(this.preopenPlanDetails);\n    }\n\n    if (this.preopenSignUp !== null) {\n      clerkjs.openSignUp(this.preopenSignUp);\n    }\n\n    if (this.preopenUserProfile !== null) {\n      clerkjs.openUserProfile(this.preopenUserProfile);\n    }\n\n    if (this.preopenUserVerification !== null) {\n      clerkjs.__internal_openReverification(this.preopenUserVerification);\n    }\n\n    if (this.preopenOneTap !== null) {\n      clerkjs.openGoogleOneTap(this.preopenOneTap);\n    }\n\n    if (this.preopenOrganizationProfile !== null) {\n      clerkjs.openOrganizationProfile(this.preopenOrganizationProfile);\n    }\n\n    if (this.preopenCreateOrganization !== null) {\n      clerkjs.openCreateOrganization(this.preopenCreateOrganization);\n    }\n\n    if (this.preOpenWaitlist !== null) {\n      clerkjs.openWaitlist(this.preOpenWaitlist);\n    }\n\n    this.premountSignInNodes.forEach((props, node) => {\n      clerkjs.mountSignIn(node, props);\n    });\n\n    this.premountSignUpNodes.forEach((props, node) => {\n      clerkjs.mountSignUp(node, props);\n    });\n\n    this.premountUserProfileNodes.forEach((props, node) => {\n      clerkjs.mountUserProfile(node, props);\n    });\n\n    this.premountUserButtonNodes.forEach((props, node) => {\n      clerkjs.mountUserButton(node, props);\n    });\n\n    this.premountOrganizationListNodes.forEach((props, node) => {\n      clerkjs.mountOrganizationList(node, props);\n    });\n\n    this.premountWaitlistNodes.forEach((props, node) => {\n      clerkjs.mountWaitlist(node, props);\n    });\n\n    this.premountPricingTableNodes.forEach((props, node) => {\n      clerkjs.mountPricingTable(node, props);\n    });\n\n    this.premountApiKeysNodes.forEach((props, node) => {\n      clerkjs.mountApiKeys(node, props);\n    });\n\n    this.premountOAuthConsentNodes.forEach((props, node) => {\n      clerkjs.__internal_mountOAuthConsent(node, props);\n    });\n\n    /**\n     * Only update status in case `clerk.status` is missing. In any other case, `clerk-js` should be the orchestrator.\n     */\n    if (typeof this.clerkjs.status === 'undefined') {\n      this.#eventBus.emit(clerkEvents.Status, 'ready');\n    }\n\n    this.emitLoaded();\n    return this.clerkjs;\n  };\n\n  get version() {\n    return this.clerkjs?.version;\n  }\n\n  get client(): ClientResource | undefined {\n    if (this.clerkjs) {\n      return this.clerkjs.client;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  get session() {\n    if (this.clerkjs) {\n      return this.clerkjs.session;\n    } else {\n      return undefined;\n    }\n  }\n\n  get user() {\n    if (this.clerkjs) {\n      return this.clerkjs.user;\n    } else {\n      return undefined;\n    }\n  }\n\n  get organization() {\n    if (this.clerkjs) {\n      return this.clerkjs.organization;\n    } else {\n      return undefined;\n    }\n  }\n\n  get telemetry() {\n    if (this.clerkjs) {\n      return this.clerkjs.telemetry;\n    } else {\n      return undefined;\n    }\n  }\n\n  get __unstable__environment(): any {\n    if (this.clerkjs) {\n      return (this.clerkjs as any).__unstable__environment;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  get isSignedIn(): boolean {\n    if (this.clerkjs) {\n      return this.clerkjs.isSignedIn;\n    } else {\n      return false;\n    }\n  }\n\n  get billing(): CommerceBillingNamespace | undefined {\n    return this.clerkjs?.billing;\n  }\n\n  get apiKeys(): APIKeysNamespace | undefined {\n    return this.clerkjs?.apiKeys;\n  }\n\n  __unstable__setEnvironment(...args: any): void {\n    if (this.clerkjs && '__unstable__setEnvironment' in this.clerkjs) {\n      (this.clerkjs as any).__unstable__setEnvironment(args);\n    } else {\n      return undefined;\n    }\n  }\n\n  __unstable__updateProps = async (props: any): Promise<void> => {\n    const clerkjs = await this.#waitForClerkJS();\n    // Handle case where accounts has clerk-react@4 installed, but clerk-js@3 is manually loaded\n    if (clerkjs && '__unstable__updateProps' in clerkjs) {\n      return (clerkjs as any).__unstable__updateProps(props);\n    }\n  };\n\n  __experimental_navigateToTask = async (params?: NextTaskParams): Promise<void> => {\n    if (this.clerkjs) {\n      return this.clerkjs.__experimental_navigateToTask(params);\n    } else {\n      return Promise.reject();\n    }\n  };\n\n  /**\n   * `setActive` can be used to set the active session and/or organization.\n   */\n  setActive = (params: SetActiveParams): Promise<void> => {\n    if (this.clerkjs) {\n      return this.clerkjs.setActive(params);\n    } else {\n      return Promise.reject();\n    }\n  };\n\n  openSignIn = (props?: SignInProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openSignIn(props);\n    } else {\n      this.preopenSignIn = props;\n    }\n  };\n\n  closeSignIn = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeSignIn();\n    } else {\n      this.preopenSignIn = null;\n    }\n  };\n\n  __internal_openCheckout = (props?: __internal_CheckoutProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_openCheckout(props);\n    } else {\n      this.preopenCheckout = props;\n    }\n  };\n\n  __internal_closeCheckout = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_closeCheckout();\n    } else {\n      this.preopenCheckout = null;\n    }\n  };\n\n  __internal_openPlanDetails = (props?: __internal_PlanDetailsProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_openPlanDetails(props);\n    } else {\n      this.preopenPlanDetails = props;\n    }\n  };\n\n  __internal_closePlanDetails = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_closePlanDetails();\n    } else {\n      this.preopenPlanDetails = null;\n    }\n  };\n\n  __internal_openReverification = (props?: __internal_UserVerificationModalProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_openReverification(props);\n    } else {\n      this.preopenUserVerification = props;\n    }\n  };\n\n  __internal_closeReverification = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_closeReverification();\n    } else {\n      this.preopenUserVerification = null;\n    }\n  };\n\n  openGoogleOneTap = (props?: GoogleOneTapProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openGoogleOneTap(props);\n    } else {\n      this.preopenOneTap = props;\n    }\n  };\n\n  closeGoogleOneTap = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeGoogleOneTap();\n    } else {\n      this.preopenOneTap = null;\n    }\n  };\n\n  openUserProfile = (props?: UserProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openUserProfile(props);\n    } else {\n      this.preopenUserProfile = props;\n    }\n  };\n\n  closeUserProfile = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeUserProfile();\n    } else {\n      this.preopenUserProfile = null;\n    }\n  };\n\n  openOrganizationProfile = (props?: OrganizationProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openOrganizationProfile(props);\n    } else {\n      this.preopenOrganizationProfile = props;\n    }\n  };\n\n  closeOrganizationProfile = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeOrganizationProfile();\n    } else {\n      this.preopenOrganizationProfile = null;\n    }\n  };\n\n  openCreateOrganization = (props?: CreateOrganizationProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openCreateOrganization(props);\n    } else {\n      this.preopenCreateOrganization = props;\n    }\n  };\n\n  closeCreateOrganization = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeCreateOrganization();\n    } else {\n      this.preopenCreateOrganization = null;\n    }\n  };\n\n  openWaitlist = (props?: WaitlistProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openWaitlist(props);\n    } else {\n      this.preOpenWaitlist = props;\n    }\n  };\n\n  closeWaitlist = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeWaitlist();\n    } else {\n      this.preOpenWaitlist = null;\n    }\n  };\n\n  openSignUp = (props?: SignUpProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.openSignUp(props);\n    } else {\n      this.preopenSignUp = props;\n    }\n  };\n\n  closeSignUp = () => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.closeSignUp();\n    } else {\n      this.preopenSignUp = null;\n    }\n  };\n\n  mountSignIn = (node: HTMLDivElement, props?: SignInProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountSignIn(node, props);\n    } else {\n      this.premountSignInNodes.set(node, props);\n    }\n  };\n\n  unmountSignIn = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountSignIn(node);\n    } else {\n      this.premountSignInNodes.delete(node);\n    }\n  };\n\n  mountSignUp = (node: HTMLDivElement, props?: SignUpProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountSignUp(node, props);\n    } else {\n      this.premountSignUpNodes.set(node, props);\n    }\n  };\n\n  unmountSignUp = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountSignUp(node);\n    } else {\n      this.premountSignUpNodes.delete(node);\n    }\n  };\n\n  mountUserProfile = (node: HTMLDivElement, props?: UserProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountUserProfile(node, props);\n    } else {\n      this.premountUserProfileNodes.set(node, props);\n    }\n  };\n\n  unmountUserProfile = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountUserProfile(node);\n    } else {\n      this.premountUserProfileNodes.delete(node);\n    }\n  };\n\n  mountOrganizationProfile = (node: HTMLDivElement, props?: OrganizationProfileProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountOrganizationProfile(node, props);\n    } else {\n      this.premountOrganizationProfileNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationProfile = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountOrganizationProfile(node);\n    } else {\n      this.premountOrganizationProfileNodes.delete(node);\n    }\n  };\n\n  mountCreateOrganization = (node: HTMLDivElement, props?: CreateOrganizationProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountCreateOrganization(node, props);\n    } else {\n      this.premountCreateOrganizationNodes.set(node, props);\n    }\n  };\n\n  unmountCreateOrganization = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountCreateOrganization(node);\n    } else {\n      this.premountCreateOrganizationNodes.delete(node);\n    }\n  };\n\n  mountOrganizationSwitcher = (node: HTMLDivElement, props?: OrganizationSwitcherProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountOrganizationSwitcher(node, props);\n    } else {\n      this.premountOrganizationSwitcherNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationSwitcher = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountOrganizationSwitcher(node);\n    } else {\n      this.premountOrganizationSwitcherNodes.delete(node);\n    }\n  };\n\n  __experimental_prefetchOrganizationSwitcher = () => {\n    const callback = () => this.clerkjs?.__experimental_prefetchOrganizationSwitcher();\n    if (this.clerkjs && this.loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('__experimental_prefetchOrganizationSwitcher', callback);\n    }\n  };\n\n  mountOrganizationList = (node: HTMLDivElement, props?: OrganizationListProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountOrganizationList(node, props);\n    } else {\n      this.premountOrganizationListNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationList = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountOrganizationList(node);\n    } else {\n      this.premountOrganizationListNodes.delete(node);\n    }\n  };\n\n  mountUserButton = (node: HTMLDivElement, userButtonProps?: UserButtonProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountUserButton(node, userButtonProps);\n    } else {\n      this.premountUserButtonNodes.set(node, userButtonProps);\n    }\n  };\n\n  unmountUserButton = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountUserButton(node);\n    } else {\n      this.premountUserButtonNodes.delete(node);\n    }\n  };\n\n  mountWaitlist = (node: HTMLDivElement, props?: WaitlistProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountWaitlist(node, props);\n    } else {\n      this.premountWaitlistNodes.set(node, props);\n    }\n  };\n\n  unmountWaitlist = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountWaitlist(node);\n    } else {\n      this.premountWaitlistNodes.delete(node);\n    }\n  };\n\n  mountPricingTable = (node: HTMLDivElement, props?: PricingTableProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountPricingTable(node, props);\n    } else {\n      this.premountPricingTableNodes.set(node, props);\n    }\n  };\n\n  unmountPricingTable = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountPricingTable(node);\n    } else {\n      this.premountPricingTableNodes.delete(node);\n    }\n  };\n\n  mountApiKeys = (node: HTMLDivElement, props?: APIKeysProps): void => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.mountApiKeys(node, props);\n    } else {\n      this.premountApiKeysNodes.set(node, props);\n    }\n  };\n\n  unmountApiKeys = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.unmountApiKeys(node);\n    } else {\n      this.premountApiKeysNodes.delete(node);\n    }\n  };\n\n  __internal_mountOAuthConsent = (node: HTMLDivElement, props?: __internal_OAuthConsentProps) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_mountOAuthConsent(node, props);\n    } else {\n      this.premountOAuthConsentNodes.set(node, props);\n    }\n  };\n\n  __internal_unmountOAuthConsent = (node: HTMLDivElement) => {\n    if (this.clerkjs && this.loaded) {\n      this.clerkjs.__internal_unmountOAuthConsent(node);\n    } else {\n      this.premountOAuthConsentNodes.delete(node);\n    }\n  };\n\n  addListener = (listener: ListenerCallback): UnsubscribeCallback => {\n    if (this.clerkjs) {\n      return this.clerkjs.addListener(listener);\n    } else {\n      const unsubscribe = () => {\n        const listenerHandlers = this.premountAddListenerCalls.get(listener);\n        if (listenerHandlers) {\n          listenerHandlers.nativeUnsubscribe?.();\n          this.premountAddListenerCalls.delete(listener);\n        }\n      };\n      this.premountAddListenerCalls.set(listener, { unsubscribe, nativeUnsubscribe: undefined });\n      return unsubscribe;\n    }\n  };\n\n  navigate = (to: string) => {\n    const callback = () => this.clerkjs?.navigate(to);\n    if (this.clerkjs && this.loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('navigate', callback);\n    }\n  };\n\n  redirectWithAuth = async (...args: Parameters<Clerk['redirectWithAuth']>) => {\n    const callback = () => this.clerkjs?.redirectWithAuth(...args);\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectWithAuth', callback);\n      return;\n    }\n  };\n\n  redirectToSignIn = async (opts?: SignInRedirectOptions) => {\n    const callback = () => this.clerkjs?.redirectToSignIn(opts as any);\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignIn', callback);\n      return;\n    }\n  };\n\n  redirectToSignUp = async (opts?: SignUpRedirectOptions) => {\n    const callback = () => this.clerkjs?.redirectToSignUp(opts as any);\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignUp', callback);\n      return;\n    }\n  };\n\n  redirectToUserProfile = async () => {\n    const callback = () => this.clerkjs?.redirectToUserProfile();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToUserProfile', callback);\n      return;\n    }\n  };\n\n  redirectToAfterSignUp = (): void => {\n    const callback = () => this.clerkjs?.redirectToAfterSignUp();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToAfterSignUp', callback);\n    }\n  };\n\n  redirectToAfterSignIn = () => {\n    const callback = () => this.clerkjs?.redirectToAfterSignIn();\n    if (this.clerkjs && this.loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToAfterSignIn', callback);\n    }\n  };\n\n  redirectToAfterSignOut = () => {\n    const callback = () => this.clerkjs?.redirectToAfterSignOut();\n    if (this.clerkjs && this.loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToAfterSignOut', callback);\n    }\n  };\n\n  redirectToOrganizationProfile = async () => {\n    const callback = () => this.clerkjs?.redirectToOrganizationProfile();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToOrganizationProfile', callback);\n      return;\n    }\n  };\n\n  redirectToCreateOrganization = async () => {\n    const callback = () => this.clerkjs?.redirectToCreateOrganization();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToCreateOrganization', callback);\n      return;\n    }\n  };\n\n  redirectToWaitlist = async () => {\n    const callback = () => this.clerkjs?.redirectToWaitlist();\n    if (this.clerkjs && this.loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('redirectToWaitlist', callback);\n      return;\n    }\n  };\n\n  handleRedirectCallback = async (params: HandleOAuthCallbackParams): Promise<void> => {\n    const callback = () => this.clerkjs?.handleRedirectCallback(params);\n    if (this.clerkjs && this.loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleRedirectCallback', callback);\n    }\n  };\n\n  handleGoogleOneTapCallback = async (\n    signInOrUp: SignInResource | SignUpResource,\n    params: HandleOAuthCallbackParams,\n  ): Promise<void> => {\n    const callback = () => this.clerkjs?.handleGoogleOneTapCallback(signInOrUp, params);\n    if (this.clerkjs && this.loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleGoogleOneTapCallback', callback);\n    }\n  };\n\n  handleEmailLinkVerification = async (params: HandleEmailLinkVerificationParams) => {\n    const callback = () => this.clerkjs?.handleEmailLinkVerification(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('handleEmailLinkVerification', callback);\n    }\n  };\n\n  authenticateWithMetamask = async (params?: AuthenticateWithMetamaskParams) => {\n    const callback = () => this.clerkjs?.authenticateWithMetamask(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithMetamask', callback);\n    }\n  };\n\n  authenticateWithCoinbaseWallet = async (params?: AuthenticateWithCoinbaseWalletParams) => {\n    const callback = () => this.clerkjs?.authenticateWithCoinbaseWallet(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithCoinbaseWallet', callback);\n    }\n  };\n\n  authenticateWithOKXWallet = async (params?: AuthenticateWithOKXWalletParams) => {\n    const callback = () => this.clerkjs?.authenticateWithOKXWallet(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithOKXWallet', callback);\n    }\n  };\n\n  authenticateWithWeb3 = async (params: ClerkAuthenticateWithWeb3Params) => {\n    const callback = () => this.clerkjs?.authenticateWithWeb3(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithWeb3', callback);\n    }\n  };\n\n  authenticateWithGoogleOneTap = async (params: AuthenticateWithGoogleOneTapParams) => {\n    const clerkjs = await this.#waitForClerkJS();\n    return clerkjs.authenticateWithGoogleOneTap(params);\n  };\n\n  createOrganization = async (params: CreateOrganizationParams): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.createOrganization(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('createOrganization', callback);\n    }\n  };\n\n  getOrganization = async (organizationId: string): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.getOrganization(organizationId);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('getOrganization', callback);\n    }\n  };\n\n  joinWaitlist = async (params: JoinWaitlistParams): Promise<WaitlistResource | void> => {\n    const callback = () => this.clerkjs?.joinWaitlist(params);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<WaitlistResource>;\n    } else {\n      this.premountMethodCalls.set('joinWaitlist', callback);\n    }\n  };\n\n  signOut = async (...args: Parameters<Clerk['signOut']>) => {\n    const callback = () => this.clerkjs?.signOut(...args);\n    if (this.clerkjs && this.loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('signOut', callback);\n    }\n  };\n}\n"], "names": ["logErrorInDevMode", "React", "React", "React", "React", "React", "React", "React", "children", "logErrorInDevMode", "React", "React", "logErrorInDevMode", "child", "props", "isReorderItem", "isExternalLink", "useState", "React", "React", "logErrorInDevMode", "React", "React", "React", "React", "React", "React", "React", "React", "React", "React", "_a", "React", "React"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;ACEA,SAAS,yCAAyC;;;ACFlD,SAAS,qBAAAA,0BAAyB;;AAiBlC,OAAOC,UAAS,eAAe,eAAe,kBAAkB;AIhBhE,SAAS,oBAAoB;;;AKD7B,SAAS,eAAe;AACxB,SAAS,qBAAqB;;AKD9B,SAAS,wBAAwB;;;ACAjC,SAAS,mBAAmB;;;;ACA5B,SAAS,iBAAiB;AAC1B,SAAS,aAAa,2BAA2B;AAEjD,SAAS,uBAAuB;;;;AlBGhC,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,MAAA,EAAQ;IACnD,OAAO,MAAA,GAAS,OAAO,WAAW,cAAc,SAAS;AAC3D;;;;;AGHO,IAAM,oBACX,CAAC,WACD,CAAC,SAAyF;QACxF,IAAI;YACF,sZAAO,UAAA,CAAM,QAAA,CAAS,IAAA,CAAK,QAAQ;QACrC,EAAA,OAAQ;YACN,mVAAO,eAAA,CAAa,KAAA,gVAAM,qCAAA,EAAkC,IAAI,CAAC;QACnE;IACF;AAEK,IAAM,4BAA4B,CAAC,UAAuC,gBAAwB;IACvG,IAAI,CAAC,UAAU;QACb,WAAW;IACb;IACA,IAAI,OAAO,aAAa,UAAU;QAChC,WAAW,aAAA,GAAA,8YAAA,CAAA,UAAA,CAAA,aAAA,CAAC,UAAA,MAAQ,QAAS;IAC/B;IACA,OAAO;AACT;AAEO,IAAM,cACX,CAAC,KACD,CAAA,GAAI,SAAc;QAChB,IAAI,MAAM,OAAO,OAAO,YAAY;YAClC,OAAO,GAAG,GAAG,IAAI;QACnB;IACF;;AC/BK,SAAS,cAAiB,CAAA,EAAgB;IAC/C,OAAO,OAAO,MAAM;AACtB;;ACEA,IAAM,SAAS,aAAA,GAAA,IAAI,IAAoB;AAEhC,SAAS,4BAA4B,IAAA,EAAc,KAAA,EAAe,WAAW,CAAA,EAAS;mZAC3FE,UAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,MAAM,QAAQ,OAAO,GAAA,CAAI,IAAI,KAAK;QAClC,IAAI,SAAS,UAAU;YACrB,mVAAO,eAAA,CAAa,KAAA,CAAM,KAAK;QACjC;QACA,OAAO,GAAA,CAAI,MAAM,QAAQ,CAAC;QAE1B,OAAO,MAAM;YACX,OAAO,GAAA,CAAI,MAAA,CAAO,OAAO,GAAA,CAAI,IAAI,KAAK,CAAA,IAAK,CAAC;QAC9C;IACF,GAAG,CAAC,CAAC;AACP;AAEO,SAAS,6BACd,gBAAA,EACA,IAAA,EACA,KAAA,EACwB;IACxB,MAAM,cAAc,iBAAiB,WAAA,IAAe,iBAAiB,IAAA,IAAQ,QAAQ;IACrF,MAAM,MAAM,CAAC,UAAa;QACxB,4BAA4B,MAAM,KAAK;QACvC,OAAO,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,kBAAA;YAAkB,GAAI,KAAA;QAAA,CAAe;IAC/C;IACA,IAAI,WAAA,GAAc,CAAA,6BAAA,EAAgC,WAAW,CAAA,CAAA,CAAA;IAC7D,OAAO;AACT;;;ACfO,IAAM,yBAAyB,CAAC,aAA6C;IAClF,MAAM,eAAe,MAAM,SAAS,MAAM,EAAE,IAAA,CAAK,IAAI;IACrD,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAI,8ZAAA,EAA6B,YAAY;IAEnE,OAAO,SAAS,GAAA,CAAI,CAAC,IAAI,QAAA,CAAW;YAClC,IAAI,GAAG,EAAA;YACP,OAAO,CAAC,OAAkB,SAAS,CAAA,YAAa,UAAU,GAAA,CAAI,CAAC,GAAG,IAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;YACjG,SAAS,IAAM,SAAS,CAAA,YAAa,UAAU,GAAA,CAAI,CAAC,GAAG,IAAO,MAAM,QAAQ,OAAO,CAAE,CAAC;YACtF,QAAQ,IAAM,aAAA,kZAAAC,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MAAG,KAAA,CAAM,KAAK,CAAA,6ZAAI,eAAA,EAAa,GAAG,SAAA,EAAW,KAAA,CAAM,KAAK,CAAC,IAAI,IAAK;QAClF,CAAA,CAAE;AACJ;;;;AEzBO,IAAM,kBAAkB,CAAC,GAAQ,cAAqD;IAC3F,OAAO,CAAC,CAAC,oZAAKE,UAAAA,CAAM,cAAA,CAAe,CAAC,KAAA,CAAM,KAAA,OAAA,KAAA,IAAA,EAA0B,IAAA,MAAS;AAC/E;;ADcO,IAAM,4BAA4B,CACvC,UACA,YACG;IACH,MAAM,qBAAqB;QAAC;QAAW,UAAU;KAAA;IACjD,OAAO,eACL;QACE;QACA;QACA,eAAe;QACf,eAAe;QACf,oBAAoB;QACpB,eAAe;IACjB,GACA;AAEJ;AAEO,IAAM,oCAAoC,CAC/C,UACA,YACG;IACH,MAAM,qBAAqB;QAAC;QAAW,SAAS;KAAA;IAChD,OAAO,eACL;QACE;QACA;QACA,eAAe;QACf,eAAe;QACf,eAAe;IACjB,GACA;AAEJ;AA+BO,IAAM,uBAAuB,CAAC,aAA8B;IACjE,MAAM,oBAAuC,CAAC,CAAA;IAE9C,MAAM,qBAA4B;QAChC;QACA;QACA;QACA;QACA;KACF;mZAEAC,UAAAA,CAAM,QAAA,CAAS,OAAA,CAAQ,UAAU,CAAA,UAAS;QACxC,IAAI,CAAC,mBAAmB,IAAA,CAAK,CAAA,YAAa,gBAAgB,OAAO,SAAS,CAAC,GAAG;YAC5E,kBAAkB,IAAA,CAAK,KAAK;QAC9B;IACF,CAAC;IAED,OAAO;AACT;AAEA,IAAM,iBAAiB,CAAC,QAA8B,YAAoC;IACxF,MAAM,EAAE,QAAA,EAAU,aAAA,EAAe,aAAA,EAAe,kBAAA,EAAoB,kBAAA,EAAoB,aAAA,CAAc,CAAA,GAAI;IAC1G,MAAM,EAAE,sBAAsB,KAAA,CAAM,CAAA,GAAI,WAAW,CAAC;IACpD,MAAM,gBAAwC,CAAC,CAAA;mZAE/CA,UAAAA,CAAM,QAAA,CAAS,OAAA,CAAQ,UAAU,CAAA,UAAS;QACxC,IACE,CAAC,gBAAgB,OAAO,aAAa,KACrC,CAAC,gBAAgB,OAAO,aAAa,KACrC,CAAC,gBAAgB,OAAO,kBAAkB,GAC1C;YACA,IAAI,SAAS,CAAC,qBAAqB;gBACjC,CAAA,GAAA,0SAAA,CAAA,oBAAA,kVAAkB,8BAAA,EAA4B,aAAa,CAAC;YAC9D;YACA;QACF;QAEA,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI;QAElB,MAAM,EAAE,UAAAC,SAAAA,EAAU,KAAA,EAAO,GAAA,EAAK,SAAA,CAAU,CAAA,GAAI;QAE5C,IAAI,gBAAgB,OAAO,aAAa,GAAG;YACzC,IAAI,cAAc,OAAO,kBAAkB,GAAG;gBAE5C,cAAc,IAAA,CAAK;oBAAE;gBAAM,CAAC;YAC9B,OAAA,IAAW,aAAa,KAAK,GAAG;gBAE9B,cAAc,IAAA,CAAK;oBAAE;oBAAO;oBAAW,UAAAA;oBAAU;gBAAI,CAAC;YACxD,OAAO;gBACL,CAAA,GAAA,0SAAA,CAAA,oBAAA,kVAAkB,uBAAA,EAAqB,aAAa,CAAC;gBACrD;YACF;QACF;QAEA,IAAI,gBAAgB,OAAO,aAAa,GAAG;YACzC,IAAI,eAAe,KAAK,GAAG;gBAEzB,cAAc,IAAA,CAAK;oBAAE;oBAAO;oBAAW;gBAAI,CAAC;YAC9C,OAAO;gBACL,CAAA,GAAA,0SAAA,CAAA,oBAAA,kVAAkB,uBAAA,EAAqB,aAAa,CAAC;gBACrD;YACF;QACF;IACF,CAAC;IAED,MAAM,qBAAqD,CAAC,CAAA;IAC5D,MAAM,uBAAuD,CAAC,CAAA;IAC9D,MAAM,uBAAuD,CAAC,CAAA;IAE9D,cAAc,OAAA,CAAQ,CAAC,IAAI,UAAU;QACnC,IAAI,aAAa,EAAE,GAAG;YACpB,mBAAmB,IAAA,CAAK;gBAAE,WAAW,GAAG,QAAA;gBAAU,IAAI;YAAM,CAAC;YAC7D,qBAAqB,IAAA,CAAK;gBAAE,WAAW,GAAG,SAAA;gBAAW,IAAI;YAAM,CAAC;YAChE;QACF;QACA,IAAI,eAAe,EAAE,GAAG;YACtB,qBAAqB,IAAA,CAAK;gBAAE,WAAW,GAAG,SAAA;gBAAW,IAAI;YAAM,CAAC;QAClE;IACF,CAAC;IAED,MAAM,4BAA4B,uBAAuB,kBAAkB;IAC3E,MAAM,8BAA8B,uBAAuB,oBAAoB;IAC/E,MAAM,8BAA8B,uBAAuB,oBAAoB;IAE/E,MAAM,cAA4B,CAAC,CAAA;IACnC,MAAM,qBAA4C,CAAC,CAAA;IAEnD,cAAc,OAAA,CAAQ,CAAC,IAAI,UAAU;QACnC,IAAI,cAAc,IAAI,kBAAkB,GAAG;YACzC,YAAY,IAAA,CAAK;gBAAE,OAAO,GAAG,KAAA;YAAM,CAAC;YACpC;QACF;QACA,IAAI,aAAa,EAAE,GAAG;YACpB,MAAM,EACJ,QAAQ,aAAA,EACR,KAAA,EACA,OAAA,EACF,GAAI,0BAA0B,IAAA,CAAK,CAAA,IAAK,EAAE,EAAA,KAAO,KAAK;YACtD,MAAM,EACJ,QAAQ,WAAA,EACR,OAAO,SAAA,EACP,SAAS,WAAA,EACX,GAAI,4BAA4B,IAAA,CAAK,CAAA,IAAK,EAAE,EAAA,KAAO,KAAK;YACxD,YAAY,IAAA,CAAK;gBAAE,OAAO,GAAG,KAAA;gBAAO,KAAK,GAAG,GAAA;gBAAK;gBAAO;gBAAS;gBAAW;YAAY,CAAC;YACzF,mBAAmB,IAAA,CAAK,aAAa;YACrC,mBAAmB,IAAA,CAAK,WAAW;YACnC;QACF;QACA,IAAI,eAAe,EAAE,GAAG;YACtB,MAAM,EACJ,QAAQ,WAAA,EACR,OAAO,SAAA,EACP,SAAS,WAAA,EACX,GAAI,4BAA4B,IAAA,CAAK,CAAA,IAAK,EAAE,EAAA,KAAO,KAAK;YACxD,YAAY,IAAA,CAAK;gBAAE,OAAO,GAAG,KAAA;gBAAO,KAAK,GAAG,GAAA;gBAAK;gBAAW;YAAY,CAAC;YACzE,mBAAmB,IAAA,CAAK,WAAW;YACnC;QACF;IACF,CAAC;IAED,OAAO;QAAE;QAAa;IAAmB;AAC3C;AAEA,IAAM,gBAAgB,CAAC,YAAiB,eAAkC;IACxE,MAAM,EAAE,QAAA,EAAU,KAAA,EAAO,GAAA,EAAK,SAAA,CAAU,CAAA,GAAI;IAC5C,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,WAAW,IAAA,CAAK,CAAA,IAAK,MAAM,KAAK;AAC5E;AAEA,IAAM,eAAe,CAAC,eAA6B;IACjD,MAAM,EAAE,QAAA,EAAU,KAAA,EAAO,GAAA,EAAK,SAAA,CAAU,CAAA,GAAI;IAC5C,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AACjD;AAEA,IAAM,iBAAiB,CAAC,eAA6B;IACnD,MAAM,EAAE,QAAA,EAAU,KAAA,EAAO,GAAA,EAAK,SAAA,CAAU,CAAA,GAAI;IAC5C,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC;AAChD;;;AEzMO,IAAM,+BAA+B,CAAC,aAAkD;IAC7F,MAAM,qBAAqB;QAAC;QAAiB,SAAS;KAAA;IACtD,OAAO,mBAAmB;QACxB;QACA;QACA,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;QACnB,0BAA0B;QAC1B,0BAA0B;IAC5B,CAAC;AACH;AAcA,IAAM,qBAAqB,CAAC,EAC1B,QAAA,EACA,kBAAA,EACA,mBAAA,EACA,iBAAA,EACA,wBAAA,EACA,wBAAA,EACA,kBAAA,EACF,KAAgC;IAC9B,MAAM,gBAAsC,CAAC,CAAA;IAC7C,MAAM,kBAAoC,CAAC,CAAA;IAC3C,MAAM,yBAAgD,CAAC,CAAA;mZAEvDG,UAAAA,CAAM,QAAA,CAAS,OAAA,CAAQ,UAAU,CAAA,UAAS;QACxC,IACE,CAAC,gBAAgB,OAAO,kBAAkB,KAC1C,CAAC,gBAAgB,OAAO,wBAAwB,KAChD,CAAC,gBAAgB,OAAO,wBAAwB,GAChD;YACA,IAAI,OAAO;+TACTC,oBAAAA,8UAAkB,6BAA0B;YAC9C;YACA;QACF;QAGA,IAAI,gBAAgB,OAAO,wBAAwB,KAAK,gBAAgB,OAAO,wBAAwB,GAAG;YACxG;QACF;QAGA,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI;uZAElBD,UAAAA,CAAM,QAAA,CAAS,OAAA,CAAQ,MAAM,QAAA,EAAU,CAAAE,WAAS;YAC9C,IAAI,CAAC,gBAAgBA,QAAO,mBAAmB,KAAK,CAAC,gBAAgBA,QAAO,iBAAiB,GAAG;gBAC9F,IAAIA,QAAO;mUACTD,oBAAAA,6UAAkB,mCAA+B;gBACnD;gBAEA;YACF;YAEA,MAAM,EAAE,OAAAE,MAAAA,CAAM,CAAA,GAAID;YAElB,MAAM,EAAE,KAAA,EAAO,SAAA,EAAW,IAAA,EAAM,OAAA,EAAS,IAAA,CAAK,CAAA,GAAIC;YAElD,IAAI,gBAAgBD,QAAO,mBAAmB,GAAG;gBAC/C,IAAIE,eAAcD,QAAO,kBAAkB,GAAG;oBAE5C,cAAc,IAAA,CAAK;wBAAE;oBAAM,CAAC;gBAC9B,OAAA,IAAW,iBAAiBA,MAAK,GAAG;oBAClC,MAAM,WAAW;wBACf;wBACA;oBACF;oBAEA,IAAI,YAAY,KAAA,GAAW;wBACzB,cAAc,IAAA,CAAK;4BACjB,GAAG,QAAA;4BACH;wBACF,CAAC;oBACH,OAAA,IAAW,SAAS,KAAA,GAAW;wBAC7B,cAAc,IAAA,CAAK;4BACjB,GAAG,QAAA;4BACH,MAAM,KAAK,UAAA,CAAW,GAAG,IAAI,OAAO,CAAA,CAAA,EAAI,IAAI,EAAA;wBAC9C,CAAC;oBACH,OAAO;uUAELF,oBAAAA,EAAkB,4DAA4D;wBAC9E;oBACF;gBACF,OAAO;mUACLA,oBAAAA,8UAAkB,uCAAoC;oBACtD;gBACF;YACF;YAEA,IAAI,gBAAgBC,QAAO,iBAAiB,GAAG;gBAC7C,IAAIG,gBAAeF,MAAK,GAAG;oBACzB,cAAc,IAAA,CAAK;wBAAE;wBAAO;wBAAW;oBAAK,CAAC;gBAC/C,OAAO;mUACLF,oBAAAA,EAAkB,+WAAgC;oBAClD;gBACF;YACF;QACF,CAAC;IACH,CAAC;IAED,MAAM,2BAA2D,CAAC,CAAA;IAClE,MAAM,uBAAuD,CAAC,CAAA;IAC9D,cAAc,OAAA,CAAQ,CAAC,IAAI,UAAU;QACnC,IAAI,iBAAiB,EAAE,GAAG;YACxB,yBAAyB,IAAA,CAAK;gBAAE,WAAW,GAAG,SAAA;gBAAW,IAAI;YAAM,CAAC;QACtE;QACA,IAAII,gBAAe,EAAE,GAAG;YACtB,qBAAqB,IAAA,CAAK;gBAAE,WAAW,GAAG,SAAA;gBAAW,IAAI;YAAM,CAAC;QAClE;IACF,CAAC;IAED,MAAM,kCAAkC,uBAAuB,wBAAwB;IACvF,MAAM,8BAA8B,uBAAuB,oBAAoB;IAE/E,cAAc,OAAA,CAAQ,CAAC,IAAI,UAAU;QACnC,IAAID,eAAc,IAAI,kBAAkB,GAAG;YACzC,gBAAgB,IAAA,CAAK;gBACnB,OAAO,GAAG,KAAA;YACZ,CAAC;QACH;QACA,IAAI,iBAAiB,EAAE,GAAG;YACxB,MAAM,EACJ,QAAQ,UAAA,EACR,OAAO,SAAA,EACP,SAAS,WAAA,EACX,GAAI,gCAAgC,IAAA,CAAK,CAAA,IAAK,EAAE,EAAA,KAAO,KAAK;YAC5D,MAAM,WAA2B;gBAC/B,OAAO,GAAG,KAAA;gBACV;gBACA;YACF;YAEA,IAAI,aAAa,IAAI;gBACnB,SAAS,OAAA,GAAU,GAAG,OAAA;YACxB,OAAA,IAAW,UAAU,IAAI;gBACvB,SAAS,IAAA,GAAO,GAAG,IAAA;YACrB;YACA,gBAAgB,IAAA,CAAK,QAAQ;YAC7B,uBAAuB,IAAA,CAAK,UAAU;QACxC;QACA,IAAIC,gBAAe,EAAE,GAAG;YACtB,MAAM,EACJ,QAAQ,UAAA,EACR,OAAO,SAAA,EACP,SAAS,WAAA,EACX,GAAI,4BAA4B,IAAA,CAAK,CAAA,IAAK,EAAE,EAAA,KAAO,KAAK;YACxD,gBAAgB,IAAA,CAAK;gBACnB,OAAO,GAAG,KAAA;gBACV,MAAM,GAAG,IAAA;gBACT;gBACA;YACF,CAAC;YACD,uBAAuB,IAAA,CAAK,UAAU;QACxC;IACF,CAAC;IAED,OAAO;QAAE;QAAiB;IAAuB;AACnD;AAEA,IAAMD,iBAAgB,CAAC,YAAiB,eAAkC;IACxE,MAAM,EAAE,QAAA,EAAU,KAAA,EAAO,OAAA,EAAS,SAAA,CAAU,CAAA,GAAI;IAChD,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,WAAW,IAAA,CAAK,CAAA,IAAK,MAAM,KAAK;AAChF;AAEA,IAAM,mBAAmB,CAAC,eAAyD;IACjF,MAAM,EAAE,KAAA,EAAO,SAAA,EAAW,OAAA,EAAS,IAAA,CAAK,CAAA,GAAI;IAC5C,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,SAAA,CAAU,OAAO,YAAY,cAAc,OAAO,SAAS,QAAA;AACrF;AAEA,IAAMC,kBAAiB,CAAC,eAAuD;IAC7E,MAAM,EAAE,KAAA,EAAO,IAAA,EAAM,SAAA,CAAU,CAAA,GAAI;IACnC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;AACpC;;ACrMA,SAAS,uBAAuB,OAAA,EAA6E;IAC3G,MAAM,EAAE,OAAO,YAAA,OAAA,KAAA,IAAA,SAAU,IAAA,EAAM,QAAA,EAAU,UAAU,CAAA,CAAE,CAAA,GAAI;IAEzD,OAAO,IAAI,QAAc,CAAC,SAAS,WAAW;QAC5C,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,MAAM,0BAA0B,CAAC;YAC5C;QACF;QAEA,IAAI,iBAAqC;QACzC,IAAI,UAAU;YACZ,iBAAiB,QAAA,OAAA,KAAA,IAAA,KAAM,aAAA,CAAc;QACvC;QAGA,MAAM,0BAAA,CAA0B,kBAAA,OAAA,KAAA,IAAA,eAAgB,iBAAA,KAAqB,eAAe,iBAAA,GAAoB;QACxG,IAAI,yBAAyB;YAC3B,QAAQ;YACR;QACF;QAGA,MAAM,WAAW,IAAI,iBAAiB,CAAA,kBAAiB;YACrD,KAAA,MAAW,YAAY,cAAe;gBACpC,IAAI,SAAS,IAAA,KAAS,aAAa;oBACjC,IAAI,CAAC,kBAAkB,UAAU;wBAC/B,iBAAiB,QAAA,OAAA,KAAA,IAAA,KAAM,aAAA,CAAc;oBACvC;oBAEA,IAAA,CAAI,kBAAA,OAAA,KAAA,IAAA,eAAgB,iBAAA,KAAqB,eAAe,iBAAA,GAAoB,GAAG;wBAC7E,SAAS,UAAA,CAAW;wBACpB,QAAQ;wBACR;oBACF;gBACF;YACF;QACF,CAAC;QAED,SAAS,OAAA,CAAQ,MAAM;YAAE,WAAW;YAAM,SAAS;QAAK,CAAC;QAGzD,IAAI,UAAU,GAAG;YACf,WAAW,MAAM;gBACf,SAAS,UAAA,CAAW;gBACpB,OAAO,IAAI,MAAM,CAAA,oCAAA,CAAsC,CAAC;YAC1D,GAAG,OAAO;QACZ;IACF,CAAC;AACH;AAKO,SAAS,yBAAyB,SAAA,EAAoB;IAC3D,MAAM,gaAAa,SAAA,CAAsB;IACzC,MAAM,CAAC,QAAQ,SAAS,CAAA,sZAAIC,WAAAA,EAA6C,WAAW;IAEpF,CAAA,GAAA,8YAAA,CAAA,YAAA,EAAU,MAAM;QACd,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM,4DAA4D;QAC9E;QAEA,IAAI,OAAO,WAAW,eAAe,CAAC,WAAW,OAAA,EAAS;YACxD,WAAW,OAAA,GAAU,uBAAuB;gBAAE,UAAU,CAAA,uBAAA,EAA0B,SAAS,CAAA,EAAA,CAAA;YAAK,CAAC,EAC9F,IAAA,CAAK,MAAM;gBACV,UAAU,UAAU;YACtB,CAAC,EACA,KAAA,CAAM,MAAM;gBACX,UAAU,OAAO;YACnB,CAAC;QACL;IACF,GAAG;QAAC,SAAS;KAAC;IAEd,OAAO;AACT;;;;ACxEA,IAAM,eAAe,CAAC,UAAoC;IACxD,OAAO,WAAW;AACpB;AAEA,IAAM,cAAc,CAAC,UAAmC;IACtD,OAAO,UAAU;AACnB;AAEA,IAAM,4BAA4B,CAChC,cAKG;IACH,OAAO,aAAA,OAAA,KAAA,IAAA,UAAW,GAAA,CAAI,CAAC,EAAE,SAAA,EAAW,WAAA,EAAa,GAAG,KAAK,CAAA,GAAM;AACjE;AAmCO,IAAM,oBAAN,6ZAAgCC,UAAAA,CAAM,aAAA,CAQ3C;IARK,aAAA;QAAA,KAAA,IAAA;QASL,IAAA,CAAQ,OAAA,kZAAUA,UAAAA,CAAM,SAAA,CAA0B;IAAA;IAElD,mBAAmB,UAAA,EAA8C;QArEnE,IAAA,IAAA,IAAA,IAAA;QAsEI,IAAI,CAAC,aAAa,UAAU,KAAK,CAAC,aAAa,IAAA,CAAK,KAAK,GAAG;YAC1D;QACF;QAKA,MAAM,2TAAY,UAAA,EAAQ,WAAW,KAAA,EAAO,eAAe,mBAAmB,UAAU;QACxF,MAAM,WAAW,yTAAA,EAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,EAAO,eAAe,mBAAmB,UAAU;QAGvF,MAAM,qBAAA,CAAA,CAAqB,KAAA,UAAU,WAAA,KAAV,OAAA,KAAA,IAAA,GAAuB,MAAA,MAAA,CAAA,CAAW,KAAA,SAAS,WAAA,KAAT,OAAA,KAAA,IAAA,GAAsB,MAAA;QACnF,MAAM,yBAAA,CAAA,CAAyB,KAAA,UAAU,eAAA,KAAV,OAAA,KAAA,IAAA,GAA2B,MAAA,MAAA,CAAA,CAAW,KAAA,SAAS,eAAA,KAAT,OAAA,KAAA,IAAA,GAA0B,MAAA;QAI/F,MAAM,+BAA+B,0BAA0B,WAAW,KAAA,CAAM,eAAe;QAC/F,MAAM,8BAA8B,0BAA0B,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAe;QAE9F,IACE,CAAC,4TAAA,EAAc,WAAW,QAAQ,KAClC,6SAAC,gBAAA,EAAc,8BAA8B,2BAA2B,KACxE,sBACA,wBACA;YACA,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;gBACxB,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY;oBAAE,MAAM,IAAA,CAAK,OAAA,CAAQ,OAAA;oBAAS,OAAO,IAAA,CAAK,KAAA,CAAM,KAAA;gBAAM,CAAC;YAChF;QACF;IACF;IAEA,oBAAoB;QAClB,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;YACxB,IAAI,aAAa,IAAA,CAAK,KAAK,GAAG;gBAC5B,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS,IAAA,CAAK,KAAA,CAAM,KAAK;YACzD;YAEA,IAAI,YAAY,IAAA,CAAK,KAAK,GAAG;gBAC3B,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,KAAK;YAClC;QACF;IACF;IAEA,uBAAuB;QACrB,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;YACxB,IAAI,aAAa,IAAA,CAAK,KAAK,GAAG;gBAC5B,IAAA,CAAK,KAAA,CAAM,OAAA,CAAQ,IAAA,CAAK,OAAA,CAAQ,OAAO;YACzC;YACA,IAAI,YAAY,IAAA,CAAK,KAAK,GAAG;gBAC3B,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM;YACnB;QACF;IACF;IAEA,SAAS;QACP,MAAM,EAAE,sBAAsB,KAAA,CAAM,CAAA,GAAI,IAAA,CAAK,KAAA;QAC7C,MAAM,iBAAiB;YACrB,KAAK,IAAA,CAAK,OAAA;YACV,GAAG,IAAA,CAAK,KAAA,CAAM,SAAA;YACd,GAAI,IAAA,CAAK,KAAA,CAAM,SAAA,IAAa;gBAAE,wBAAwB,IAAA,CAAK,KAAA,CAAM,SAAA;YAAU,CAAA;QAC7E;QAEA,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,CAAC,uBAAuB,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,OAAA;YAAK,GAAG,cAAA;QAAA,CAAgB,GACjD,IAAA,CAAK,KAAA,CAAM,QACd;IAEJ;AACF;;ATpBA,IAAM,wBAAwB,CAAC,UAAsC;IAvHrE,IAAA,IAAA;IAwHE,OACE,aAAA,GAAAC,yZAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MAAA,CACG,KAAA,SAAA,OAAA,KAAA,IAAA,MAAO,kBAAA,KAAP,OAAA,KAAA,IAAA,GAA2B,GAAA,CAAI,CAAC,QAAQ,2ZAAU,gBAAA,EAAc,QAAQ;YAAE,KAAK;QAAM,CAAC,IAAA,CACtF,KAAA,SAAA,OAAA,KAAA,IAAA,MAAO,sBAAA,KAAP,OAAA,KAAA,IAAA,GAA+B,GAAA,CAAI,CAAC,QAAQ,SAAU,kaAAA,EAAc,QAAQ;YAAE,KAAK;QAAM,CAAC,EAC7F;AAEJ;AAEO,IAAM,yVAAS,YAAA,EACpB,CAAC,EAAE,KAAA,EAAO,SAAA,EAAW,QAAA,EAAU,GAAG,MAAM,CAAA,KAAiD;IACvF,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,OAAO,MAAM,WAAA;QACb,SAAS,MAAM,aAAA;QACf,aAAc,MAAc,uBAAA;QAC5B;QACA,WAAW;IAAA;AAKrB,GACA;IAAE,WAAW;IAAU,oBAAoB;AAAK;AAG3C,IAAM,yVAAS,YAAA,EACpB,CAAC,EAAE,KAAA,EAAO,SAAA,EAAW,QAAA,EAAU,GAAG,MAAM,CAAA,KAAiD;IACvF,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,OACE,aAAA,GAAAA,yZAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,iZAAAA,WAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,OAAO,MAAM,WAAA;QACb,SAAS,MAAM,aAAA;QACf,aAAc,MAAc,uBAAA;QAC5B;QACA,WAAW;IAAA;AAKrB,GACA;IAAE,WAAW;IAAU,oBAAoB;AAAK;AAG3C,SAAS,gBAAgB,EAAE,QAAA,CAAS,CAAA,EAA4C;mTACrFC,oBAAAA,8UAAkB,+BAA4B;IAC9C,OAAO,aAAA,kZAAAD,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB;AAEO,SAAS,gBAAgB,EAAE,QAAA,CAAS,CAAA,EAA4C;IACrFC,mUAAAA,8UAAkB,+BAA4B;IAC9C,OAAO,aAAA,kZAAAD,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB;AAEA,IAAM,+VAAe,YAAA,EACnB,CAAC,EACC,KAAA,EACA,SAAA,EACA,QAAA,EACA,GAAG,OACL,KAAiG;IAC/F,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,MAAM,EAAE,WAAA,EAAa,kBAAA,CAAmB,CAAA,GAAI,0BAA0B,MAAM,QAAQ;IACpF,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACvB,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,OAAO,MAAM,gBAAA;QACb,SAAS,MAAM,kBAAA;QACf,aAAc,MAAc,uBAAA;QAC5B,OAAO;YAAE,GAAG,KAAA;YAAO;QAAY;QAC/B,WAAW;IAAA,GAEX,aAAA,GAAAA,yZAAAA,CAAA,aAAA,CAAC,uBAAA;QAAsB;IAAA,CAAwC;AAIvE,GACA;IAAE,WAAW;IAAe,oBAAoB;AAAK;AAGhD,IAAM,cAAqC,OAAO,MAAA,CAAO,cAAc;IAC5E,MAAM;IACN,MAAM;AACR,CAAC;AAED,IAAM,qBAAoB,kaAAA,EAA0B;IAClD,OAAO,KAAO,CAAD;IACb,SAAS,KAAO,CAAD;IACf,aAAa,KAAO,CAAD;AACrB,CAAC;AAED,IAAM,6VAAc,aAAA,EAClB,CAAC,EACC,KAAA,EACA,SAAA,EACA,QAAA,EACA,GAAG,OACL,KAA0F;IACxF,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,MAAM,EAAE,WAAA,EAAa,kBAAA,CAAmB,CAAA,GAAI,0BAA0B,MAAM,QAAA,EAAU;QACpF,qBAAqB,CAAC,CAAC,MAAM,yBAAA;IAC/B,CAAC;IACD,MAAM,mBAAmB,OAAO,MAAA,CAAO,MAAM,gBAAA,IAAoB,CAAC,GAAG;QAAE;IAAY,CAAC;IACpF,MAAM,EAAE,eAAA,EAAiB,sBAAA,CAAuB,CAAA,GAAI,6BAA6B,MAAM,QAAQ;IAC/F,MAAM,oBAAoB,qBAAqB,MAAM,QAAQ;IAE7D,MAAM,gBAAgB;QACpB,OAAO,MAAM,eAAA;QACb,SAAS,MAAM,iBAAA;QACf,aAAc,MAAc,uBAAA;QAC5B,OAAO;YAAE,GAAG,KAAA;YAAO;YAAkB;QAAgB;IACvD;IACA,MAAM,cAAc;QAClB;QACA;IACF;IAEA,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,kBAAkB,QAAA,EAAlB;QAA2B,OAAO;IAAA,GAChC,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACC,GAAG,aAAA;QACJ,qBAAqB,CAAC,CAAC,MAAM,yBAAA;QAC7B,WAAW;IAAA,GAGV,MAAM,yBAAA,GAA4B,oBAAoB,MACvD,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,uBAAA;QAAuB,GAAG,WAAA;IAAA,CAAa;AAKlD,GACA;IAAE,WAAW;IAAc,oBAAoB;AAAK;AAG/C,SAAS,UAAU,EAAE,QAAA,CAAS,CAAA,EAAsB;mTACzDC,oBAAAA,8UAAkB,mCAAgC;IAClD,OAAO,aAAA,kZAAAD,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB;AAEO,SAAS,WAAW,EAAE,QAAA,CAAS,CAAA,EAA6C;kTACjFC,qBAAAA,8UAAkB,oCAAiC;IACnD,OAAO,aAAA,kZAAAD,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB;AAEO,SAAS,SAAS,EAAE,QAAA,CAAS,CAAA,EAA2C;mTAC7EC,oBAAAA,8UAAkB,kCAA+B;IACjD,OAAO,aAAA,kZAAAD,UAAAA,CAAA,aAAA,+YAAAA,WAAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB;AAEO,SAAS,iBAAiB,WAAA,EAA2D;IAC1F,MAAM,maAAgB,aAAA,EAAW,iBAAiB;IAElD,MAAM,cAAc;QAClB,GAAG,aAAA;QACH,OAAO;YACL,GAAG,cAAc,KAAA;YACjB,GAAG,WAAA;QACL;IACF;IAEA,OAAO,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QAAmB,GAAG,WAAA;IAAA,CAAa;AAC7C;AAEO,IAAM,aAAmC,OAAO,MAAA,CAAO,aAAa;IACzE;IACA;IACA;IACA,QAAQ;IACR,MAAM;IACN,uBAAuB;AACzB,CAAC;AAEM,SAAS,wBAAwB,EAAE,QAAA,CAAS,CAAA,EAAoD;mTACrGC,oBAAAA,EAAkB,mXAAoC;IACtD,OAAO,aAAA,kZAAAD,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB;AAEO,SAAS,wBAAwB,EAAE,QAAA,CAAS,CAAA,EAAoD;KACrGC,kUAAAA,8UAAkB,uCAAoC;IACtD,OAAO,aAAA,kZAAAD,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MAAG,QAAS;AACrB;AAEA,IAAM,uWAAuB,YAAA,EAC3B,CAAC,EACC,KAAA,EACA,SAAA,EACA,QAAA,EACA,GAAG,OACL,KAAyG;IACvG,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,MAAM,EAAE,WAAA,EAAa,kBAAA,CAAmB,CAAA,GAAI,kCAAkC,MAAM,QAAQ;IAC5F,OACE,aAAA,GAAAA,yZAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,OAAO,MAAM,wBAAA;QACb,SAAS,MAAM,0BAAA;QACf,aAAc,MAAc,uBAAA;QAC5B,OAAO;YAAE,GAAG,KAAA;YAAO;QAAY;QAC/B,WAAW;IAAA,GAEX,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,uBAAA;QAAsB;IAAA,CAAwC;AAKzE,GACA;IAAE,WAAW;IAAuB,oBAAoB;AAAK;AAGxD,IAAM,sBAAqD,OAAO,MAAA,CAAO,sBAAsB;IACpG,MAAM;IACN,MAAM;AACR,CAAC;AAEM,IAAM,qWAAqB,YAAA,EAChC,CAAC,EAAE,KAAA,EAAO,SAAA,EAAW,QAAA,EAAU,GAAG,MAAM,CAAA,KAA6D;IACnG,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,OACE,aAAA,GAAAA,yZAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,OAAO,MAAM,uBAAA;QACb,SAAS,MAAM,yBAAA;QACf,aAAc,MAAc,uBAAA;QAC5B;QACA,WAAW;IAAA;AAKrB,GACA;IAAE,WAAW;IAAsB,oBAAoB;AAAK;AAG9D,IAAM,8BAA8B,maAAA,EAA0B;IAC5D,OAAO,KAAO,CAAD;IACb,SAAS,KAAO,CAAD;IACf,aAAa,KAAO,CAAD;AACrB,CAAC;AAED,IAAM,wWAAwB,YAAA,EAC5B,CAAC,EACC,KAAA,EACA,SAAA,EACA,QAAA,EACA,GAAG,OACL,KAAoG;IAClG,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,MAAM,EAAE,WAAA,EAAa,kBAAA,CAAmB,CAAA,GAAI,kCAAkC,MAAM,QAAA,EAAU;QAC5F,qBAAqB,CAAC,CAAC,MAAM,yBAAA;IAC/B,CAAC;IACD,MAAM,2BAA2B,OAAO,MAAA,CAAO,MAAM,wBAAA,IAA4B,CAAC,GAAG;QAAE;IAAY,CAAC;IACpG,MAAM,oBAAoB,qBAAqB,MAAM,QAAQ;IAE7D,MAAM,gBAAgB;QACpB,OAAO,MAAM,yBAAA;QACb,SAAS,MAAM,2BAAA;QACf,aAAc,MAAc,uBAAA;QAC5B,OAAO;YAAE,GAAG,KAAA;YAAO;QAAyB;QAC5C,WAAW;QACX;IACF;IAKA,MAAM,2CAAA,CAA4C;IAElD,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,4BAA4B,QAAA,EAA5B;QAAqC,OAAO;IAAA,GAC3C,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QACE,GAAG,aAAA;QACJ,qBAAqB,CAAC,CAAC,MAAM,yBAAA;IAAA,GAG5B,MAAM,yBAAA,GAA4B,oBAAoB,MACvD,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,uBAAA;QAAsB;IAAA,CAAwC;AAM3E,GACA;IAAE,WAAW;IAAwB,oBAAoB;AAAK;AAGzD,SAAS,2BACd,WAAA,EACA;IACA,MAAM,maAAgB,aAAA,EAAW,2BAA2B;IAE5D,MAAM,cAAc;QAClB,GAAG,aAAA;QACH,OAAO;YACL,GAAG,cAAc,KAAA;YACjB,GAAG,WAAA;QACL;IACF;IAEA,OAAO,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QAAmB,GAAG,WAAA;IAAA,CAAa;AAC7C;AAEO,IAAM,uBAAuD,OAAO,MAAA,CAAO,uBAAuB;IACvG;IACA;IACA,uBAAuB;AACzB,CAAC;AAEM,IAAM,mWAAmB,YAAA,EAC9B,CAAC,EAAE,KAAA,EAAO,SAAA,EAAW,QAAA,EAAU,GAAG,MAAM,CAAA,KAA2D;IACjG,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,iZAAAA,WAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,OAAO,MAAM,qBAAA;QACb,SAAS,MAAM,uBAAA;QACf,aAAc,MAAc,uBAAA;QAC5B;QACA,WAAW;IAAA;AAKrB,GACA;IAAE,WAAW;IAAoB,oBAAoB;AAAK;AAGrD,IAAM,+VAAe,YAAA,EAC1B,CAAC,EAAE,KAAA,EAAO,SAAA,EAAW,QAAA,EAAU,GAAG,MAAM,CAAA,KAAuD;IAC7F,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,MAAM,MAAM,gBAAA;QACZ,OAAO,MAAM,iBAAA;QACb,aAAc,MAAc,uBAAA;QAC5B;QACA,WAAW;IAAA;AAKrB,GACA;IAAE,WAAW;IAAgB,oBAAoB;AAAK;AAGjD,IAAM,2VAAW,YAAA,EACtB,CAAC,EAAE,KAAA,EAAO,SAAA,EAAW,QAAA,EAAU,GAAG,MAAM,CAAA,KAAmD;IACzF,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,GAAAA,yZAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,OAAO,MAAM,aAAA;QACb,SAAS,MAAM,eAAA;QACf,aAAc,MAAc,uBAAA;QAC5B;QACA,WAAW;IAAA;AAKrB,GACA;IAAE,WAAW;IAAY,oBAAoB;AAAK;AAG7C,IAAM,+VAAe,YAAA,EAC1B,CAAC,EAAE,KAAA,EAAO,SAAA,EAAW,QAAA,EAAU,GAAG,MAAM,CAAA,KAAuD;IAC7F,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,OAAO,MAAM,iBAAA;QACb,SAAS,MAAM,mBAAA;QACf,aAAc,MAAc,uBAAA;QAC5B;QACA,WAAW;IAAA;AAKrB,GACA;IAAE,WAAW;IAAgB,oBAAoB;AAAK;AAOjD,IAAM,0VAAU,YAAA,EACrB,CAAC,EAAE,KAAA,EAAO,SAAA,EAAW,QAAA,EAAU,GAAG,MAAM,CAAA,KAAkD;IACxF,MAAM,iBAAiB,yBAAyB,SAAS;IACzD,MAAM,qBAAqB,mBAAmB,eAAe,CAAC,MAAM,MAAA;IAEpE,MAAM,oBAAoB;QACxB,GAAI,sBAAsB,YAAY;YAAE,OAAO;gBAAE,SAAS;YAAO;QAAE,CAAA;IACrE;IAEA,OACE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,gZAAAA,UAAAA,CAAA,QAAA,EAAA,MACG,sBAAsB,UACtB,MAAM,MAAA,IACL,aAAA,iZAAAA,WAAAA,CAAA,aAAA,CAAC,mBAAA;QACC;QACA,OAAO,MAAM,YAAA;QACb,SAAS,MAAM,cAAA;QACf,aAAc,MAAc,uBAAA;QAC5B;QACA,WAAW;IAAA;AAKrB,GACA;IAAE,WAAW;IAAW,oBAAoB;AAAK;;AUlnB5C,IAAM,+VAAe,YAAA,EAC1B,CAAC,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,MAAM,CAAA,KAAiE;IAC5F,MAAM,EACJ,yBAAA,EACA,gBAAA,EACA,mBAAA,EACA,sBAAA,EACA,IAAA,EACA,aAAA,EACA,UAAA,EACA,SAAA,EACA,GAAG,MACL,GAAI;IACJ,WAAW,0BAA0B,UAAU,SAAS;IACxD,MAAM,QAAQ,kBAAkB,QAAQ,EAAE,cAAc;IAExD,MAAM,eAAe,MAAM;QACzB,MAAM,OAAoB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,IAAI,SAAS,SAAS;YACpB,OAAO,MAAM,UAAA,CAAW;gBAAE,GAAG,IAAA;gBAAM,YAAY,MAAM,UAAA;YAAW,CAAC;QACnE;QACA,OAAO,MAAM,gBAAA,CAAiB;YAC5B,GAAG,IAAA;YACH,2BAA2B;YAC3B,wBAAwB;QAC1B,CAAC;IACH;IAEA,MAAM,2BAAoD,OAAM,MAAK;QACnE,IAAI,SAAS,OAAO,UAAU,YAAY,WAAW,OAAO;YAC1D,MAAM,YAAY,MAAM,KAAA,CAAM,OAAO,EAAE,CAAC;QAC1C;QACA,OAAO,aAAa;IACtB;IAEA,MAAM,aAAa;QAAE,GAAG,IAAA;QAAM,SAAS;IAAyB;IAChE,sZAAOG,UAAAA,CAAM,YAAA,CAAa,OAAsC,UAAU;AAC5E,GACA;IAAE,WAAW;IAAgB,oBAAoB;AAAK;;AC/CjD,IAAM,+VAAe,YAAA,EAC1B,CAAC,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,MAAM,CAAA,KAAiE;IAC5F,MAAM,EACJ,mBAAA,EACA,gBAAA,EACA,yBAAA,EACA,sBAAA,EACA,IAAA,EACA,cAAA,EACA,aAAA,EACA,SAAA,EACA,GAAG,MACL,GAAI;IAEJ,WAAW,0BAA0B,UAAU,SAAS;IACxD,MAAM,QAAQ,kBAAkB,QAAQ,EAAE,cAAc;IAExD,MAAM,eAAe,MAAM;QACzB,MAAM,OAAoB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,IAAI,SAAS,SAAS;YACpB,OAAO,MAAM,UAAA,CAAW;gBAAE,GAAG,IAAA;gBAAM,YAAY,MAAM,UAAA;YAAW,CAAC;QACnE;QAEA,OAAO,MAAM,gBAAA,CAAiB;YAC5B,GAAG,IAAA;YACH,2BAA2B;YAC3B,wBAAwB;QAC1B,CAAC;IACH;IAEA,MAAM,2BAAoD,OAAM,MAAK;QACnE,IAAI,SAAS,OAAO,UAAU,YAAY,WAAW,OAAO;YAC1D,MAAM,YAAY,MAAM,KAAA,CAAM,OAAO,EAAE,CAAC;QAC1C;QACA,OAAO,aAAa;IACtB;IAEA,MAAM,aAAa;QAAE,GAAG,IAAA;QAAM,SAAS;IAAyB;IAChE,sZAAOE,UAAAA,CAAM,YAAA,CAAa,OAAsC,UAAU;AAC5E,GACA;IAAE,WAAW;IAAgB,oBAAoB;AAAK;;AC3CjD,IAAM,gWAAgB,YAAA,EAC3B,CAAC,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,MAAM,CAAA,KAAkE;IAC7F,MAAM,EAAE,cAAc,GAAA,EAAK,cAAA,EAAgB,GAAG,KAAK,CAAA,GAAI;IAEvD,WAAW,0BAA0B,UAAU,UAAU;IACzD,MAAM,QAAQ,kBAAkB,QAAQ,EAAE,eAAe;IAEzD,MAAM,eAAe,IAAM,MAAM,OAAA,CAAQ;YAAE;YAAa,GAAG,cAAA;QAAe,CAAC;IAC3E,MAAM,2BAAoD,OAAM,MAAK;QACnE,MAAM,YAAa,MAAc,KAAA,CAAM,OAAO,EAAE,CAAC;QACjD,OAAO,aAAa;IACtB;IAEA,MAAM,aAAa;QAAE,GAAG,IAAA;QAAM,SAAS;IAAyB;IAChE,sZAAOE,UAAAA,CAAM,YAAA,CAAa,OAAsC,UAAU;AAC5E,GACA;IAAE,WAAW;IAAiB,oBAAoB;AAAK;;ACvBlD,IAAM,2WAA2B,YAAA,EACtC,CAAC,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,MAAM,CAAA,KAAoD;IAC/E,MAAM,EAAE,WAAA,EAAa,GAAG,KAAK,CAAA,GAAI;IAEjC,WAAW,0BAA0B,UAAU,uBAAuB;IACtE,MAAM,QAAQ,kBAAkB,QAAQ,EAAE,0BAA0B;IAIpE,MAAM,eAAe,YAAY;QAC/B,eAAe,eAAe;YAC5B,MAAM,MAAM,wBAAA,CAAyB;gBAAE,aAAa,eAAe,KAAA;YAAU,CAAC;QAChF;QACA,KAAK,aAAa;IACpB;IAEA,MAAM,2BAAoD,OAAM,MAAK;QACnE,MAAM,YAAa,MAAc,KAAA,CAAM,OAAO,EAAE,CAAC;QACjD,OAAO,aAAa;IACtB;IAEA,MAAM,aAAa;QAAE,GAAG,IAAA;QAAM,SAAS;IAAyB;IAChE,sZAAOE,UAAAA,CAAM,YAAA,CAAa,OAAsC,UAAU;AAC5E,GACA;IAAE,WAAW;IAAsB,oBAAoB;AAAK;;;;;;;;;;AGmC9D,IAAI,OAAO,WAAW,qBAAA,KAA0B,aAAa;IAC3D,WAAW,qBAAA,GAAwB;AACrC;AAEA,IAAM,eAAe;IACnB,MAAM;IACN,SAAS;IACT,WAAA,EAAa,QAAQ,IAAI;AAC3B;AAzEA,IAAA,SAAA,SAAA,WAAA,iBAAA,WAAA,WAAA,4BAAA;AAgHO,IAAM,mBAAN,MAAM,iBAAiD;IA+H5D,YAAY,OAAA,CAAiC;QA/HxC,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QAIL,IAAA,CAAQ,OAAA,GAAsD;QAC9D,IAAA,CAAQ,aAAA,GAA2C;QACnD,IAAA,CAAQ,uBAAA,GAAoE;QAC5E,IAAA,CAAQ,aAAA,GAAqC;QAC7C,IAAA,CAAQ,eAAA,GAAoD;QAC5D,IAAA,CAAQ,kBAAA,GAA0D;QAClE,IAAA,CAAQ,aAAA,GAAqC;QAC7C,IAAA,CAAQ,kBAAA,GAA+C;QACvD,IAAA,CAAQ,0BAAA,GAA+D;QACvE,IAAA,CAAQ,yBAAA,GAA6D;QACrE,IAAA,CAAQ,eAAA,GAAyC;QACjD,IAAA,CAAQ,mBAAA,GAAsB,aAAA,GAAA,IAAI,IAA6C;QAC/E,IAAA,CAAQ,mBAAA,GAAsB,aAAA,GAAA,IAAI,IAA6C;QAC/E,IAAA,CAAQ,wBAAA,GAA2B,aAAA,GAAA,IAAI,IAAkD;QACzF,IAAA,CAAQ,uBAAA,GAA0B,aAAA,GAAA,IAAI,IAAiD;QACvF,IAAA,CAAQ,gCAAA,GAAmC,aAAA,GAAA,IAAI,IAA0D;QACzG,IAAA,CAAQ,+BAAA,GAAkC,aAAA,GAAA,IAAI,IAAyD;QACvG,IAAA,CAAQ,iCAAA,GAAoC,aAAA,GAAA,IAAI,IAA2D;QAC3G,IAAA,CAAQ,6BAAA,GAAgC,aAAA,GAAA,IAAI,IAAuD;QACnG,IAAA,CAAQ,mBAAA,GAAsB,aAAA,GAAA,IAAI,IAA8C;QAChF,IAAA,CAAQ,qBAAA,GAAwB,aAAA,GAAA,IAAI,IAA+C;QACnF,IAAA,CAAQ,yBAAA,GAA4B,aAAA,GAAA,IAAI,IAAmD;QAC3F,IAAA,CAAQ,oBAAA,GAAuB,aAAA,GAAA,IAAI,IAA8C;QACjF,IAAA,CAAQ,yBAAA,GAA4B,aAAA,GAAA,IAAI,IAA8D;QAEtG,6EAAA;QAAA,IAAA,CAAQ,wBAAA,GAA2B,aAAA,GAAA,IAAI,IAMrC;QACF,IAAA,CAAQ,eAAA,GAAqC,CAAC,CAAA;QAE9C,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAA,SAAuB;QACvB,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACA,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACA,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAA;QACA,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAA,WAAY,iUAAA,CAAoB;QAqIhC,IAAA,CAAA,cAAA,GAAiB,CAAC,SAA0C;YAC1D,MAAM,WAAW,MAAG;gBAhSxB,IAAA;gBAgS2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,cAAA,CAAe,KAAA,KAAS;YAAA;YAC7D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,kBAAkB,QAAQ;YACzD;QACF;QAEA,IAAA,CAAA,cAAA,GAAiB,CAAC,SAA0C;YAC1D,MAAM,WAAW,MAAG;gBAzSxB,IAAA;gBAyS2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,cAAA,CAAe,KAAA,KAAS;YAAA;YAC7D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,kBAAkB,QAAQ;YACzD;QACF;QAEA,IAAA,CAAA,mBAAA,GAAsB,CAAA,GAAI,SAAkE;YAC1F,MAAM,WAAW,MAAG;gBAlTxB,IAAA;gBAkT2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,mBAAA,CAAoB,GAAG,KAAA,KAAS;YAAA;YACrE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,uBAAuB,QAAQ;YAC9D;QACF;QAEA,IAAA,CAAA,mBAAA,GAAsB,CAAA,GAAI,SAAkE;YAC1F,MAAM,WAAW,MAAG;gBA3TxB,IAAA;gBA2T2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,mBAAA,CAAoB,GAAG,KAAA,KAAS;YAAA;YACrE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,uBAAuB,QAAQ;YAC9D;QACF;QAEA,IAAA,CAAA,oBAAA,GAAuB,MAAqB;YAC1C,MAAM,WAAW,MAAG;gBApUxB,IAAA;gBAoU2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,oBAAA,EAAA,KAA0B;YAAA;YAC/D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,wBAAwB,QAAQ;YAC/D;QACF;QAEA,IAAA,CAAA,+BAAA,GAAkC,MAAqB;YACrD,MAAM,WAAW,MAAG;gBA7UxB,IAAA;gBA6U2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,+BAAA,EAAA,KAAqC;YAAA;YAC1E,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,mCAAmC,QAAQ;YAC1E;QACF;QAEA,IAAA,CAAA,sCAAA,GAAyC,MAAqB;YAC5D,MAAM,WAAW,MAAG;gBAtVxB,IAAA;gBAsV2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,sCAAA,EAAA,KAA4C;YAAA;YACjF,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,0CAA0C,QAAQ;YACjF;QACF;QAEA,IAAA,CAAA,mBAAA,GAAsB,MAAqB;YACzC,MAAM,WAAW,MAAG;gBA/VxB,IAAA;gBA+V2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,mBAAA,EAAA,KAAyB;YAAA;YAC9D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,uBAAuB,QAAQ;YAC9D;QACF;QAEA,IAAA,CAAA,0BAAA,GAA6B,MAAqB;YAChD,MAAM,WAAW,MAAG;gBAxWxB,IAAA;gBAwW2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,0BAAA,EAAA,KAAgC;YAAA;YACrE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,8BAA8B,QAAQ;YACrE;QACF;QAEA,IAAA,CAAA,2BAAA,GAA8B,MAAqB;YACjD,MAAM,WAAW,MAAG;gBAjXxB,IAAA;gBAiX2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,2BAAA,EAAA,KAAiC;YAAA;YACtE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,+BAA+B,QAAQ;YACtE;QACF;QAEA,IAAA,CAAA,gBAAA,GAAmB,MAAqB;YACtC,MAAM,WAAW,MAAG;gBA1XxB,IAAA;gBA0X2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,gBAAA,EAAA,KAAsB;YAAA;YAC3D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,oBAAoB,QAAQ;YAC3D;QACF;QAEA,IAAA,CAAA,gBAAA,GAAmB,CAAC,OAA8B;YAChD,MAAM,WAAW,MAAG;gBAnYxB,IAAA;gBAmY2B,OAAA,CAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,gBAAA,CAAiB,GAAA,KAAO;YAAA;YAC7D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,oBAAoB,QAAQ;YAC3D;QACF;QAEA,IAAA,CAAA,qBAAA,GAAwB,YAAY;YAClC,MAAM,WAAW,MAAG;gBA5YxB,IAAA;gBA4Y2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,qBAAA;YAAA;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,KAAK,SAAS;YAChB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,yBAAyB,QAAQ;YAChE;QACF;QAsFA,IAAA,CAAO,EAAA,GAAkB,CAAA,GAAI,SAAS;YAxexC,IAAA;YA0eI,IAAA,CAAI,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,EAAA,EAAI;gBACpB,OAAO,IAAA,CAAK,OAAA,CAAQ,EAAA,CAAG,GAAG,IAAI;YAChC,OAAO;gBACL,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,EAAA,CAAG,GAAG,IAAI;YAC3B;QACF;QAEA,IAAA,CAAO,GAAA,GAAoB,CAAA,GAAI,SAAS;YAjf1C,IAAA;YAmfI,IAAA,CAAI,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,GAAA,EAAK;gBACrB,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,GAAG,IAAI;YACjC,OAAO;gBACL,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,GAAA,CAAI,GAAG,IAAI;YAC5B;QACF;QAKA;;KAAA,GAAA,IAAA,CAAO,WAAA,GAAc,CAAC,OAAmB;YACvC,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,EAAE;YAI5B,IAAI,IAAA,CAAK,MAAA,EAAQ;gBACf,IAAA,CAAK,UAAA,CAAW;YAClB;QACF;QAKA;;KAAA,GAAA,IAAA,CAAO,UAAA,GAAa,MAAM;YACxB,IAAA,CAAK,eAAA,CAAgB,OAAA,CAAQ,CAAA,KAAM,GAAG,CAAC;YACvC,IAAA,CAAK,eAAA,GAAkB,CAAC,CAAA;QAC1B;QAEA,IAAA,CAAQ,UAAA,GAAa,CAAC,YAA6D;YACjF,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM,mCAAmC;YACrD;QACF;QAEA,IAAA,CAAQ,cAAA,GAAiB,CAAC,YAA6D;YArhBzF,IAAA;YAshBI,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM,mCAAmC;YACrD;YAEA,IAAA,CAAK,OAAA,GAAU;YAEf,IAAA,CAAK,mBAAA,CAAoB,OAAA,CAAQ,CAAA,KAAM,GAAG,CAAC;YAC3C,IAAA,CAAK,wBAAA,CAAyB,OAAA,CAAQ,CAAC,kBAAkB,aAAa;gBACpE,iBAAiB,iBAAA,GAAoB,QAAQ,WAAA,CAAY,QAAQ;YACnE,CAAC;YAED,CAAA,KAAA,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,QAAA,CAAS,iBAAA,CAAkB,QAAQ,CAAA,KAAlD,OAAA,KAAA,IAAA,GAAqD,OAAA,CAAQ,CAAA,aAAY;gBAEvE,IAAA,CAAK,EAAA,CAAG,UAAU,UAAU;oBAAE,QAAQ;gBAAK,CAAC;YAC9C;YAEA,IAAI,IAAA,CAAK,aAAA,KAAkB,MAAM;gBAC/B,QAAQ,UAAA,CAAW,IAAA,CAAK,aAAa;YACvC;YAEA,IAAI,IAAA,CAAK,eAAA,KAAoB,MAAM;gBACjC,QAAQ,uBAAA,CAAwB,IAAA,CAAK,eAAe;YACtD;YAEA,IAAI,IAAA,CAAK,kBAAA,KAAuB,MAAM;gBACpC,QAAQ,0BAAA,CAA2B,IAAA,CAAK,kBAAkB;YAC5D;YAEA,IAAI,IAAA,CAAK,aAAA,KAAkB,MAAM;gBAC/B,QAAQ,UAAA,CAAW,IAAA,CAAK,aAAa;YACvC;YAEA,IAAI,IAAA,CAAK,kBAAA,KAAuB,MAAM;gBACpC,QAAQ,eAAA,CAAgB,IAAA,CAAK,kBAAkB;YACjD;YAEA,IAAI,IAAA,CAAK,uBAAA,KAA4B,MAAM;gBACzC,QAAQ,6BAAA,CAA8B,IAAA,CAAK,uBAAuB;YACpE;YAEA,IAAI,IAAA,CAAK,aAAA,KAAkB,MAAM;gBAC/B,QAAQ,gBAAA,CAAiB,IAAA,CAAK,aAAa;YAC7C;YAEA,IAAI,IAAA,CAAK,0BAAA,KAA+B,MAAM;gBAC5C,QAAQ,uBAAA,CAAwB,IAAA,CAAK,0BAA0B;YACjE;YAEA,IAAI,IAAA,CAAK,yBAAA,KAA8B,MAAM;gBAC3C,QAAQ,sBAAA,CAAuB,IAAA,CAAK,yBAAyB;YAC/D;YAEA,IAAI,IAAA,CAAK,eAAA,KAAoB,MAAM;gBACjC,QAAQ,YAAA,CAAa,IAAA,CAAK,eAAe;YAC3C;YAEA,IAAA,CAAK,mBAAA,CAAoB,OAAA,CAAQ,CAAC,OAAO,SAAS;gBAChD,QAAQ,WAAA,CAAY,MAAM,KAAK;YACjC,CAAC;YAED,IAAA,CAAK,mBAAA,CAAoB,OAAA,CAAQ,CAAC,OAAO,SAAS;gBAChD,QAAQ,WAAA,CAAY,MAAM,KAAK;YACjC,CAAC;YAED,IAAA,CAAK,wBAAA,CAAyB,OAAA,CAAQ,CAAC,OAAO,SAAS;gBACrD,QAAQ,gBAAA,CAAiB,MAAM,KAAK;YACtC,CAAC;YAED,IAAA,CAAK,uBAAA,CAAwB,OAAA,CAAQ,CAAC,OAAO,SAAS;gBACpD,QAAQ,eAAA,CAAgB,MAAM,KAAK;YACrC,CAAC;YAED,IAAA,CAAK,6BAAA,CAA8B,OAAA,CAAQ,CAAC,OAAO,SAAS;gBAC1D,QAAQ,qBAAA,CAAsB,MAAM,KAAK;YAC3C,CAAC;YAED,IAAA,CAAK,qBAAA,CAAsB,OAAA,CAAQ,CAAC,OAAO,SAAS;gBAClD,QAAQ,aAAA,CAAc,MAAM,KAAK;YACnC,CAAC;YAED,IAAA,CAAK,yBAAA,CAA0B,OAAA,CAAQ,CAAC,OAAO,SAAS;gBACtD,QAAQ,iBAAA,CAAkB,MAAM,KAAK;YACvC,CAAC;YAED,IAAA,CAAK,oBAAA,CAAqB,OAAA,CAAQ,CAAC,OAAO,SAAS;gBACjD,QAAQ,YAAA,CAAa,MAAM,KAAK;YAClC,CAAC;YAED,IAAA,CAAK,yBAAA,CAA0B,OAAA,CAAQ,CAAC,OAAO,SAAS;gBACtD,QAAQ,4BAAA,CAA6B,MAAM,KAAK;YAClD,CAAC;YAKD,IAAI,OAAO,IAAA,CAAK,OAAA,CAAQ,MAAA,KAAW,aAAa;gBAC9C,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,IAAA,wSAAK,cAAA,CAAY,MAAA,EAAQ,OAAO;YACjD;YAEA,IAAA,CAAK,UAAA,CAAW;YAChB,OAAO,IAAA,CAAK,OAAA;QACd;QAgFA,IAAA,CAAA,uBAAA,GAA0B,OAAO,UAA8B;YAC7D,MAAM,UAAU,sUAAM,kBAAA,EAAA,IAAA,EAAK,4BAAA,mBAAL,IAAA,CAAA,IAAA;YAEtB,IAAI,WAAW,6BAA6B,SAAS;gBACnD,OAAQ,QAAgB,uBAAA,CAAwB,KAAK;YACvD;QACF;QAEA,IAAA,CAAA,6BAAA,GAAgC,OAAO,WAA2C;YAChF,IAAI,IAAA,CAAK,OAAA,EAAS;gBAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,6BAAA,CAA8B,MAAM;YAC1D,OAAO;gBACL,OAAO,QAAQ,MAAA,CAAO;YACxB;QACF;QAKA;;KAAA,GAAA,IAAA,CAAA,SAAA,GAAY,CAAC,WAA2C;YACtD,IAAI,IAAA,CAAK,OAAA,EAAS;gBAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,SAAA,CAAU,MAAM;YACtC,OAAO;gBACL,OAAO,QAAQ,MAAA,CAAO;YACxB;QACF;QAEA,IAAA,CAAA,UAAA,GAAa,CAAC,UAAwB;YACpC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,KAAK;YAC/B,OAAO;gBACL,IAAA,CAAK,aAAA,GAAgB;YACvB;QACF;QAEA,IAAA,CAAA,WAAA,GAAc,MAAM;YAClB,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY;YAC3B,OAAO;gBACL,IAAA,CAAK,aAAA,GAAgB;YACvB;QACF;QAEA,IAAA,CAAA,uBAAA,GAA0B,CAAC,UAAqC;YAC9D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,uBAAA,CAAwB,KAAK;YAC5C,OAAO;gBACL,IAAA,CAAK,eAAA,GAAkB;YACzB;QACF;QAEA,IAAA,CAAA,wBAAA,GAA2B,MAAM;YAC/B,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,wBAAA,CAAyB;YACxC,OAAO;gBACL,IAAA,CAAK,eAAA,GAAkB;YACzB;QACF;QAEA,IAAA,CAAA,0BAAA,GAA6B,CAAC,UAAwC;YACpE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,0BAAA,CAA2B,KAAK;YAC/C,OAAO;gBACL,IAAA,CAAK,kBAAA,GAAqB;YAC5B;QACF;QAEA,IAAA,CAAA,2BAAA,GAA8B,MAAM;YAClC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,2BAAA,CAA4B;YAC3C,OAAO;gBACL,IAAA,CAAK,kBAAA,GAAqB;YAC5B;QACF;QAEA,IAAA,CAAA,6BAAA,GAAgC,CAAC,UAAkD;YACjF,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,6BAAA,CAA8B,KAAK;YAClD,OAAO;gBACL,IAAA,CAAK,uBAAA,GAA0B;YACjC;QACF;QAEA,IAAA,CAAA,8BAAA,GAAiC,MAAM;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,8BAAA,CAA+B;YAC9C,OAAO;gBACL,IAAA,CAAK,uBAAA,GAA0B;YACjC;QACF;QAEA,IAAA,CAAA,gBAAA,GAAmB,CAAC,UAA8B;YAChD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,gBAAA,CAAiB,KAAK;YACrC,OAAO;gBACL,IAAA,CAAK,aAAA,GAAgB;YACvB;QACF;QAEA,IAAA,CAAA,iBAAA,GAAoB,MAAM;YACxB,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,iBAAA,CAAkB;YACjC,OAAO;gBACL,IAAA,CAAK,aAAA,GAAgB;YACvB;QACF;QAEA,IAAA,CAAA,eAAA,GAAkB,CAAC,UAA6B;YAC9C,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,eAAA,CAAgB,KAAK;YACpC,OAAO;gBACL,IAAA,CAAK,kBAAA,GAAqB;YAC5B;QACF;QAEA,IAAA,CAAA,gBAAA,GAAmB,MAAM;YACvB,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,gBAAA,CAAiB;YAChC,OAAO;gBACL,IAAA,CAAK,kBAAA,GAAqB;YAC5B;QACF;QAEA,IAAA,CAAA,uBAAA,GAA0B,CAAC,UAAqC;YAC9D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,uBAAA,CAAwB,KAAK;YAC5C,OAAO;gBACL,IAAA,CAAK,0BAAA,GAA6B;YACpC;QACF;QAEA,IAAA,CAAA,wBAAA,GAA2B,MAAM;YAC/B,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,wBAAA,CAAyB;YACxC,OAAO;gBACL,IAAA,CAAK,0BAAA,GAA6B;YACpC;QACF;QAEA,IAAA,CAAA,sBAAA,GAAyB,CAAC,UAAoC;YAC5D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,sBAAA,CAAuB,KAAK;YAC3C,OAAO;gBACL,IAAA,CAAK,yBAAA,GAA4B;YACnC;QACF;QAEA,IAAA,CAAA,uBAAA,GAA0B,MAAM;YAC9B,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,uBAAA,CAAwB;YACvC,OAAO;gBACL,IAAA,CAAK,yBAAA,GAA4B;YACnC;QACF;QAEA,IAAA,CAAA,YAAA,GAAe,CAAC,UAA0B;YACxC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,YAAA,CAAa,KAAK;YACjC,OAAO;gBACL,IAAA,CAAK,eAAA,GAAkB;YACzB;QACF;QAEA,IAAA,CAAA,aAAA,GAAgB,MAAM;YACpB,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,aAAA,CAAc;YAC7B,OAAO;gBACL,IAAA,CAAK,eAAA,GAAkB;YACzB;QACF;QAEA,IAAA,CAAA,UAAA,GAAa,CAAC,UAAwB;YACpC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,KAAK;YAC/B,OAAO;gBACL,IAAA,CAAK,aAAA,GAAgB;YACvB;QACF;QAEA,IAAA,CAAA,WAAA,GAAc,MAAM;YAClB,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY;YAC3B,OAAO;gBACL,IAAA,CAAK,aAAA,GAAgB;YACvB;QACF;QAEA,IAAA,CAAA,WAAA,GAAc,CAAC,MAAsB,UAAwB;YAC3D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY,MAAM,KAAK;YACtC,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,MAAM,KAAK;YAC1C;QACF;QAEA,IAAA,CAAA,aAAA,GAAgB,CAAC,SAAyB;YACxC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,aAAA,CAAc,IAAI;YACjC,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,MAAA,CAAO,IAAI;YACtC;QACF;QAEA,IAAA,CAAA,WAAA,GAAc,CAAC,MAAsB,UAAwB;YAC3D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY,MAAM,KAAK;YACtC,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,MAAM,KAAK;YAC1C;QACF;QAEA,IAAA,CAAA,aAAA,GAAgB,CAAC,SAAyB;YACxC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,aAAA,CAAc,IAAI;YACjC,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,MAAA,CAAO,IAAI;YACtC;QACF;QAEA,IAAA,CAAA,gBAAA,GAAmB,CAAC,MAAsB,UAA6B;YACrE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,gBAAA,CAAiB,MAAM,KAAK;YAC3C,OAAO;gBACL,IAAA,CAAK,wBAAA,CAAyB,GAAA,CAAI,MAAM,KAAK;YAC/C;QACF;QAEA,IAAA,CAAA,kBAAA,GAAqB,CAAC,SAAyB;YAC7C,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,kBAAA,CAAmB,IAAI;YACtC,OAAO;gBACL,IAAA,CAAK,wBAAA,CAAyB,MAAA,CAAO,IAAI;YAC3C;QACF;QAEA,IAAA,CAAA,wBAAA,GAA2B,CAAC,MAAsB,UAAqC;YACrF,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,wBAAA,CAAyB,MAAM,KAAK;YACnD,OAAO;gBACL,IAAA,CAAK,gCAAA,CAAiC,GAAA,CAAI,MAAM,KAAK;YACvD;QACF;QAEA,IAAA,CAAA,0BAAA,GAA6B,CAAC,SAAyB;YACrD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,0BAAA,CAA2B,IAAI;YAC9C,OAAO;gBACL,IAAA,CAAK,gCAAA,CAAiC,MAAA,CAAO,IAAI;YACnD;QACF;QAEA,IAAA,CAAA,uBAAA,GAA0B,CAAC,MAAsB,UAAoC;YACnF,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,uBAAA,CAAwB,MAAM,KAAK;YAClD,OAAO;gBACL,IAAA,CAAK,+BAAA,CAAgC,GAAA,CAAI,MAAM,KAAK;YACtD;QACF;QAEA,IAAA,CAAA,yBAAA,GAA4B,CAAC,SAAyB;YACpD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,yBAAA,CAA0B,IAAI;YAC7C,OAAO;gBACL,IAAA,CAAK,+BAAA,CAAgC,MAAA,CAAO,IAAI;YAClD;QACF;QAEA,IAAA,CAAA,yBAAA,GAA4B,CAAC,MAAsB,UAAsC;YACvF,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,yBAAA,CAA0B,MAAM,KAAK;YACpD,OAAO;gBACL,IAAA,CAAK,iCAAA,CAAkC,GAAA,CAAI,MAAM,KAAK;YACxD;QACF;QAEA,IAAA,CAAA,2BAAA,GAA8B,CAAC,SAAyB;YACtD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,2BAAA,CAA4B,IAAI;YAC/C,OAAO;gBACL,IAAA,CAAK,iCAAA,CAAkC,MAAA,CAAO,IAAI;YACpD;QACF;QAEA,IAAA,CAAA,2CAAA,GAA8C,MAAM;YAClD,MAAM,WAAW,MAAG;gBAv+BxB,IAAA;gBAu+B2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,2CAAA;YAAA;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,KAAK,SAAS;YAChB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,+CAA+C,QAAQ;YACtF;QACF;QAEA,IAAA,CAAA,qBAAA,GAAwB,CAAC,MAAsB,UAAkC;YAC/E,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,qBAAA,CAAsB,MAAM,KAAK;YAChD,OAAO;gBACL,IAAA,CAAK,6BAAA,CAA8B,GAAA,CAAI,MAAM,KAAK;YACpD;QACF;QAEA,IAAA,CAAA,uBAAA,GAA0B,CAAC,SAAyB;YAClD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,uBAAA,CAAwB,IAAI;YAC3C,OAAO;gBACL,IAAA,CAAK,6BAAA,CAA8B,MAAA,CAAO,IAAI;YAChD;QACF;QAEA,IAAA,CAAA,eAAA,GAAkB,CAAC,MAAsB,oBAAsC;YAC7E,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,eAAA,CAAgB,MAAM,eAAe;YACpD,OAAO;gBACL,IAAA,CAAK,uBAAA,CAAwB,GAAA,CAAI,MAAM,eAAe;YACxD;QACF;QAEA,IAAA,CAAA,iBAAA,GAAoB,CAAC,SAAyB;YAC5C,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,iBAAA,CAAkB,IAAI;YACrC,OAAO;gBACL,IAAA,CAAK,uBAAA,CAAwB,MAAA,CAAO,IAAI;YAC1C;QACF;QAEA,IAAA,CAAA,aAAA,GAAgB,CAAC,MAAsB,UAA0B;YAC/D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,aAAA,CAAc,MAAM,KAAK;YACxC,OAAO;gBACL,IAAA,CAAK,qBAAA,CAAsB,GAAA,CAAI,MAAM,KAAK;YAC5C;QACF;QAEA,IAAA,CAAA,eAAA,GAAkB,CAAC,SAAyB;YAC1C,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,eAAA,CAAgB,IAAI;YACnC,OAAO;gBACL,IAAA,CAAK,qBAAA,CAAsB,MAAA,CAAO,IAAI;YACxC;QACF;QAEA,IAAA,CAAA,iBAAA,GAAoB,CAAC,MAAsB,UAA8B;YACvE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,iBAAA,CAAkB,MAAM,KAAK;YAC5C,OAAO;gBACL,IAAA,CAAK,yBAAA,CAA0B,GAAA,CAAI,MAAM,KAAK;YAChD;QACF;QAEA,IAAA,CAAA,mBAAA,GAAsB,CAAC,SAAyB;YAC9C,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,mBAAA,CAAoB,IAAI;YACvC,OAAO;gBACL,IAAA,CAAK,yBAAA,CAA0B,MAAA,CAAO,IAAI;YAC5C;QACF;QAEA,IAAA,CAAA,YAAA,GAAe,CAAC,MAAsB,UAA+B;YACnE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,YAAA,CAAa,MAAM,KAAK;YACvC,OAAO;gBACL,IAAA,CAAK,oBAAA,CAAqB,GAAA,CAAI,MAAM,KAAK;YAC3C;QACF;QAEA,IAAA,CAAA,cAAA,GAAiB,CAAC,SAA+B;YAC/C,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,cAAA,CAAe,IAAI;YAClC,OAAO;gBACL,IAAA,CAAK,oBAAA,CAAqB,MAAA,CAAO,IAAI;YACvC;QACF;QAEA,IAAA,CAAA,4BAAA,GAA+B,CAAC,MAAsB,UAAyC;YAC7F,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,4BAAA,CAA6B,MAAM,KAAK;YACvD,OAAO;gBACL,IAAA,CAAK,yBAAA,CAA0B,GAAA,CAAI,MAAM,KAAK;YAChD;QACF;QAEA,IAAA,CAAA,8BAAA,GAAiC,CAAC,SAAyB;YACzD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,IAAA,CAAK,OAAA,CAAQ,8BAAA,CAA+B,IAAI;YAClD,OAAO;gBACL,IAAA,CAAK,yBAAA,CAA0B,MAAA,CAAO,IAAI;YAC5C;QACF;QAEA,IAAA,CAAA,WAAA,GAAc,CAAC,aAAoD;YACjE,IAAI,IAAA,CAAK,OAAA,EAAS;gBAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY,QAAQ;YAC1C,OAAO;gBACL,MAAM,cAAc,MAAM;oBAnlChC,IAAA;oBAolCQ,MAAM,mBAAmB,IAAA,CAAK,wBAAA,CAAyB,GAAA,CAAI,QAAQ;oBACnE,IAAI,kBAAkB;wBACpB,CAAA,KAAA,iBAAiB,iBAAA,KAAjB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA;wBACA,IAAA,CAAK,wBAAA,CAAyB,MAAA,CAAO,QAAQ;oBAC/C;gBACF;gBACA,IAAA,CAAK,wBAAA,CAAyB,GAAA,CAAI,UAAU;oBAAE;oBAAa,mBAAmB,KAAA;gBAAU,CAAC;gBACzF,OAAO;YACT;QACF;QAEA,IAAA,CAAA,QAAA,GAAW,CAAC,OAAe;YACzB,MAAM,WAAW,MAAG;gBAhmCxB,IAAA;gBAgmC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,QAAA,CAAS;YAAA;YAC9C,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,KAAK,SAAS;YAChB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,YAAY,QAAQ;YACnD;QACF;QAEA,IAAA,CAAA,gBAAA,GAAmB,OAAA,GAAU,SAAgD;YAC3E,MAAM,WAAW,MAAG;gBAzmCxB,IAAA;gBAymC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,gBAAA,CAAiB,GAAG;YAAA;YACzD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,oBAAoB,QAAQ;gBACzD;YACF;QACF;QAEA,IAAA,CAAA,gBAAA,GAAmB,OAAO,SAAiC;YACzD,MAAM,WAAW,MAAG;gBAnnCxB,IAAA;gBAmnC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,gBAAA,CAAiB;YAAA;YACtD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,oBAAoB,QAAQ;gBACzD;YACF;QACF;QAEA,IAAA,CAAA,gBAAA,GAAmB,OAAO,SAAiC;YACzD,MAAM,WAAW,MAAG;gBA7nCxB,IAAA;gBA6nC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,gBAAA,CAAiB;YAAA;YACtD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,oBAAoB,QAAQ;gBACzD;YACF;QACF;QAEA,IAAA,CAAA,qBAAA,GAAwB,YAAY;YAClC,MAAM,WAAW,MAAG;gBAvoCxB,IAAA;gBAuoC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,qBAAA;YAAA;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,yBAAyB,QAAQ;gBAC9D;YACF;QACF;QAEA,IAAA,CAAA,qBAAA,GAAwB,MAAY;YAClC,MAAM,WAAW,MAAG;gBAjpCxB,IAAA;gBAipC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,qBAAA;YAAA;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,yBAAyB,QAAQ;YAChE;QACF;QAEA,IAAA,CAAA,qBAAA,GAAwB,MAAM;YAC5B,MAAM,WAAW,MAAG;gBA1pCxB,IAAA;gBA0pC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,qBAAA;YAAA;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,SAAS;YACX,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,yBAAyB,QAAQ;YAChE;QACF;QAEA,IAAA,CAAA,sBAAA,GAAyB,MAAM;YAC7B,MAAM,WAAW,MAAG;gBAnqCxB,IAAA;gBAmqC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,sBAAA;YAAA;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,SAAS;YACX,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,0BAA0B,QAAQ;YACjE;QACF;QAEA,IAAA,CAAA,6BAAA,GAAgC,YAAY;YAC1C,MAAM,WAAW,MAAG;gBA5qCxB,IAAA;gBA4qC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,6BAAA;YAAA;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,iCAAiC,QAAQ;gBACtE;YACF;QACF;QAEA,IAAA,CAAA,4BAAA,GAA+B,YAAY;YACzC,MAAM,WAAW,MAAG;gBAtrCxB,IAAA;gBAsrC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,4BAAA;YAAA;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,gCAAgC,QAAQ;gBACrE;YACF;QACF;QAEA,IAAA,CAAA,kBAAA,GAAqB,YAAY;YAC/B,MAAM,WAAW,MAAG;gBAhsCxB,IAAA;gBAgsC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,kBAAA;YAAA;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,sBAAsB,QAAQ;gBAC3D;YACF;QACF;QAEA,IAAA,CAAA,sBAAA,GAAyB,OAAO,WAAqD;YAzsCvF,IAAA;YA0sCI,MAAM,WAAW,MAAG;gBA1sCxB,IAAAG;gBA0sC2B,OAAA,CAAAA,MAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAAA,IAAc,sBAAA,CAAuB;YAAA;YAC5D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,KAAA,CAAA,CAAK,KAAA,SAAS,CAAA,KAAT,OAAA,KAAA,IAAA,GAAY,KAAA,CAAM,KAQvB,CAR6B,CAQ7B;YACF,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,0BAA0B,QAAQ;YACjE;QACF;QAEA,IAAA,CAAA,0BAAA,GAA6B,OAC3B,YACA,WACkB;YA7tCtB,IAAA;YA8tCI,MAAM,WAAW,MAAG;gBA9tCxB,IAAAA;gBA8tC2B,OAAA,CAAAA,MAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAAA,IAAc,0BAAA,CAA2B,YAAY;YAAA;YAC5E,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,KAAA,CAAA,CAAK,KAAA,SAAS,CAAA,KAAT,OAAA,KAAA,IAAA,GAAY,KAAA,CAAM,KAQvB,CAR6B,CAQ7B;YACF,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,8BAA8B,QAAQ;YACrE;QACF;QAEA,IAAA,CAAA,2BAAA,GAA8B,OAAO,WAA8C;YACjF,MAAM,WAAW,MAAG;gBA/uCxB,IAAA;gBA+uC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,2BAAA,CAA4B;YAAA;YACjE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,+BAA+B,QAAQ;YACtE;QACF;QAEA,IAAA,CAAA,wBAAA,GAA2B,OAAO,WAA4C;YAC5E,MAAM,WAAW,MAAG;gBAxvCxB,IAAA;gBAwvC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,wBAAA,CAAyB;YAAA;YAC9D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,4BAA4B,QAAQ;YACnE;QACF;QAEA,IAAA,CAAA,8BAAA,GAAiC,OAAO,WAAkD;YACxF,MAAM,WAAW,MAAG;gBAjwCxB,IAAA;gBAiwC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,8BAAA,CAA+B;YAAA;YACpE,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,kCAAkC,QAAQ;YACzE;QACF;QAEA,IAAA,CAAA,yBAAA,GAA4B,OAAO,WAA6C;YAC9E,MAAM,WAAW,MAAG;gBA1wCxB,IAAA;gBA0wC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,yBAAA,CAA0B;YAAA;YAC/D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,6BAA6B,QAAQ;YACpE;QACF;QAEA,IAAA,CAAA,oBAAA,GAAuB,OAAO,WAA4C;YACxE,MAAM,WAAW,MAAG;gBAnxCxB,IAAA;gBAmxC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,oBAAA,CAAqB;YAAA;YAC1D,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,wBAAwB,QAAQ;YAC/D;QACF;QAEA,IAAA,CAAA,4BAAA,GAA+B,OAAO,WAA+C;YACnF,MAAM,UAAU,sUAAM,kBAAA,EAAA,IAAA,EAAK,4BAAA,mBAAL,IAAA,CAAA,IAAA;YACtB,OAAO,QAAQ,4BAAA,CAA6B,MAAM;QACpD;QAEA,IAAA,CAAA,kBAAA,GAAqB,OAAO,WAA2E;YACrG,MAAM,WAAW,MAAG;gBAjyCxB,IAAA;gBAiyC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,kBAAA,CAAmB;YAAA;YACxD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,sBAAsB,QAAQ;YAC7D;QACF;QAEA,IAAA,CAAA,eAAA,GAAkB,OAAO,mBAAiE;YACxF,MAAM,WAAW,MAAG;gBA1yCxB,IAAA;gBA0yC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,eAAA,CAAgB;YAAA;YACrD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,mBAAmB,QAAQ;YAC1D;QACF;QAEA,IAAA,CAAA,YAAA,GAAe,OAAO,WAAiE;YACrF,MAAM,WAAW,MAAG;gBAnzCxB,IAAA;gBAmzC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,YAAA,CAAa;YAAA;YAClD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,gBAAgB,QAAQ;YACvD;QACF;QAEA,IAAA,CAAA,OAAA,GAAU,OAAA,GAAU,SAAuC;YACzD,MAAM,WAAW,MAAG;gBA5zCxB,IAAA;gBA4zC2B,OAAA,CAAA,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,OAAA,CAAQ,GAAG;YAAA;YAChD,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,EAAQ;gBAC/B,OAAO,SAAS;YAClB,OAAO;gBACL,IAAA,CAAK,mBAAA,CAAoB,GAAA,CAAI,WAAW,QAAQ;YAClD;QACF;QAllCE,MAAM,EAAE,QAAQ,IAAA,EAAM,cAAA,CAAe,CAAA,GAAI,WAAW,CAAC;QACrD,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,iBAAkB;QACvB,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAY,WAAA,OAAA,KAAA,IAAA,QAAS,QAAA;QAC1B,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,SAAU,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA;QACxB,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,IAAA,kTAAO,YAAA,CAAU,KAAI,YAAY;QAEtC,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,WAAA,EAAa;YAC7B,IAAA,CAAK,OAAA,CAAQ,WAAA,GAAc;QAC7B;QACA,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,IAAA,wSAAK,cAAA,CAAY,MAAA,EAAQ,SAAS;QACjD,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,aAAA,CAAc,qTAAA,CAAY,MAAA,EAAQ,CAAA,yUAAW,eAAA,EAAA,IAAA,EAAK,SAAU,OAAO;QAElF,QAAI,2UAAA,EAAA,IAAA,EAAK,kBAAiB;YACxB,KAAK,IAAA,CAAK,WAAA,CAAY;QACxB;IACF;IArGA,IAAI,iBAAyB;QAC3B,QAAO,8UAAA,EAAA,IAAA,EAAK;IACd;IAEA,IAAI,SAAkB;QAhKxB,IAAA;QAiKI,OAAA,CAAA,CAAO,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,MAAA,KAAU;IACjC;IAEA,IAAI,SAAsB;QApK5B,IAAA;QAwKI,IAAI,CAAC,IAAA,CAAK,OAAA,EAAS;YACjB,uUAAO,eAAA,EAAA,IAAA,EAAK;QACd;QACA,OAAA,CAAA,CACE,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,MAAA,KAAA;;;;;KAAA,GAAA,CAOb,IAAA,CAAK,OAAA,CAAQ,MAAA,GAAS,UAAU,SAAA;IAErC;IAIA,OAAO,oBAAoB,OAAA,EAAiC;QAK1D,IACE,KAAC,uTAAA,CAAU,MACX,iUAAC,eAAA,EAAA,IAAA,EAAK,cACL,QAAQ,KAAA,oUAAS,eAAA,EAAA,IAAA,EAAK,WAAU,KAAA,KAAU,QAAQ,KAAA,IAAA,uCAAA;wUAEnD,eAAA,EAAA,IAAA,EAAK,WAAU,cAAA,KAAmB,QAAQ,cAAA,EAC1C;YACA,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAY,IAAI,iBAAgB,OAAO;QAC9C;QACA,uUAAO,eAAA,EAAA,IAAA,EAAK;IACd;IAEA,OAAO,gBAAgB;QACrB,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAY;IACnB;IAEA,IAAI,SAAiB;QAGnB,IAAI,OAAO,WAAW,eAAe,OAAO,QAAA,EAAU;YACpD,sTAAO,kBAAA,kUAAgB,eAAA,EAAA,IAAA,EAAK,UAAS,IAAI,IAAI,OAAO,QAAA,CAAS,IAAI,GAAG,EAAE;QACxE;QACA,IAAI,uUAAO,eAAA,EAAA,IAAA,EAAK,aAAY,YAAY;YACtC,mVAAO,eAAA,CAAa,KAAA,6UAAM,gDAA6C;QACzE;QACA,QAAO,8UAAA,EAAA,IAAA,EAAK,YAAW;IACzB;IAEA,IAAI,WAAmB;QAGrB,IAAI,OAAO,WAAW,eAAe,OAAO,QAAA,EAAU;YACpD,OAAO,iUAAA,kUAAgB,eAAA,EAAA,IAAA,EAAK,YAAW,IAAI,IAAI,OAAO,QAAA,CAAS,IAAI,GAAG,EAAE;QAC1E;QACA,IAAI,uUAAO,eAAA,EAAA,IAAA,EAAK,eAAc,YAAY;YACxC,mVAAO,eAAA,CAAa,KAAA,6UAAM,gDAA6C;QACzE;QACA,uUAAO,eAAA,EAAA,IAAA,EAAK,cAAa;IAC3B;IAAA;;;;GAAA,GAOO,qBAAmD,GAAA,EAAqC;QA3OjG,IAAA,IAAA;QA4OI,OAAA,CAAA,CAAO,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,oBAAA,IAAA,CAAuB,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,oBAAA,CAAqB,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAG,CAAA;IACxG;IAsBA,IAAI,cAAc;QAnQpB,IAAA;QAoQI,OAAA,CAAA,CAAO,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,WAAA,KAAe,IAAA,CAAK,OAAA,CAAQ,WAAA,IAAe,KAAA;IAClE;IAEA,IAAI,eAAe;QAvQrB,IAAA;QAwQI,OAAA,CAAO,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,YAAA;IACvB;IAEA,IAAI,cAAc;QA3QpB,IAAA;QA4QI,OAAA,CAAA,CAAO,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,WAAA,KAAe;IACtC;IAEA,IAAI,oBAAoB;QA/Q1B,IAAA;QAgRI,OAAA,CAAA,CAAO,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,iBAAA,KAAqB,IAAA,CAAK,OAAA,CAAQ,eAAA,IAAmB;IAC5E;IAEA,IAAI,cAAc;QAGhB,IAAI,OAAO,WAAW,eAAe,OAAO,QAAA,EAAU;YACpD,sTAAO,kBAAA,EAAgB,IAAA,CAAK,OAAA,CAAQ,WAAA,EAAa,IAAI,IAAI,OAAO,QAAA,CAAS,IAAI,GAAG,KAAK;QACvF;QACA,IAAI,OAAO,IAAA,CAAK,OAAA,CAAQ,WAAA,KAAgB,YAAY;YAClD,mVAAO,eAAA,CAAa,KAAA,6UAAM,gDAA6C;QACzE;QACA,OAAO;IACT;IA8HA,MAAM,cAAwE;QA3ZhF,IAAA;QA4ZI,IAAI,IAAA,CAAK,IAAA,KAAS,aAAa,IAAA,CAAK,MAAA,EAAQ;YAC1C;QACF;QAYA,IAAI,OAAO,WAAW,aAAa;YACjC,OAAO,uBAAA,IAA0B,8UAAA,EAAA,IAAA,EAAK;YACtC,OAAO,iBAAA,GAAoB,IAAA,CAAK,QAAA;YAChC,OAAO,cAAA,GAAiB,IAAA,CAAK,MAAA;QAC/B;QAEA,IAAI;YACF,IAAI,IAAA,CAAK,KAAA,EAAO;gBAEd,IAAI;gBAEJ,IAAI,cAAyE,IAAA,CAAK,KAAK,GAAG;oBAExF,IAAI,IAAI,IAAA,CAAK,KAAA,iUAAM,eAAA,EAAA,IAAA,EAAK,kBAAiB;wBACvC,UAAU,IAAA,CAAK,QAAA;wBACf,QAAQ,IAAA,CAAK,MAAA;oBACf,CAAQ;oBAER,IAAA,CAAK,UAAA,CAAW,CAAC;oBACjB,MAAM,EAAE,IAAA,CAAK,IAAA,CAAK,OAAO;gBAC3B,OAAO;oBAEL,IAAI,IAAA,CAAK,KAAA;oBACT,IAAI,CAAC,EAAE,MAAA,EAAQ;wBACb,IAAA,CAAK,UAAA,CAAW,CAAC;wBACjB,MAAM,EAAE,IAAA,CAAK,IAAA,CAAK,OAAO;oBAC3B;gBACF;gBAEA,OAAO,KAAA,GAAQ;YACjB,OAAA,IAAW,CAAC,uBAAuB;gBAEjC,IAAI,CAAC,OAAO,KAAA,EAAO;oBACjB,OAAM,kUAAA,EAAkB;wBACtB,GAAG,IAAA,CAAK,OAAA;wBACR,gVAAgB,eAAA,EAAA,IAAA,EAAK;wBACrB,UAAU,IAAA,CAAK,QAAA;wBACf,QAAQ,IAAA,CAAK,MAAA;wBACb,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAA;oBACtB,CAAC;gBACH;gBAEA,IAAI,CAAC,OAAO,KAAA,EAAO;oBACjB,MAAM,IAAI,MAAM,+DAA+D;gBACjF;gBAEA,IAAA,CAAK,UAAA,CAAW,OAAO,KAAK;gBAC5B,MAAM,OAAO,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,OAAO;YACtC;YAEA,IAAA,CAAI,KAAA,OAAO,KAAA,KAAP,OAAA,KAAA,IAAA,GAAc,MAAA,EAAQ;gBACxB,OAAO,IAAA,CAAK,cAAA,CAAe,OAAO,KAAK;YACzC;YACA;QACF,EAAA,OAAS,KAAK;YACZ,MAAM,QAAQ;YACd,CAAA,GAAA,2TAAA,CAAA,eAAA,EAAA,IAAA,EAAK,WAAU,IAAA,wSAAK,cAAA,CAAY,MAAA,EAAQ,OAAO;YAC/C,QAAQ,KAAA,CAAM,MAAM,KAAA,IAAS,MAAM,OAAA,IAAW,KAAK;YACnD;QACF;IACF;IAuJA,IAAI,UAAU;QA7nBhB,IAAA;QA8nBI,OAAA,CAAO,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,OAAA;IACvB;IAEA,IAAI,SAAqC;QACvC,IAAI,IAAA,CAAK,OAAA,EAAS;YAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,MAAA;QAEtB,OAAO;YACL,OAAO,KAAA;QACT;IACF;IAEA,IAAI,UAAU;QACZ,IAAI,IAAA,CAAK,OAAA,EAAS;YAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAA;QACtB,OAAO;YACL,OAAO,KAAA;QACT;IACF;IAEA,IAAI,OAAO;QACT,IAAI,IAAA,CAAK,OAAA,EAAS;YAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA;QACtB,OAAO;YACL,OAAO,KAAA;QACT;IACF;IAEA,IAAI,eAAe;QACjB,IAAI,IAAA,CAAK,OAAA,EAAS;YAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,YAAA;QACtB,OAAO;YACL,OAAO,KAAA;QACT;IACF;IAEA,IAAI,YAAY;QACd,IAAI,IAAA,CAAK,OAAA,EAAS;YAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,SAAA;QACtB,OAAO;YACL,OAAO,KAAA;QACT;IACF;IAEA,IAAI,0BAA+B;QACjC,IAAI,IAAA,CAAK,OAAA,EAAS;YAChB,OAAQ,IAAA,CAAK,OAAA,CAAgB,uBAAA;QAE/B,OAAO;YACL,OAAO,KAAA;QACT;IACF;IAEA,IAAI,aAAsB;QACxB,IAAI,IAAA,CAAK,OAAA,EAAS;YAChB,OAAO,IAAA,CAAK,OAAA,CAAQ,UAAA;QACtB,OAAO;YACL,OAAO;QACT;IACF;IAEA,IAAI,UAAgD;QA3rBtD,IAAA;QA4rBI,OAAA,CAAO,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,OAAA;IACvB;IAEA,IAAI,UAAwC;QA/rB9C,IAAA;QAgsBI,OAAA,CAAO,KAAA,IAAA,CAAK,OAAA,KAAL,OAAA,KAAA,IAAA,GAAc,OAAA;IACvB;IAEA,2BAAA,GAA8B,IAAA,EAAiB;QAC7C,IAAI,IAAA,CAAK,OAAA,IAAW,gCAAgC,IAAA,CAAK,OAAA,EAAS;YAC/D,IAAA,CAAK,OAAA,CAAgB,0BAAA,CAA2B,IAAI;QACvD,OAAO;YACL,OAAO,KAAA;QACT;IACF;AA0nBF;AA7qCE,UAAA,IAAA;AACA,UAAA,IAAA;AACA,YAAA,IAAA;AACA,kBAAA,IAAA;AACA,YAAA,IAAA;AA6BO,YAAA,IAAA;AAvEF,6BAAA,IAAA;AAoSL,oBAAe,WAAiD;IAC9D,OAAO,IAAI,QAA6C,CAAA,YAAW;QAEjE,IAAA,CAAK,WAAA,CAAY,IAAM,QAAQ,IAAA,CAAK,OAAQ,CAAC;IAC/C,CAAC;AACH;gUAlOA,eAAA,EAvEW,kBAuEJ;AAvEF,IAAM,kBAAN;;AD9FA,SAAS,qBAAqB,KAAA,EAA6B;IAChE,MAAM,EAAE,sBAAA,EAAwB,YAAA,EAAc,QAAA,CAAS,CAAA,GAAI;IAC3D,MAAM,EAAE,iBAAiB,KAAA,EAAO,WAAA,CAAY,CAAA,GAAI,yBAAyB,sBAAsB;IAE/F,MAAM,CAAC,OAAO,QAAQ,CAAA,kZAAIC,UAAAA,CAAM,QAAA,CAAoC;QAClE,QAAQ,MAAM,MAAA;QACd,SAAS,MAAM,OAAA;QACf,MAAM,MAAM,IAAA;QACZ,cAAc,MAAM,YAAA;IACtB,CAAC;mZAEDA,UAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,OAAO,MAAM,WAAA,CAAY,CAAA,IAAK,SAAS;gBAAE,GAAG,CAAA;YAAE,CAAC,CAAC;IAClD,GAAG,CAAC,CAAC;IAEL,MAAM,8TAAe,cAAA,EAAY,MAAM,MAAA,EAAQ,OAAO,YAAY;IAClE,MAAM,WAAWA,yZAAAA,CAAM,OAAA,CACrB,IAAA,CAAO;YAAE,OAAO;QAAM,CAAA,GACtB;QAAA,mDAAA;QAEE;KACF;IAEF,MAAM,2ZAAYA,UAAAA,CAAM,OAAA,CAAQ,IAAA,CAAO;YAAE,OAAO,MAAM,MAAA;QAAO,CAAA,GAAI;QAAC,MAAM,MAAM;KAAC;IAE/E,MAAM,EACJ,SAAA,EACA,aAAA,EACA,aAAA,EACA,OAAA,EACA,MAAA,EACA,IAAA,EACA,KAAA,EACA,KAAA,EACA,YAAA,EACA,OAAA,EACA,OAAA,EACA,cAAA,EACA,qBAAA,EACF,GAAI;IAEJ,MAAM,yZAAUA,UAAAA,CAAM,OAAA,CAAQ,MAAM;QAClC,MAAM,QAAQ;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB,GAAG;QAAC;QAAW;QAAe;QAAQ;QAAO;QAAO;QAAS;QAAS;QAAuB,iBAAA,OAAA,KAAA,IAAA,cAAe,KAAK;KAAC;IAElH,MAAM,4ZAAaA,UAAAA,CAAM,OAAA,CAAQ,IAAA,CAAO;YAAE,OAAO;QAAQ,CAAA,GAAI;QAAC;QAAW,OAAO;KAAC;IACjF,MAAM,yZAAUA,UAAAA,CAAM,OAAA,CAAQ,IAAA,CAAO;YAAE,OAAO;QAAK,CAAA,GAAI;QAAC;QAAQ,IAAI;KAAC;IACrE,MAAM,iaAAkBA,UAAAA,CAAM,OAAA,CAAQ,MAAM;QAC1C,MAAM,QAAQ;YACZ;QACF;QACA,OAAO;YAAE;QAAM;IACjB,GAAG;QAAC;QAAO,YAAY;KAAC;IAExB,OAAA,iGAAA;IAEE,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,qWAAA,CAAuB,QAAA,EAAvB;QAAgC,OAAO;IAAA,GACtC,aAAA,kZAAAA,UAAAA,CAAA,aAAA,ySAAC,gBAAA,CAAc,QAAA,EAAd;QAAuB,OAAO;IAAA,GAC7B,aAAA,iZAAAA,WAAAA,CAAA,aAAA,ySAAC,iBAAA,CAAe,QAAA,EAAf;QAAwB,OAAO;IAAA,GAC9B,aAAA,kZAAAA,UAAAA,CAAA,aAAA,CAAC,+TAAA,EAAA;QAAsB,GAAG,gBAAgB,KAAA;IAAA,GACxC,aAAA,kZAAAA,UAAAA,CAAA,aAAA,6UAAC,cAAA,CAAY,QAAA,EAAZ;QAAqB,OAAO;IAAA,GAC3B,aAAA,iZAAAA,WAAAA,CAAA,aAAA,ySAAC,cAAA,CAAY,QAAA,EAAZ;QAAqB,OAAO;IAAA,GAAU,QAAS,CAClD,CACF,CACF,CACF,CACF;AAEJ;AAEA,IAAM,2BAA2B,CAAC,YAAoC;IACpE,MAAM,oaAAqBA,UAAAA,CAAM,MAAA,CAAO,gBAAgB,mBAAA,CAAoB,OAAO,CAAC;IACpF,MAAM,CAAC,aAAa,cAAc,CAAA,kZAAIA,UAAAA,CAAM,QAAA,CAAS,mBAAmB,OAAA,CAAQ,MAAM;kZAEtFA,WAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,KAAK,mBAAmB,OAAA,CAAQ,uBAAA,CAAwB;YAAE,YAAY,QAAQ,UAAA;QAAW,CAAC;IAC5F,GAAG;QAAC,QAAQ,UAAU;KAAC;mZAEvBA,UAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,KAAK,mBAAmB,OAAA,CAAQ,uBAAA,CAAwB;YAAE;QAAQ,CAAC;IACrE,GAAG;QAAC,QAAQ,YAAY;KAAC;IAEzBA,yZAAAA,CAAM,SAAA,CAAU,MAAM;QACpB,mBAAmB,OAAA,CAAQ,EAAA,CAAG,UAAU,cAAc;QACtD,OAAO,MAAM;YACX,IAAI,mBAAmB,OAAA,EAAS;gBAC9B,mBAAmB,OAAA,CAAQ,GAAA,CAAI,UAAU,cAAc;YACzD;YACA,gBAAgB,aAAA,CAAc;QAChC;IACF,GAAG,CAAC,CAAC;IAEL,OAAO;QAAE,iBAAiB,mBAAmB,OAAA;QAAS;IAAY;AACpE;;ADlHA,SAAS,kBAAkB,KAAA,EAA2B;IACpD,MAAM,EAAE,YAAA,EAAc,QAAA,EAAU,sCAAA,EAAwC,GAAG,2BAA2B,CAAA,GAAI;IAC1G,MAAM,EAAE,iBAAiB,EAAA,EAAI,OAAO,oBAAA,CAAqB,CAAA,GAAI;IAE7D,IAAI,CAAC,wBAAwB,CAAC,wCAAwC;QACpE,IAAI,CAAC,gBAAgB;YACnB,2UAAA,CAAA,eAAA,CAAa,+BAAA,CAAgC;QAC/C,OAAA,IAAW,kBAAkB,CAAC,kUAAA,EAAiB,cAAc,GAAG;YAC9D,2UAAA,CAAA,eAAA,CAAa,+BAAA,CAAgC;gBAAE,KAAK;YAAe,CAAC;QACtE;IACF;IAEA,OACE,aAAA,kZAAAC,UAAAA,CAAA,aAAA,CAAC,sBAAA;QACC;QACA,wBAAwB;IAAA,GAEvB;AAGP;AAEA,IAAM,gBAAgB,6BAA6B,mBAAmB,6VAAiB,8BAA2B;AAElH,cAAc,WAAA,GAAc;;gVfrB5B,yBAAA,EAAuB;IAAE,aAAa;AAAa,CAAC;+SACpD,oCAAA,EAAkC,oBAAY", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18], "debugId": null}}, {"offset": {"line": 2921, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/hooks/useRoutingProps.ts", "file:///Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/node_modules/.pnpm/%40clerk%2Bclerk-react%405.32.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/%40clerk/clerk-react/src/internal.ts"], "sourcesContent": ["import type { RoutingOptions } from '@clerk/types';\n\nimport { errorThrower } from '../errors/errorThrower';\nimport { incompatibleRoutingWithPathProvidedError, noPathProvidedError } from '../errors/messages';\n\nexport function useRoutingProps<T extends RoutingOptions>(\n  componentName: string,\n  props: T,\n  routingOptions?: RoutingOptions,\n): T {\n  const path = props.path || routingOptions?.path;\n  const routing = props.routing || routingOptions?.routing || 'path';\n\n  if (routing === 'path') {\n    if (!path) {\n      return errorThrower.throw(noPathProvidedError(componentName));\n    }\n\n    return {\n      ...routingOptions,\n      ...props,\n      routing: 'path',\n    };\n  }\n\n  if (props.path) {\n    return errorThrower.throw(incompatibleRoutingWithPathProvidedError(componentName));\n  }\n\n  return {\n    ...routingOptions,\n    ...props,\n    path: undefined,\n  };\n}\n", "export { setErrorThrowerOptions } from './errors/errorThrower';\nexport { MultisessionAppSupport } from './components/controlComponents';\nexport { useRoutingProps } from './hooks/useRoutingProps';\nexport { useDerivedAuth } from './hooks/useAuth';\n\nexport {\n  clerkJsScriptUrl,\n  buildClerkJsScriptAttributes,\n  setClerkJsLoadingErrorPackageName,\n} from '@clerk/shared/loadClerkJsScript';\n"], "names": [], "mappings": ";;;;;;;ACKA;;;;ADAO,SAAS,gBACd,aAAA,EACA,KAAA,EACA,cAAA,EACG;IACH,MAAM,OAAO,MAAM,IAAA,IAAA,CAAQ,kBAAA,OAAA,KAAA,IAAA,eAAgB,IAAA;IAC3C,MAAM,UAAU,MAAM,OAAA,IAAA,CAAW,kBAAA,OAAA,KAAA,IAAA,eAAgB,OAAA,KAAW;IAE5D,IAAI,YAAY,QAAQ;QACtB,IAAI,CAAC,MAAM;YACT,mVAAO,eAAA,CAAa,KAAA,iVAAM,sBAAA,EAAoB,aAAa,CAAC;QAC9D;QAEA,OAAO;YACL,GAAG,cAAA;YACH,GAAG,KAAA;YACH,SAAS;QACX;IACF;IAEA,IAAI,MAAM,IAAA,EAAM;QACd,mVAAO,eAAA,CAAa,KAAA,iVAAM,2CAAA,EAAyC,aAAa,CAAC;IACnF;IAEA,OAAO;QACL,GAAG,cAAA;QACH,GAAG,KAAA;QACH,MAAM,KAAA;IACR;AACF", "ignoreList": [0, 1], "debugId": null}}]}