{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/sentry.edge.config.ts"], "sourcesContent": ["import * as Sentry from \"@sentry/nextjs\";\n\nSentry.init({\n  dsn: process.env.SENTRY_DSN,\n\n  // Set tracesSampleRate to 1.0 to capture 100%\n  // of the transactions for performance monitoring.\n  // We recommend adjusting this value in production\n  tracesSampleRate: 1.0,\n\n  // ...\n\n  // Note: if you want to override the automatic release value, do not set a\n  // `release` value here - use the environment variable `SENTRY_RELEASE`, so\n  // that it will also get attached to your source maps\n});"], "names": [], "mappings": ";AAAA;;AAEA,CAAA,GAAA,uYAAA,CAAA,OAAW,AAAD,EAAE;IACV,KAAK,QAAQ,GAAG,CAAC,UAAU;IAE3B,8CAA8C;IAC9C,kDAAkD;IAClD,kDAAkD;IAClD,kBAAkB;AAOpB"}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/instrumentation.ts"], "sourcesContent": ["export async function register() {\n  if (process.env.NEXT_RUNTIME === \"nodejs\") {\n    await import(\"./sentry.server.config\");\n  }\n\n  if (process.env.NEXT_RUNTIME === \"edge\") {\n    await import(\"./sentry.edge.config\");\n  }\n}"], "names": [], "mappings": ";;;AAAO,eAAe;IACpB,uCAA2C;;IAE3C;IAEA,wCAAyC;QACvC;IACF;AACF"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/edge-wrapper.js"], "sourcesContent": ["self._ENTRIES ||= {};\nconst modProm = import('MODULE');\nmodProm.catch(() => {});\nself._ENTRIES[\"middleware_instrumentation\"] = new Proxy(modProm, {\n    get(modProm, name) {\n        if (name === \"then\") {\n            return (res, rej) => modProm.then(res, rej);\n        }\n        let result = (...args) => modProm.then((mod) => (0, mod[name])(...args));\n        result.then = (res, rej) => modProm.then((mod) => mod[name]).then(res, rej);\n        return result;\n    },\n});\n"], "names": [], "mappings": "AAAA,KAAK,QAAQ,KAAK,CAAC;AACnB,MAAM;AACN,QAAQ,KAAK,CAAC,KAAO;AACrB,KAAK,QAAQ,CAAC,6BAA6B,GAAG,IAAI,MAAM,SAAS;IAC7D,KAAI,OAAO,EAAE,IAAI;QACb,IAAI,SAAS,QAAQ;YACjB,OAAO,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,KAAK;QAC3C;QACA,IAAI,SAAS,CAAC,GAAG,OAAS,QAAQ,IAAI,CAAC,CAAC,MAAQ,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK;QAClE,OAAO,IAAI,GAAG,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,CAAC,MAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK;QACvE,OAAO;IACX;AACJ"}}]}