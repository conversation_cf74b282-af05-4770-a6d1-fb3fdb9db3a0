{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/util/shared.ts"], "sourcesContent": ["export { addClerkPrefix, getScriptUrl, getClerkJsMajorVersionOrTag } from '@clerk/shared/url';\nexport { retry } from '@clerk/shared/retry';\nexport {\n  isDevelopmentFromSecretKey,\n  isProductionFromSecretKey,\n  parsePublishable<PERSON><PERSON>,\n  getCookieSuffix,\n  getSuffixedCookieName,\n} from '@clerk/shared/keys';\nexport { deprecated, deprecatedProperty } from '@clerk/shared/deprecated';\n\nimport { buildErrorThrower } from '@clerk/shared/error';\nimport { createDevOrStagingUrlCache } from '@clerk/shared/keys';\n// TODO: replace packageName with `${PACKAGE_NAME}@${PACKAGE_VERSION}` from tsup.config.ts\nexport const errorThrower = buildErrorThrower({ packageName: '@clerk/backend' });\n\nexport const { isDevOrStagingUrl } = createDevOrStagingUrlCache();\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gBAAgB,cAAc,mCAAmC;AAC1E,SAAS,aAAa;AACtB;AAOA,SAAS,YAAY,0BAA0B;;AAE/C,SAAS,yBAAyB;AAClC,SAAS,kCAAkC;;;;;;;AAEpC,IAAM,sUAAe,oBAAA,EAAkB;IAAE,aAAa;AAAiB,CAAC;AAExE,IAAM,EAAE,iBAAA,CAAkB,CAAA,0TAAI,6BAAA,CAA2B", "ignoreList": [0]}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/errors.ts"], "sourcesContent": ["export type TokenCarrier = 'header' | 'cookie';\n\nexport const TokenVerificationErrorCode = {\n  InvalidSecretKey: 'clerk_key_invalid',\n};\n\nexport type TokenVerificationErrorCode = (typeof TokenVerificationErrorCode)[keyof typeof TokenVerificationErrorCode];\n\nexport const TokenVerificationErrorReason = {\n  TokenExpired: 'token-expired',\n  TokenInvalid: 'token-invalid',\n  TokenInvalidAlgorithm: 'token-invalid-algorithm',\n  TokenInvalidAuthorizedParties: 'token-invalid-authorized-parties',\n  TokenInvalidSignature: 'token-invalid-signature',\n  TokenNotActiveYet: 'token-not-active-yet',\n  TokenIatInTheFuture: 'token-iat-in-the-future',\n  TokenVerificationFailed: 'token-verification-failed',\n  InvalidSecretKey: 'secret-key-invalid',\n  LocalJWKMissing: 'jwk-local-missing',\n  RemoteJWKFailedToLoad: 'jwk-remote-failed-to-load',\n  RemoteJWKInvalid: 'jwk-remote-invalid',\n  RemoteJWKMissing: 'jwk-remote-missing',\n  JWKFailedToResolve: 'jwk-failed-to-resolve',\n  JWKKidMismatch: 'jwk-kid-mismatch',\n};\n\nexport type TokenVerificationErrorReason =\n  (typeof TokenVerificationErrorReason)[keyof typeof TokenVerificationErrorReason];\n\nexport const TokenVerificationErrorAction = {\n  ContactSupport: 'Contact <EMAIL>',\n  EnsureClerkJWT: 'Make sure that this is a valid Clerk generate JWT.',\n  SetClerkJWTKey: 'Set the CLERK_JWT_KEY environment variable.',\n  SetClerkSecretKey: 'Set the CLERK_SECRET_KEY environment variable.',\n  EnsureClockSync: 'Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization).',\n};\n\nexport type TokenVerificationErrorAction =\n  (typeof TokenVerificationErrorAction)[keyof typeof TokenVerificationErrorAction];\n\nexport class TokenVerificationError extends Error {\n  action?: TokenVerificationErrorAction;\n  reason: TokenVerificationErrorReason;\n  tokenCarrier?: TokenCarrier;\n\n  constructor({\n    action,\n    message,\n    reason,\n  }: {\n    action?: TokenVerificationErrorAction;\n    message: string;\n    reason: TokenVerificationErrorReason;\n  }) {\n    super(message);\n\n    Object.setPrototypeOf(this, TokenVerificationError.prototype);\n\n    this.reason = reason;\n    this.message = message;\n    this.action = action;\n  }\n\n  public getFullMessage() {\n    return `${[this.message, this.action].filter(m => m).join(' ')} (reason=${this.reason}, token-carrier=${\n      this.tokenCarrier\n    })`;\n  }\n}\n\nexport class SignJWTError extends Error {}\n\nexport const MachineTokenVerificationErrorCode = {\n  TokenInvalid: 'token-invalid',\n  InvalidSecretKey: 'secret-key-invalid',\n  UnexpectedError: 'unexpected-error',\n} as const;\n\nexport type MachineTokenVerificationErrorCode =\n  (typeof MachineTokenVerificationErrorCode)[keyof typeof MachineTokenVerificationErrorCode];\n\nexport class MachineTokenVerificationError extends Error {\n  code: MachineTokenVerificationErrorCode;\n  long_message?: string;\n  status: number;\n\n  constructor({ message, code, status }: { message: string; code: MachineTokenVerificationErrorCode; status: number }) {\n    super(message);\n    Object.setPrototypeOf(this, MachineTokenVerificationError.prototype);\n\n    this.code = code;\n    this.status = status;\n  }\n\n  public getFullMessage() {\n    return `${this.message} (code=${this.code}, status=${this.status})`;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEO,IAAM,6BAA6B;IACxC,kBAAkB;AACpB;AAIO,IAAM,+BAA+B;IAC1C,cAAc;IACd,cAAc;IACd,uBAAuB;IACvB,+BAA+B;IAC/B,uBAAuB;IACvB,mBAAmB;IACnB,qBAAqB;IACrB,yBAAyB;IACzB,kBAAkB;IAClB,iBAAiB;IACjB,uBAAuB;IACvB,kBAAkB;IAClB,kBAAkB;IAClB,oBAAoB;IACpB,gBAAgB;AAClB;AAKO,IAAM,+BAA+B;IAC1C,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,mBAAmB;IACnB,iBAAiB;AACnB;AAKO,IAAM,yBAAN,MAAM,gCAA+B,MAAM;IAKhD,YAAY,EACV,MAAA,EACA,OAAA,EACA,MAAA,EACF,CAIG;QACD,KAAA,CAAM,OAAO;QAEb,OAAO,cAAA,CAAe,IAAA,EAAM,wBAAuB,SAAS;QAE5D,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,MAAA,GAAS;IAChB;IAEO,iBAAiB;QACtB,OAAO,GAAG;YAAC,IAAA,CAAK,OAAA;YAAS,IAAA,CAAK,MAAM;SAAA,CAAE,MAAA,CAAO,CAAA,IAAK,CAAC,EAAE,IAAA,CAAK,GAAG,CAAC,CAAA,SAAA,EAAY,IAAA,CAAK,MAAM,CAAA,gBAAA,EACnF,IAAA,CAAK,YACP,CAAA,CAAA,CAAA;IACF;AACF;AAEO,IAAM,eAAN,cAA2B,MAAM;AAAC;AAElC,IAAM,oCAAoC;IAC/C,cAAc;IACd,kBAAkB;IAClB,iBAAiB;AACnB;AAKO,IAAM,gCAAN,MAAM,uCAAsC,MAAM;IAKvD,YAAY,EAAE,OAAA,EAAS,IAAA,EAAM,MAAA,CAAO,CAAA,CAAiF;QACnH,KAAA,CAAM,OAAO;QACb,OAAO,cAAA,CAAe,IAAA,EAAM,+BAA8B,SAAS;QAEnE,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,MAAA,GAAS;IAChB;IAEO,iBAAiB;QACtB,OAAO,GAAG,IAAA,CAAK,OAAO,CAAA,OAAA,EAAU,IAAA,CAAK,IAAI,CAAA,SAAA,EAAY,IAAA,CAAK,MAAM,CAAA,CAAA,CAAA;IAClE;AACF", "ignoreList": [0]}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/dist/runtime/browser/crypto.mjs"], "sourcesContent": ["export const webcrypto = crypto;\n"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY", "ignoreList": [0]}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/runtime.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/util/rfc4648.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/jwt/algorithms.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/jwt/assertions.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/jwt/cryptoKeys.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/jwt/verifyJwt.ts"], "sourcesContent": ["/**\n * This file exports APIs that vary across runtimes (i.e. Node & Browser - V8 isolates)\n * as a singleton object.\n *\n * Runtime polyfills are written in VanillaJS for now to avoid TS complication. Moreover,\n * due to this issue https://github.com/microsoft/TypeScript/issues/44848, there is not a good way\n * to tell Typescript which conditional import to use during build type.\n *\n * The Runtime type definition ensures type safety for now.\n * Runtime js modules are copied into dist folder with bash script.\n *\n * TODO: Support TS runtime modules\n */\n\n// @ts-ignore - These are package subpaths\nimport { webcrypto as crypto } from '#crypto';\n\ntype Runtime = {\n  crypto: Crypto;\n  fetch: typeof globalThis.fetch;\n  AbortController: typeof globalThis.AbortController;\n  Blob: typeof globalThis.Blob;\n  FormData: typeof globalThis.FormData;\n  Headers: typeof globalThis.Headers;\n  Request: typeof globalThis.Request;\n  Response: typeof globalThis.Response;\n};\n\n// Invoking the global.fetch without binding it first to the globalObject fails in\n// Cloudflare Workers with an \"Illegal Invocation\" error.\n//\n// The globalThis object is supported for Node >= 12.0.\n//\n// https://github.com/supabase/supabase/issues/4417\nconst globalFetch = fetch.bind(globalThis);\n\nexport const runtime: Runtime = {\n  crypto,\n  get fetch() {\n    // We need to use the globalFetch for Cloudflare Workers but the fetch for testing\n    return process.env.NODE_ENV === 'test' ? fetch : globalFetch;\n  },\n  AbortController: globalThis.AbortController,\n  Blob: globalThis.Blob,\n  FormData: globalThis.FormData,\n  Headers: globalThis.Headers,\n  Request: globalThis.Request,\n  Response: globalThis.Response,\n};\n", "/**\n * The base64url helper was extracted from the rfc4648 package\n * in order to resolve CSJ/ESM interoperability issues\n *\n * https://github.com/swansontec/rfc4648.js\n *\n * For more context please refer to:\n * - https://github.com/evanw/esbuild/issues/1719\n * - https://github.com/evanw/esbuild/issues/532\n * - https://github.com/swansontec/rollup-plugin-mjs-entry\n */\nexport const base64url = {\n  parse(string: string, opts?: ParseOptions): Uint8Array {\n    return parse(string, base64UrlEncoding, opts);\n  },\n\n  stringify(data: ArrayLike<number>, opts?: StringifyOptions): string {\n    return stringify(data, base64UrlEncoding, opts);\n  },\n};\n\nconst base64UrlEncoding: Encoding = {\n  chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bits: 6,\n};\n\ninterface Encoding {\n  bits: number;\n  chars: string;\n  codes?: { [char: string]: number };\n}\n\ninterface ParseOptions {\n  loose?: boolean;\n  out?: new (size: number) => { [index: number]: number };\n}\n\ninterface StringifyOptions {\n  pad?: boolean;\n}\n\nfunction parse(string: string, encoding: Encoding, opts: ParseOptions = {}): Uint8Array {\n  // Build the character lookup table:\n  if (!encoding.codes) {\n    encoding.codes = {};\n    for (let i = 0; i < encoding.chars.length; ++i) {\n      encoding.codes[encoding.chars[i]] = i;\n    }\n  }\n\n  // The string must have a whole number of bytes:\n  if (!opts.loose && (string.length * encoding.bits) & 7) {\n    throw new SyntaxError('Invalid padding');\n  }\n\n  // Count the padding bytes:\n  let end = string.length;\n  while (string[end - 1] === '=') {\n    --end;\n\n    // If we get a whole number of bytes, there is too much padding:\n    if (!opts.loose && !(((string.length - end) * encoding.bits) & 7)) {\n      throw new SyntaxError('Invalid padding');\n    }\n  }\n\n  // Allocate the output:\n  const out = new (opts.out ?? Uint8Array)(((end * encoding.bits) / 8) | 0) as Uint8Array;\n\n  // Parse the data:\n  let bits = 0; // Number of bits currently in the buffer\n  let buffer = 0; // Bits waiting to be written out, MSB first\n  let written = 0; // Next byte to write\n  for (let i = 0; i < end; ++i) {\n    // Read one character from the string:\n    const value = encoding.codes[string[i]];\n    if (value === undefined) {\n      throw new SyntaxError('Invalid character ' + string[i]);\n    }\n\n    // Append the bits to the buffer:\n    buffer = (buffer << encoding.bits) | value;\n    bits += encoding.bits;\n\n    // Write out some bits if the buffer has a byte's worth:\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 0xff & (buffer >> bits);\n    }\n  }\n\n  // Verify that we have received just enough bits:\n  if (bits >= encoding.bits || 0xff & (buffer << (8 - bits))) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n\n  return out;\n}\n\nfunction stringify(data: ArrayLike<number>, encoding: Encoding, opts: StringifyOptions = {}): string {\n  const { pad = true } = opts;\n  const mask = (1 << encoding.bits) - 1;\n  let out = '';\n\n  let bits = 0; // Number of bits currently in the buffer\n  let buffer = 0; // Bits waiting to be written out, MSB first\n  for (let i = 0; i < data.length; ++i) {\n    // Slurp data into the buffer:\n    buffer = (buffer << 8) | (0xff & data[i]);\n    bits += 8;\n\n    // Write out as much as we can:\n    while (bits > encoding.bits) {\n      bits -= encoding.bits;\n      out += encoding.chars[mask & (buffer >> bits)];\n    }\n  }\n\n  // Partial character:\n  if (bits) {\n    out += encoding.chars[mask & (buffer << (encoding.bits - bits))];\n  }\n\n  // Add padding characters until we hit a byte boundary:\n  if (pad) {\n    while ((out.length * encoding.bits) & 7) {\n      out += '=';\n    }\n  }\n\n  return out;\n}\n", "const algToHash: Record<string, string> = {\n  RS256: 'SHA-256',\n  RS384: 'SHA-384',\n  RS512: 'SHA-512',\n};\nconst RSA_ALGORITHM_NAME = 'RSASSA-PKCS1-v1_5';\n\nconst jwksAlgToCryptoAlg: Record<string, string> = {\n  RS256: RSA_ALGORITHM_NAME,\n  RS384: RSA_ALGORITHM_NAME,\n  RS512: RSA_ALGORITHM_NAME,\n};\n\nexport const algs = Object.keys(algToHash);\n\nexport function getCryptoAlgorithm(algorithmName: string): RsaHashedImportParams {\n  const hash = algToHash[algorithmName];\n  const name = jwksAlgToCryptoAlg[algorithmName];\n\n  if (!hash || !name) {\n    throw new Error(`Unsupported algorithm ${algorithmName}, expected one of ${algs.join(',')}.`);\n  }\n\n  return {\n    hash: { name: algToHash[algorithmName] },\n    name: jwksAlgToCryptoAlg[algorithmName],\n  };\n}\n", "import { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from '../errors';\nimport { algs } from './algorithms';\n\nexport type IssuerResolver = string | ((iss: string) => boolean);\n\nconst isArrayString = (s: unknown): s is string[] => {\n  return Array.isArray(s) && s.length > 0 && s.every(a => typeof a === 'string');\n};\n\nexport const assertAudienceClaim = (aud?: unknown, audience?: unknown) => {\n  const audienceList = [audience].flat().filter(a => !!a);\n  const audList = [aud].flat().filter(a => !!a);\n  const shouldVerifyAudience = audienceList.length > 0 && audList.length > 0;\n\n  if (!shouldVerifyAudience) {\n    // Notice: Clerk JWTs use AZP claim instead of Audience\n    //\n    // return {\n    //   valid: false,\n    //   reason: `Invalid JWT audience claim (aud) ${JSON.stringify(\n    //     aud,\n    //   )}. Expected a string or a non-empty array of strings.`,\n    // };\n    return;\n  }\n\n  if (typeof aud === 'string') {\n    if (!audienceList.includes(aud)) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.EnsureClerkJWT,\n        reason: TokenVerificationErrorReason.TokenVerificationFailed,\n        message: `Invalid JWT audience claim (aud) ${JSON.stringify(aud)}. Is not included in \"${JSON.stringify(\n          audienceList,\n        )}\".`,\n      });\n    }\n  } else if (isArrayString(aud)) {\n    if (!aud.some(a => audienceList.includes(a))) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.EnsureClerkJWT,\n        reason: TokenVerificationErrorReason.TokenVerificationFailed,\n        message: `Invalid JWT audience claim array (aud) ${JSON.stringify(aud)}. Is not included in \"${JSON.stringify(\n          audienceList,\n        )}\".`,\n      });\n    }\n  }\n};\n\nexport const assertHeaderType = (typ?: unknown) => {\n  if (typeof typ === 'undefined') {\n    return;\n  }\n\n  if (typ !== 'JWT') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenInvalid,\n      message: `Invalid JWT type ${JSON.stringify(typ)}. Expected \"JWT\".`,\n    });\n  }\n};\n\nexport const assertHeaderAlgorithm = (alg: string) => {\n  if (!algs.includes(alg)) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenInvalidAlgorithm,\n      message: `Invalid JWT algorithm ${JSON.stringify(alg)}. Supported: ${algs}.`,\n    });\n  }\n};\n\nexport const assertSubClaim = (sub?: string) => {\n  if (typeof sub !== 'string') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Subject claim (sub) is required and must be a string. Received ${JSON.stringify(sub)}.`,\n    });\n  }\n};\n\nexport const assertAuthorizedPartiesClaim = (azp?: string, authorizedParties?: string[]) => {\n  if (!azp || !authorizedParties || authorizedParties.length === 0) {\n    return;\n  }\n\n  if (!authorizedParties.includes(azp)) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidAuthorizedParties,\n      message: `Invalid JWT Authorized party claim (azp) ${JSON.stringify(azp)}. Expected \"${authorizedParties}\".`,\n    });\n  }\n};\n\nexport const assertExpirationClaim = (exp: number, clockSkewInMs: number) => {\n  if (typeof exp !== 'number') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT expiry date claim (exp) ${JSON.stringify(exp)}. Expected number.`,\n    });\n  }\n\n  const currentDate = new Date(Date.now());\n  const expiryDate = new Date(0);\n  expiryDate.setUTCSeconds(exp);\n\n  const expired = expiryDate.getTime() <= currentDate.getTime() - clockSkewInMs;\n  if (expired) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenExpired,\n      message: `JWT is expired. Expiry date: ${expiryDate.toUTCString()}, Current date: ${currentDate.toUTCString()}.`,\n    });\n  }\n};\n\nexport const assertActivationClaim = (nbf: number | undefined, clockSkewInMs: number) => {\n  if (typeof nbf === 'undefined') {\n    return;\n  }\n\n  if (typeof nbf !== 'number') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT not before date claim (nbf) ${JSON.stringify(nbf)}. Expected number.`,\n    });\n  }\n\n  const currentDate = new Date(Date.now());\n  const notBeforeDate = new Date(0);\n  notBeforeDate.setUTCSeconds(nbf);\n\n  const early = notBeforeDate.getTime() > currentDate.getTime() + clockSkewInMs;\n  if (early) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenNotActiveYet,\n      message: `JWT cannot be used prior to not before date claim (nbf). Not before date: ${notBeforeDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`,\n    });\n  }\n};\n\nexport const assertIssuedAtClaim = (iat: number | undefined, clockSkewInMs: number) => {\n  if (typeof iat === 'undefined') {\n    return;\n  }\n\n  if (typeof iat !== 'number') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT issued at date claim (iat) ${JSON.stringify(iat)}. Expected number.`,\n    });\n  }\n\n  const currentDate = new Date(Date.now());\n  const issuedAtDate = new Date(0);\n  issuedAtDate.setUTCSeconds(iat);\n\n  const postIssued = issuedAtDate.getTime() > currentDate.getTime() + clockSkewInMs;\n  if (postIssued) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenIatInTheFuture,\n      message: `JWT issued at date claim (iat) is in the future. Issued at date: ${issuedAtDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`,\n    });\n  }\n};\n", "import { isomorphicAtob } from '@clerk/shared/isomorphicAtob';\n\nimport { runtime } from '../runtime';\n\n// https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/importKey#pkcs_8_import\nfunction pemToBuffer(secret: string): ArrayBuffer {\n  const trimmed = secret\n    .replace(/-----BEGIN.*?-----/g, '')\n    .replace(/-----END.*?-----/g, '')\n    .replace(/\\s/g, '');\n\n  const decoded = isomorphicAtob(trimmed);\n\n  const buffer = new ArrayBuffer(decoded.length);\n  const bufView = new Uint8Array(buffer);\n\n  for (let i = 0, strLen = decoded.length; i < strLen; i++) {\n    bufView[i] = decoded.charCodeAt(i);\n  }\n\n  return bufView;\n}\n\nexport function importKey(\n  key: JsonWebKey | string,\n  algorithm: RsaHashedImportParams,\n  keyUsage: 'verify' | 'sign',\n): Promise<CryptoKey> {\n  if (typeof key === 'object') {\n    return runtime.crypto.subtle.importKey('jwk', key, algorithm, false, [keyUsage]);\n  }\n\n  const keyData = pemToBuffer(key);\n  const format = keyUsage === 'sign' ? 'pkcs8' : 'spki';\n\n  return runtime.crypto.subtle.importKey(format, keyData, algorithm, false, [keyUsage]);\n}\n", "import type { Jwt, JwtPayload } from '@clerk/types';\n\nimport { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from '../errors';\nimport { runtime } from '../runtime';\nimport { base64url } from '../util/rfc4648';\nimport { getCryptoAlgorithm } from './algorithms';\nimport {\n  assertActivationClaim,\n  assertAudienceClaim,\n  assertAuthorizedPartiesClaim,\n  assertExpirationClaim,\n  assertHeaderAlgorithm,\n  assertHeaderType,\n  assertIssuedAtClaim,\n  assertSubClaim,\n} from './assertions';\nimport { importKey } from './cryptoKeys';\nimport type { JwtReturnType } from './types';\n\nconst DEFAULT_CLOCK_SKEW_IN_MS = 5 * 1000;\n\nexport async function hasValidSignature(jwt: Jwt, key: JsonWebKey | string): Promise<JwtReturnType<boolean, Error>> {\n  const { header, signature, raw } = jwt;\n  const encoder = new TextEncoder();\n  const data = encoder.encode([raw.header, raw.payload].join('.'));\n  const algorithm = getCryptoAlgorithm(header.alg);\n\n  try {\n    const cryptoKey = await importKey(key, algorithm, 'verify');\n\n    const verified = await runtime.crypto.subtle.verify(algorithm.name, cryptoKey, signature, data);\n    return { data: verified };\n  } catch (error) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          reason: TokenVerificationErrorReason.TokenInvalidSignature,\n          message: (error as Error)?.message,\n        }),\n      ],\n    };\n  }\n}\n\nexport function decodeJwt(token: string): JwtReturnType<Jwt, TokenVerificationError> {\n  const tokenParts = (token || '').toString().split('.');\n  if (tokenParts.length !== 3) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          reason: TokenVerificationErrorReason.TokenInvalid,\n          message: `Invalid JWT form. A JWT consists of three parts separated by dots.`,\n        }),\n      ],\n    };\n  }\n\n  const [rawHeader, rawPayload, rawSignature] = tokenParts;\n\n  const decoder = new TextDecoder();\n\n  // To verify a JWS with SubtleCrypto you need to be careful to encode and decode\n  // the data properly between binary and base64url representation. Unfortunately\n  // the standard implementation in the V8 of btoa() and atob() are difficult to\n  // work with as they use \"a Unicode string containing only characters in the\n  // range U+0000 to U+00FF, each representing a binary byte with values 0x00 to\n  // 0xFF respectively\" as the representation of binary data.\n\n  // A better solution to represent binary data in Javascript is to use ES6 TypedArray\n  // and use a Javascript library to convert them to base64url that honors RFC 4648.\n\n  // Side note: The difference between base64 and base64url is the characters selected\n  // for value 62 and 63 in the standard, base64 encode them to + and / while base64url\n  // encode - and _.\n\n  // More info at https://stackoverflow.com/questions/54062583/how-to-verify-a-signed-jwt-with-subtlecrypto-of-the-web-crypto-API\n  const header = JSON.parse(decoder.decode(base64url.parse(rawHeader, { loose: true })));\n  const payload = JSON.parse(decoder.decode(base64url.parse(rawPayload, { loose: true })));\n\n  const signature = base64url.parse(rawSignature, { loose: true });\n\n  const data = {\n    header,\n    payload,\n    signature,\n    raw: {\n      header: rawHeader,\n      payload: rawPayload,\n      signature: rawSignature,\n      text: token,\n    },\n  } satisfies Jwt;\n\n  return { data };\n}\n\n/**\n * @inline\n */\nexport type VerifyJwtOptions = {\n  /**\n   * A string or list of [audiences](https://datatracker.ietf.org/doc/html/rfc7519#section-4.1.3). If passed, it is checked against the `aud` claim in the token.\n   */\n  audience?: string | string[];\n  /**\n   * An allowlist of origins to verify against, to protect your application from the subdomain cookie leaking attack.\n   * @example\n   * ```ts\n   * ['http://localhost:3000', 'https://example.com']\n   * ```\n   */\n  authorizedParties?: string[];\n  /**\n   * Specifies the allowed time difference (in milliseconds) between the Clerk server (which generates the token) and the clock of the user's application server when validating a token.\n   * @default 5000\n   */\n  clockSkewInMs?: number;\n  /**\n   * @internal\n   */\n  key: JsonWebKey | string;\n};\n\nexport async function verifyJwt(\n  token: string,\n  options: VerifyJwtOptions,\n): Promise<JwtReturnType<JwtPayload, TokenVerificationError>> {\n  const { audience, authorizedParties, clockSkewInMs, key } = options;\n  const clockSkew = clockSkewInMs || DEFAULT_CLOCK_SKEW_IN_MS;\n\n  const { data: decoded, errors } = decodeJwt(token);\n  if (errors) {\n    return { errors };\n  }\n\n  const { header, payload } = decoded;\n  try {\n    // Header verifications\n    const { typ, alg } = header;\n\n    assertHeaderType(typ);\n    assertHeaderAlgorithm(alg);\n\n    // Payload verifications\n    const { azp, sub, aud, iat, exp, nbf } = payload;\n\n    assertSubClaim(sub);\n    assertAudienceClaim([aud], [audience]);\n    assertAuthorizedPartiesClaim(azp, authorizedParties);\n    assertExpirationClaim(exp, clockSkew);\n    assertActivationClaim(nbf, clockSkew);\n    assertIssuedAtClaim(iat, clockSkew);\n  } catch (err) {\n    return { errors: [err as TokenVerificationError] };\n  }\n\n  const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);\n  if (signatureErrors) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          action: TokenVerificationErrorAction.EnsureClerkJWT,\n          reason: TokenVerificationErrorReason.TokenVerificationFailed,\n          message: `Error verifying JWT signature. ${signatureErrors[0]}`,\n        }),\n      ],\n    };\n  }\n\n  if (!signatureValid) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          reason: TokenVerificationErrorReason.TokenInvalidSignature,\n          message: 'JWT signature is invalid.',\n        }),\n      ],\n    };\n  }\n\n  return { data: payload };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAeA,SAAS,aAAa,cAAc;;AIfpC,SAAS,sBAAsB;;;;AJkC/B,IAAM,cAAc,MAAM,IAAA,CAAK,UAAU;AAElC,IAAM,UAAmB;2UAC9B,aAAA;IACA,IAAI,SAAQ;QAEV,OAAO,QAAQ,IAAI,aAAa,SAAS,2CAAQ;IACnD;IACA,iBAAiB,WAAW,eAAA;IAC5B,MAAM,WAAW,IAAA;IACjB,UAAU,WAAW,QAAA;IACrB,SAAS,WAAW,OAAA;IACpB,SAAS,WAAW,OAAA;IACpB,UAAU,WAAW,QAAA;AACvB;;ACrCO,IAAM,YAAY;IACvB,OAAM,MAAA,EAAgB,IAAA,EAAiC;QACrD,OAAO,MAAM,QAAQ,mBAAmB,IAAI;IAC9C;IAEA,WAAU,IAAA,EAAyB,IAAA,EAAiC;QAClE,OAAO,UAAU,MAAM,mBAAmB,IAAI;IAChD;AACF;AAEA,IAAM,oBAA8B;IAClC,OAAO;IACP,MAAM;AACR;AAiBA,SAAS,MAAM,MAAA,EAAgB,QAAA,EAAoB,OAAqB,CAAC,CAAA,EAAe;IAEtF,IAAI,CAAC,SAAS,KAAA,EAAO;QACnB,SAAS,KAAA,GAAQ,CAAC;QAClB,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,KAAA,CAAM,MAAA,EAAQ,EAAE,EAAG;YAC9C,SAAS,KAAA,CAAM,SAAS,KAAA,CAAM,CAAC,CAAC,CAAA,GAAI;QACtC;IACF;IAGA,IAAI,CAAC,KAAK,KAAA,IAAU,OAAO,MAAA,GAAS,SAAS,IAAA,GAAQ,GAAG;QACtD,MAAM,IAAI,YAAY,iBAAiB;IACzC;IAGA,IAAI,MAAM,OAAO,MAAA;IACjB,MAAO,MAAA,CAAO,MAAM,CAAC,CAAA,KAAM,IAAK;QAC9B,EAAE;QAGF,IAAI,CAAC,KAAK,KAAA,IAAS,CAAA,CAAA,CAAI,OAAO,MAAA,GAAS,GAAA,IAAO,SAAS,IAAA,GAAQ,CAAA,GAAI;YACjE,MAAM,IAAI,YAAY,iBAAiB;QACzC;IACF;IAGA,MAAM,MAAM,IAAA,CAAK,KAAK,GAAA,IAAO,UAAA,EAAc,MAAM,SAAS,IAAA,GAAQ,IAAK,CAAC;IAGxE,IAAI,OAAO;IACX,IAAI,SAAS;IACb,IAAI,UAAU;IACd,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;QAE5B,MAAM,QAAQ,SAAS,KAAA,CAAM,MAAA,CAAO,CAAC,CAAC,CAAA;QACtC,IAAI,UAAU,KAAA,GAAW;YACvB,MAAM,IAAI,YAAY,uBAAuB,MAAA,CAAO,CAAC,CAAC;QACxD;QAGA,SAAU,UAAU,SAAS,IAAA,GAAQ;QACrC,QAAQ,SAAS,IAAA;QAGjB,IAAI,QAAQ,GAAG;YACb,QAAQ;YACR,GAAA,CAAI,SAAS,CAAA,GAAI,MAAQ,UAAU;QACrC;IACF;IAGA,IAAI,QAAQ,SAAS,IAAA,IAAQ,MAAQ,UAAW,IAAI,MAAQ;QAC1D,MAAM,IAAI,YAAY,wBAAwB;IAChD;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,IAAA,EAAyB,QAAA,EAAoB,OAAyB,CAAC,CAAA,EAAW;IACnG,MAAM,EAAE,MAAM,IAAA,CAAK,CAAA,GAAI;IACvB,MAAM,OAAA,CAAQ,KAAK,SAAS,IAAA,IAAQ;IACpC,IAAI,MAAM;IAEV,IAAI,OAAO;IACX,IAAI,SAAS;IACb,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,MAAA,EAAQ,EAAE,EAAG;QAEpC,SAAU,UAAU,IAAM,MAAO,IAAA,CAAK,CAAC,CAAA;QACvC,QAAQ;QAGR,MAAO,OAAO,SAAS,IAAA,CAAM;YAC3B,QAAQ,SAAS,IAAA;YACjB,OAAO,SAAS,KAAA,CAAM,OAAQ,UAAU,IAAK,CAAA;QAC/C;IACF;IAGA,IAAI,MAAM;QACR,OAAO,SAAS,KAAA,CAAM,OAAQ,UAAW,SAAS,IAAA,GAAO,IAAM,CAAA;IACjE;IAGA,IAAI,KAAK;QACP,MAAQ,IAAI,MAAA,GAAS,SAAS,IAAA,GAAQ,EAAG;YACvC,OAAO;QACT;IACF;IAEA,OAAO;AACT;;ACnIA,IAAM,YAAoC;IACxC,OAAO;IACP,OAAO;IACP,OAAO;AACT;AACA,IAAM,qBAAqB;AAE3B,IAAM,qBAA6C;IACjD,OAAO;IACP,OAAO;IACP,OAAO;AACT;AAEO,IAAM,OAAO,OAAO,IAAA,CAAK,SAAS;AAElC,SAAS,mBAAmB,aAAA,EAA8C;IAC/E,MAAM,OAAO,SAAA,CAAU,aAAa,CAAA;IACpC,MAAM,OAAO,kBAAA,CAAmB,aAAa,CAAA;IAE7C,IAAI,CAAC,QAAQ,CAAC,MAAM;QAClB,MAAM,IAAI,MAAM,CAAA,sBAAA,EAAyB,aAAa,CAAA,kBAAA,EAAqB,KAAK,IAAA,CAAK,GAAG,CAAC,CAAA,CAAA,CAAG;IAC9F;IAEA,OAAO;QACL,MAAM;YAAE,MAAM,SAAA,CAAU,aAAa,CAAA;QAAE;QACvC,MAAM,kBAAA,CAAmB,aAAa,CAAA;IACxC;AACF;;ACtBA,IAAM,gBAAgB,CAAC,MAA8B;IACnD,OAAO,MAAM,OAAA,CAAQ,CAAC,KAAK,EAAE,MAAA,GAAS,KAAK,EAAE,KAAA,CAAM,CAAA,IAAK,OAAO,MAAM,QAAQ;AAC/E;AAEO,IAAM,sBAAsB,CAAC,KAAe,aAAuB;IACxE,MAAM,eAAe;QAAC,QAAQ;KAAA,CAAE,IAAA,CAAK,EAAE,MAAA,CAAO,CAAA,IAAK,CAAC,CAAC,CAAC;IACtD,MAAM,UAAU;QAAC,GAAG;KAAA,CAAE,IAAA,CAAK,EAAE,MAAA,CAAO,CAAA,IAAK,CAAC,CAAC,CAAC;IAC5C,MAAM,uBAAuB,aAAa,MAAA,GAAS,KAAK,QAAQ,MAAA,GAAS;IAEzE,IAAI,CAAC,sBAAsB;QASzB;IACF;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,IAAI,CAAC,aAAa,QAAA,CAAS,GAAG,GAAG;YAC/B,MAAM,yTAAI,yBAAA,CAAuB;gBAC/B,6TAAQ,+BAAA,CAA6B,cAAA;gBACrC,6TAAQ,+BAAA,CAA6B,uBAAA;gBACrC,SAAS,CAAA,iCAAA,EAAoC,KAAK,SAAA,CAAU,GAAG,CAAC,CAAA,sBAAA,EAAyB,KAAK,SAAA,CAC5F,cACD,EAAA,CAAA;YACH,CAAC;QACH;IACF,OAAA,IAAW,cAAc,GAAG,GAAG;QAC7B,IAAI,CAAC,IAAI,IAAA,CAAK,CAAA,IAAK,aAAa,QAAA,CAAS,CAAC,CAAC,GAAG;YAC5C,MAAM,yTAAI,yBAAA,CAAuB;gBAC/B,6TAAQ,+BAAA,CAA6B,cAAA;gBACrC,6TAAQ,+BAAA,CAA6B,uBAAA;gBACrC,SAAS,CAAA,uCAAA,EAA0C,KAAK,SAAA,CAAU,GAAG,CAAC,CAAA,sBAAA,EAAyB,KAAK,SAAA,CAClG,cACD,EAAA,CAAA;YACH,CAAC;QACH;IACF;AACF;AAEO,IAAM,mBAAmB,CAAC,QAAkB;IACjD,IAAI,OAAO,QAAQ,aAAa;QAC9B;IACF;IAEA,IAAI,QAAQ,OAAO;QACjB,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,4TAAQ,gCAAA,CAA6B,cAAA;YACrC,6TAAQ,+BAAA,CAA6B,YAAA;YACrC,SAAS,CAAA,iBAAA,EAAoB,KAAK,SAAA,CAAU,GAAG,CAAC,CAAA,iBAAA,CAAA;QAClD,CAAC;IACH;AACF;AAEO,IAAM,wBAAwB,CAAC,QAAgB;IACpD,IAAI,CAAC,KAAK,QAAA,CAAS,GAAG,GAAG;QACvB,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,cAAA;YACrC,6TAAQ,+BAAA,CAA6B,qBAAA;YACrC,SAAS,CAAA,sBAAA,EAAyB,KAAK,SAAA,CAAU,GAAG,CAAC,CAAA,aAAA,EAAgB,IAAI,CAAA,CAAA,CAAA;QAC3E,CAAC;IACH;AACF;AAEO,IAAM,iBAAiB,CAAC,QAAiB;IAC9C,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,cAAA;YACrC,6TAAQ,+BAAA,CAA6B,uBAAA;YACrC,SAAS,CAAA,+DAAA,EAAkE,KAAK,SAAA,CAAU,GAAG,CAAC,CAAA,CAAA,CAAA;QAChG,CAAC;IACH;AACF;AAEO,IAAM,+BAA+B,CAAC,KAAc,sBAAiC;IAC1F,IAAI,CAAC,OAAO,CAAC,qBAAqB,kBAAkB,MAAA,KAAW,GAAG;QAChE;IACF;IAEA,IAAI,CAAC,kBAAkB,QAAA,CAAS,GAAG,GAAG;QACpC,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,6BAAA;YACrC,SAAS,CAAA,yCAAA,EAA4C,KAAK,SAAA,CAAU,GAAG,CAAC,CAAA,YAAA,EAAe,iBAAiB,CAAA,EAAA,CAAA;QAC1G,CAAC;IACH;AACF;AAEO,IAAM,wBAAwB,CAAC,KAAa,kBAA0B;IAC3E,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,4TAAQ,gCAAA,CAA6B,cAAA;YACrC,6TAAQ,+BAAA,CAA6B,uBAAA;YACrC,SAAS,CAAA,oCAAA,EAAuC,KAAK,SAAA,CAAU,GAAG,CAAC,CAAA,kBAAA,CAAA;QACrE,CAAC;IACH;IAEA,MAAM,cAAc,IAAI,KAAK,KAAK,GAAA,CAAI,CAAC;IACvC,MAAM,aAAa,aAAA,GAAA,IAAI,KAAK,CAAC;IAC7B,WAAW,aAAA,CAAc,GAAG;IAE5B,MAAM,UAAU,WAAW,OAAA,CAAQ,KAAK,YAAY,OAAA,CAAQ,IAAI;IAChE,IAAI,SAAS;QACX,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,YAAA;YACrC,SAAS,CAAA,6BAAA,EAAgC,WAAW,WAAA,CAAY,CAAC,CAAA,gBAAA,EAAmB,YAAY,WAAA,CAAY,CAAC,CAAA,CAAA,CAAA;QAC/G,CAAC;IACH;AACF;AAEO,IAAM,wBAAwB,CAAC,KAAyB,kBAA0B;IACvF,IAAI,OAAO,QAAQ,aAAa;QAC9B;IACF;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,IAAI,8UAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,cAAA;YACrC,6TAAQ,+BAAA,CAA6B,uBAAA;YACrC,SAAS,CAAA,wCAAA,EAA2C,KAAK,SAAA,CAAU,GAAG,CAAC,CAAA,kBAAA,CAAA;QACzE,CAAC;IACH;IAEA,MAAM,cAAc,IAAI,KAAK,KAAK,GAAA,CAAI,CAAC;IACvC,MAAM,gBAAgB,aAAA,GAAA,IAAI,KAAK,CAAC;IAChC,cAAc,aAAA,CAAc,GAAG;IAE/B,MAAM,QAAQ,cAAc,OAAA,CAAQ,IAAI,YAAY,OAAA,CAAQ,IAAI;IAChE,IAAI,OAAO;QACT,MAAM,IAAI,8UAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,iBAAA;YACrC,SAAS,CAAA,0EAAA,EAA6E,cAAc,WAAA,CAAY,CAAC,CAAA,gBAAA,EAAmB,YAAY,WAAA,CAAY,CAAC,CAAA,CAAA,CAAA;QAC/J,CAAC;IACH;AACF;AAEO,IAAM,sBAAsB,CAAC,KAAyB,kBAA0B;IACrF,IAAI,OAAO,QAAQ,aAAa;QAC9B;IACF;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,cAAA;YACrC,4TAAQ,gCAAA,CAA6B,uBAAA;YACrC,SAAS,CAAA,uCAAA,EAA0C,KAAK,SAAA,CAAU,GAAG,CAAC,CAAA,kBAAA,CAAA;QACxE,CAAC;IACH;IAEA,MAAM,cAAc,IAAI,KAAK,KAAK,GAAA,CAAI,CAAC;IACvC,MAAM,eAAe,aAAA,GAAA,IAAI,KAAK,CAAC;IAC/B,aAAa,aAAA,CAAc,GAAG;IAE9B,MAAM,aAAa,aAAa,OAAA,CAAQ,IAAI,YAAY,OAAA,CAAQ,IAAI;IACpE,IAAI,YAAY;QACd,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,mBAAA;YACrC,SAAS,CAAA,iEAAA,EAAoE,aAAa,WAAA,CAAY,CAAC,CAAA,gBAAA,EAAmB,YAAY,WAAA,CAAY,CAAC,CAAA,CAAA,CAAA;QACrJ,CAAC;IACH;AACF;;ACnKA,SAAS,YAAY,MAAA,EAA6B;IAChD,MAAM,UAAU,OACb,OAAA,CAAQ,uBAAuB,EAAE,EACjC,OAAA,CAAQ,qBAAqB,EAAE,EAC/B,OAAA,CAAQ,OAAO,EAAE;IAEpB,MAAM,iUAAU,iBAAA,EAAe,OAAO;IAEtC,MAAM,SAAS,IAAI,YAAY,QAAQ,MAAM;IAC7C,MAAM,UAAU,IAAI,WAAW,MAAM;IAErC,IAAA,IAAS,IAAI,GAAG,SAAS,QAAQ,MAAA,EAAQ,IAAI,QAAQ,IAAK;QACxD,OAAA,CAAQ,CAAC,CAAA,GAAI,QAAQ,UAAA,CAAW,CAAC;IACnC;IAEA,OAAO;AACT;AAEO,SAAS,UACd,GAAA,EACA,SAAA,EACA,QAAA,EACoB;IACpB,IAAI,OAAO,QAAQ,UAAU;QAC3B,OAAO,QAAQ,MAAA,CAAO,MAAA,CAAO,SAAA,CAAU,OAAO,KAAK,WAAW,OAAO;YAAC,QAAQ;SAAC;IACjF;IAEA,MAAM,UAAU,YAAY,GAAG;IAC/B,MAAM,SAAS,aAAa,SAAS,UAAU;IAE/C,OAAO,QAAQ,MAAA,CAAO,MAAA,CAAO,SAAA,CAAU,QAAQ,SAAS,WAAW,OAAO;QAAC,QAAQ;KAAC;AACtF;;ACjBA,IAAM,2BAA2B,IAAI;AAErC,eAAsB,kBAAkB,GAAA,EAAU,GAAA,EAAkE;IAClH,MAAM,EAAE,MAAA,EAAQ,SAAA,EAAW,GAAA,CAAI,CAAA,GAAI;IACnC,MAAM,UAAU,IAAI,YAAY;IAChC,MAAM,OAAO,QAAQ,MAAA,CAAO;QAAC,IAAI,MAAA;QAAQ,IAAI,OAAO;KAAA,CAAE,IAAA,CAAK,GAAG,CAAC;IAC/D,MAAM,YAAY,mBAAmB,OAAO,GAAG;IAE/C,IAAI;QACF,MAAM,YAAY,MAAM,UAAU,KAAK,WAAW,QAAQ;QAE1D,MAAM,WAAW,MAAM,QAAQ,MAAA,CAAO,MAAA,CAAO,MAAA,CAAO,UAAU,IAAA,EAAM,WAAW,WAAW,IAAI;QAC9F,OAAO;YAAE,MAAM;QAAS;IAC1B,EAAA,OAAS,OAAO;QACd,OAAO;YACL,QAAQ;gBACN,yTAAI,yBAAA,CAAuB;oBACzB,6TAAQ,+BAAA,CAA6B,qBAAA;oBACrC,SAAU,OAAiB;gBAC7B,CAAC;aACH;QACF;IACF;AACF;AAEO,SAAS,UAAU,KAAA,EAA2D;IACnF,MAAM,aAAA,CAAc,SAAS,EAAA,EAAI,QAAA,CAAS,EAAE,KAAA,CAAM,GAAG;IACrD,IAAI,WAAW,MAAA,KAAW,GAAG;QAC3B,OAAO;YACL,QAAQ;gBACN,yTAAI,yBAAA,CAAuB;oBACzB,6TAAQ,+BAAA,CAA6B,YAAA;oBACrC,SAAS,CAAA,kEAAA,CAAA;gBACX,CAAC;aACH;QACF;IACF;IAEA,MAAM,CAAC,WAAW,YAAY,YAAY,CAAA,GAAI;IAE9C,MAAM,UAAU,IAAI,YAAY;IAiBhC,MAAM,SAAS,KAAK,KAAA,CAAM,QAAQ,MAAA,CAAO,UAAU,KAAA,CAAM,WAAW;QAAE,OAAO;IAAK,CAAC,CAAC,CAAC;IACrF,MAAM,UAAU,KAAK,KAAA,CAAM,QAAQ,MAAA,CAAO,UAAU,KAAA,CAAM,YAAY;QAAE,OAAO;IAAK,CAAC,CAAC,CAAC;IAEvF,MAAM,YAAY,UAAU,KAAA,CAAM,cAAc;QAAE,OAAO;IAAK,CAAC;IAE/D,MAAM,OAAO;QACX;QACA;QACA;QACA,KAAK;YACH,QAAQ;YACR,SAAS;YACT,WAAW;YACX,MAAM;QACR;IACF;IAEA,OAAO;QAAE;IAAK;AAChB;AA6BA,eAAsB,UACpB,KAAA,EACA,OAAA,EAC4D;IAC5D,MAAM,EAAE,QAAA,EAAU,iBAAA,EAAmB,aAAA,EAAe,GAAA,CAAI,CAAA,GAAI;IAC5D,MAAM,YAAY,iBAAiB;IAEnC,MAAM,EAAE,MAAM,OAAA,EAAS,MAAA,CAAO,CAAA,GAAI,UAAU,KAAK;IACjD,IAAI,QAAQ;QACV,OAAO;YAAE;QAAO;IAClB;IAEA,MAAM,EAAE,MAAA,EAAQ,OAAA,CAAQ,CAAA,GAAI;IAC5B,IAAI;QAEF,MAAM,EAAE,GAAA,EAAK,GAAA,CAAI,CAAA,GAAI;QAErB,iBAAiB,GAAG;QACpB,sBAAsB,GAAG;QAGzB,MAAM,EAAE,GAAA,EAAK,GAAA,EAAK,GAAA,EAAK,GAAA,EAAK,GAAA,EAAK,GAAA,CAAI,CAAA,GAAI;QAEzC,eAAe,GAAG;QAClB,oBAAoB;YAAC,GAAG;SAAA,EAAG;YAAC,QAAQ;SAAC;QACrC,6BAA6B,KAAK,iBAAiB;QACnD,sBAAsB,KAAK,SAAS;QACpC,sBAAsB,KAAK,SAAS;QACpC,oBAAoB,KAAK,SAAS;IACpC,EAAA,OAAS,KAAK;QACZ,OAAO;YAAE,QAAQ;gBAAC,GAA6B;aAAA;QAAE;IACnD;IAEA,MAAM,EAAE,MAAM,cAAA,EAAgB,QAAQ,eAAA,CAAgB,CAAA,GAAI,MAAM,kBAAkB,SAAS,GAAG;IAC9F,IAAI,iBAAiB;QACnB,OAAO;YACL,QAAQ;gBACN,IAAI,8UAAA,CAAuB;oBACzB,6TAAQ,+BAAA,CAA6B,cAAA;oBACrC,6TAAQ,+BAAA,CAA6B,uBAAA;oBACrC,SAAS,CAAA,+BAAA,EAAkC,eAAA,CAAgB,CAAC,CAAC,EAAA;gBAC/D,CAAC;aACH;QACF;IACF;IAEA,IAAI,CAAC,gBAAgB;QACnB,OAAO;YACL,QAAQ;gBACN,yTAAI,yBAAA,CAAuB;oBACzB,6TAAQ,+BAAA,CAA6B,qBAAA;oBACrC,SAAS;gBACX,CAAC;aACH;QACF;IACF;IAEA,OAAO;QAAE,MAAM;IAAQ;AACzB", "ignoreList": [0, 1, 2, 3, 4, 5]}}, {"offset": {"line": 561, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/constants.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/createRedirect.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/util/mergePreDefinedOptions.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/util/optionsAssertions.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/authenticateContext.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/authObjects.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/util/path.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/AbstractApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/ActorTokenApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/AccountlessApplicationsAPI.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/AllowlistIdentifierApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/APIKeysApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/BetaFeaturesApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/BlocklistIdentifierApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/ClientApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/DomainApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/EmailAddressApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/IdPOAuthAccessTokenApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/InstanceApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/InvitationApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/MachineTokensApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/JwksApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/JwtTemplatesApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/OrganizationApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/OAuthApplicationsApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/PhoneNumberApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/ProxyCheckApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/RedirectUrlApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/SamlConnectionApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/SessionApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/SignInTokenApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/SignUpApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/TestingTokenApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/UserApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/WaitlistEntryApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/endpoints/WebhookApi.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/request.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/AccountlessApplication.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/ActorToken.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/AllowlistIdentifier.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/APIKey.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/BlocklistIdentifier.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Session.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Client.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/CnameTarget.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Cookies.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/DeletedObject.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Domain.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Email.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/IdentificationLink.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Verification.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/EmailAddress.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/ExternalAccount.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/IdPOAuthAccessToken.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Instance.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/InstanceRestrictions.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/InstanceSettings.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Invitation.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/JSON.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/MachineToken.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/JwtTemplate.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/OauthAccessToken.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/OAuthApplication.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Organization.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/OrganizationInvitation.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/OrganizationMembership.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/OrganizationSettings.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/PhoneNumber.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/ProxyCheck.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/RedirectUrl.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/SamlConnection.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/SamlAccount.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/SignInTokens.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/SignUpAttempt.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/SMSMessage.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Token.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Web3Wallet.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/User.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/WaitlistEntry.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/resources/Deserializer.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/api/factory.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/tokenTypes.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/machine.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/authStatus.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/clerkRequest.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/clerkUrl.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/cookie.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/keys.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/verify.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/handshake.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/organizationMatcher.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/request.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/tokens/factory.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/util/decorateObjectWithResources.ts", "turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/internal.ts"], "sourcesContent": ["export const API_URL = 'https://api.clerk.com';\nexport const API_VERSION = 'v1';\n\nexport const USER_AGENT = `${PACKAGE_NAME}@${PACKAGE_VERSION}`;\nexport const MAX_CACHE_LAST_UPDATED_AT_SECONDS = 5 * 60;\nexport const SUPPORTED_BAPI_VERSION = '2025-04-10';\n\nconst Attributes = {\n  AuthToken: '__clerkAuthToken',\n  AuthSignature: '__clerkAuthSignature',\n  AuthStatus: '__clerkAuthStatus',\n  AuthReason: '__clerkAuthReason',\n  AuthMessage: '__clerkAuthMessage',\n  ClerkUrl: '__clerkUrl',\n} as const;\n\nconst Cookies = {\n  Session: '__session',\n  Refresh: '__refresh',\n  ClientUat: '__client_uat',\n  Handshake: '__clerk_handshake',\n  DevBrowser: '__clerk_db_jwt',\n  RedirectCount: '__clerk_redirect_count',\n  HandshakeNonce: '__clerk_handshake_nonce',\n} as const;\n\nconst QueryParameters = {\n  ClerkSynced: '__clerk_synced',\n  SuffixedCookies: 'suffixed_cookies',\n  ClerkRedirectUrl: '__clerk_redirect_url',\n  // use the reference to Cookies to indicate that it's the same value\n  DevBrowser: Cookies.DevBrowser,\n  Handshake: Cookies.Handshake,\n  HandshakeHelp: '__clerk_help',\n  LegacyDevBrowser: '__dev_session',\n  HandshakeReason: '__clerk_hs_reason',\n  HandshakeNonce: Cookies.HandshakeNonce,\n} as const;\n\nconst Headers = {\n  Accept: 'accept',\n  AuthMessage: 'x-clerk-auth-message',\n  Authorization: 'authorization',\n  AuthReason: 'x-clerk-auth-reason',\n  AuthSignature: 'x-clerk-auth-signature',\n  AuthStatus: 'x-clerk-auth-status',\n  AuthToken: 'x-clerk-auth-token',\n  CacheControl: 'cache-control',\n  ClerkRedirectTo: 'x-clerk-redirect-to',\n  ClerkRequestData: 'x-clerk-request-data',\n  ClerkUrl: 'x-clerk-clerk-url',\n  CloudFrontForwardedProto: 'cloudfront-forwarded-proto',\n  ContentType: 'content-type',\n  ContentSecurityPolicy: 'content-security-policy',\n  ContentSecurityPolicyReportOnly: 'content-security-policy-report-only',\n  EnableDebug: 'x-clerk-debug',\n  ForwardedHost: 'x-forwarded-host',\n  ForwardedPort: 'x-forwarded-port',\n  ForwardedProto: 'x-forwarded-proto',\n  Host: 'host',\n  Location: 'location',\n  Nonce: 'x-nonce',\n  Origin: 'origin',\n  Referrer: 'referer',\n  SecFetchDest: 'sec-fetch-dest',\n  UserAgent: 'user-agent',\n  ReportingEndpoints: 'reporting-endpoints',\n} as const;\n\nconst ContentTypes = {\n  Json: 'application/json',\n} as const;\n\n/**\n * @internal\n */\nexport const constants = {\n  Attributes,\n  Cookies,\n  Headers,\n  ContentTypes,\n  QueryParameters,\n} as const;\n\nexport type Constants = typeof constants;\n", "import { buildAccountsBaseUrl } from '@clerk/shared/buildAccountsBaseUrl';\nimport type { SessionStatusClaim } from '@clerk/types';\n\nimport { constants } from './constants';\nimport { errorThrower, parsePublishableKey } from './util/shared';\n\nconst buildUrl = (\n  _baseUrl: string | URL,\n  _targetUrl: string | URL,\n  _returnBackUrl?: string | URL | null,\n  _devBrowserToken?: string | null,\n) => {\n  if (_baseUrl === '') {\n    return legacyBuildUrl(_targetUrl.toString(), _returnBackUrl?.toString());\n  }\n\n  const baseUrl = new URL(_baseUrl);\n  const returnBackUrl = _returnBackUrl ? new URL(_returnBackUrl, baseUrl) : undefined;\n  const res = new URL(_targetUrl, baseUrl);\n\n  if (returnBackUrl) {\n    res.searchParams.set('redirect_url', returnBackUrl.toString());\n  }\n  // For cross-origin redirects, we need to pass the dev browser token for URL session syncing\n  if (_devBrowserToken && baseUrl.hostname !== res.hostname) {\n    res.searchParams.set(constants.QueryParameters.DevBrowser, _devBrowserToken);\n  }\n  return res.toString();\n};\n\n/**\n * In v5, we deprecated the top-level redirectToSignIn and redirectToSignUp functions\n * in favor of the new auth().redirectToSignIn helpers\n * In order to allow for a smooth transition, we need to support the legacy redirectToSignIn for now\n * as we will remove it in v6.\n * In order to make sure that the legacy function works as expected, we will use legacyBuildUrl\n * to build the url if baseUrl is not provided (which is the case for legacy redirectToSignIn)\n * This function can be safely removed when we remove the legacy redirectToSignIn function\n */\nconst legacyBuildUrl = (targetUrl: string, redirectUrl?: string) => {\n  let url;\n  if (!targetUrl.startsWith('http')) {\n    if (!redirectUrl || !redirectUrl.startsWith('http')) {\n      throw new Error('destination url or return back url should be an absolute path url!');\n    }\n\n    const baseURL = new URL(redirectUrl);\n    url = new URL(targetUrl, baseURL.origin);\n  } else {\n    url = new URL(targetUrl);\n  }\n\n  if (redirectUrl) {\n    url.searchParams.set('redirect_url', redirectUrl);\n  }\n\n  return url.toString();\n};\n\ntype RedirectAdapter<RedirectReturn> = (url: string) => RedirectReturn;\ntype RedirectToParams = { returnBackUrl?: string | URL | null };\nexport type RedirectFun<ReturnType> = (params?: RedirectToParams) => ReturnType;\n\n/**\n * @internal\n */\ntype CreateRedirect = <ReturnType>(params: {\n  publishableKey: string;\n  devBrowserToken?: string;\n  redirectAdapter: RedirectAdapter<ReturnType>;\n  baseUrl: URL | string;\n  signInUrl?: URL | string;\n  signUpUrl?: URL | string;\n  sessionStatus?: SessionStatusClaim | null;\n}) => {\n  redirectToSignIn: RedirectFun<ReturnType>;\n  redirectToSignUp: RedirectFun<ReturnType>;\n};\n\nexport const createRedirect: CreateRedirect = params => {\n  const { publishableKey, redirectAdapter, signInUrl, signUpUrl, baseUrl, sessionStatus } = params;\n  const parsedPublishableKey = parsePublishableKey(publishableKey);\n  const frontendApi = parsedPublishableKey?.frontendApi;\n  const isDevelopment = parsedPublishableKey?.instanceType === 'development';\n  const accountsBaseUrl = buildAccountsBaseUrl(frontendApi);\n  const hasPendingStatus = sessionStatus === 'pending';\n\n  const redirectToTasks = (url: string | URL, { returnBackUrl }: RedirectToParams) => {\n    return redirectAdapter(\n      buildUrl(baseUrl, `${url}/tasks`, returnBackUrl, isDevelopment ? params.devBrowserToken : null),\n    );\n  };\n\n  const redirectToSignUp = ({ returnBackUrl }: RedirectToParams = {}) => {\n    if (!signUpUrl && !accountsBaseUrl) {\n      errorThrower.throwMissingPublishableKeyError();\n    }\n\n    const accountsSignUpUrl = `${accountsBaseUrl}/sign-up`;\n\n    // Allows redirection to SignInOrUp path\n    function buildSignUpUrl(signIn: string | URL | undefined) {\n      if (!signIn) {\n        return;\n      }\n      const url = new URL(signIn, baseUrl);\n      url.pathname = `${url.pathname}/create`;\n      return url.toString();\n    }\n\n    const targetUrl = signUpUrl || buildSignUpUrl(signInUrl) || accountsSignUpUrl;\n\n    if (hasPendingStatus) {\n      return redirectToTasks(targetUrl, { returnBackUrl });\n    }\n\n    return redirectAdapter(buildUrl(baseUrl, targetUrl, returnBackUrl, isDevelopment ? params.devBrowserToken : null));\n  };\n\n  const redirectToSignIn = ({ returnBackUrl }: RedirectToParams = {}) => {\n    if (!signInUrl && !accountsBaseUrl) {\n      errorThrower.throwMissingPublishableKeyError();\n    }\n\n    const accountsSignInUrl = `${accountsBaseUrl}/sign-in`;\n    const targetUrl = signInUrl || accountsSignInUrl;\n\n    if (hasPendingStatus) {\n      return redirectToTasks(targetUrl, { returnBackUrl });\n    }\n\n    return redirectAdapter(buildUrl(baseUrl, targetUrl, returnBackUrl, isDevelopment ? params.devBrowserToken : null));\n  };\n\n  return { redirectToSignUp, redirectToSignIn };\n};\n", "export function mergePreDefinedOptions<T extends Record<string, any>>(preDefinedOptions: T, options: Partial<T>): T {\n  return Object.keys(preDefinedOptions).reduce(\n    (obj: T, key: string) => {\n      return { ...obj, [key]: options[key] || obj[key] };\n    },\n    { ...preDefinedOptions },\n  );\n}\n", "import { parsePublishable<PERSON>ey } from './shared';\n\nexport function assertValidSecretKey(val: unknown): asserts val is string {\n  if (!val || typeof val !== 'string') {\n    throw Error('Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.');\n  }\n\n  //TODO: Check if the key is invalid and throw error\n}\n\nexport function assertValidPublishableKey(val: unknown): asserts val is string {\n  parsePublishableKey(val as string | undefined, { fatal: true });\n}\n", "import type { Jwt } from '@clerk/types';\n\nimport { constants } from '../constants';\nimport { decodeJwt } from '../jwt/verifyJwt';\nimport { runtime } from '../runtime';\nimport { assertValidPublishableKey } from '../util/optionsAssertions';\nimport { getCookieSuffix, getSuffixedCookieName, parsePublishableKey } from '../util/shared';\nimport type { ClerkRequest } from './clerkRequest';\nimport type { AuthenticateRequestOptions } from './types';\n\ninterface AuthenticateContext extends AuthenticateRequestOptions {\n  // header-based values\n  tokenInHeader: string | undefined;\n  origin: string | undefined;\n  host: string | undefined;\n  forwardedHost: string | undefined;\n  forwardedProto: string | undefined;\n  referrer: string | undefined;\n  userAgent: string | undefined;\n  secFetchDest: string | undefined;\n  accept: string | undefined;\n  // cookie-based values\n  sessionTokenInCookie: string | undefined;\n  refreshTokenInCookie: string | undefined;\n  clientUat: number;\n  // handshake-related values\n  devBrowserToken: string | undefined;\n  handshakeNonce: string | undefined;\n  handshakeToken: string | undefined;\n  handshakeRedirectLoopCounter: number;\n\n  // url derived from headers\n  clerkUrl: URL;\n  // enforce existence of the following props\n  publishableKey: string;\n  instanceType: string;\n  frontendApi: string;\n}\n\n/**\n * All data required to authenticate a request.\n * This is the data we use to decide whether a request\n * is in a signed in or signed out state or if we need\n * to perform a handshake.\n */\nclass AuthenticateContext implements AuthenticateContext {\n  /**\n   * Retrieves the session token from either the cookie or the header.\n   *\n   * @returns {string | undefined} The session token if available, otherwise undefined.\n   */\n  public get sessionToken(): string | undefined {\n    return this.sessionTokenInCookie || this.tokenInHeader;\n  }\n\n  public constructor(\n    private cookieSuffix: string,\n    private clerkRequest: ClerkRequest,\n    options: AuthenticateRequestOptions,\n  ) {\n    // Even though the options are assigned to this later in this function\n    // we set the publishableKey here because it is being used in cookies/headers/handshake-values\n    // as part of getMultipleAppsCookie\n    this.initPublishableKeyValues(options);\n    this.initHeaderValues();\n    // initCookieValues should be used before initHandshakeValues because it depends on suffixedCookies\n    this.initCookieValues();\n    this.initHandshakeValues();\n    Object.assign(this, options);\n    this.clerkUrl = this.clerkRequest.clerkUrl;\n  }\n\n  public usesSuffixedCookies(): boolean {\n    const suffixedClientUat = this.getSuffixedCookie(constants.Cookies.ClientUat);\n    const clientUat = this.getCookie(constants.Cookies.ClientUat);\n    const suffixedSession = this.getSuffixedCookie(constants.Cookies.Session) || '';\n    const session = this.getCookie(constants.Cookies.Session) || '';\n\n    // In the case of malformed session cookies (eg missing the iss claim), we should\n    // use the un-suffixed cookies to return signed-out state instead of triggering\n    // handshake\n    if (session && !this.tokenHasIssuer(session)) {\n      return false;\n    }\n\n    // If there's a token in un-suffixed, and it doesn't belong to this\n    // instance, then we must trust suffixed\n    if (session && !this.tokenBelongsToInstance(session)) {\n      return true;\n    }\n\n    // If there are no suffixed cookies use un-suffixed\n    if (!suffixedClientUat && !suffixedSession) {\n      return false;\n    }\n\n    const { data: sessionData } = decodeJwt(session);\n    const sessionIat = sessionData?.payload.iat || 0;\n    const { data: suffixedSessionData } = decodeJwt(suffixedSession);\n    const suffixedSessionIat = suffixedSessionData?.payload.iat || 0;\n\n    // Both indicate signed in, but un-suffixed is newer\n    // Trust un-suffixed because it's newer\n    if (suffixedClientUat !== '0' && clientUat !== '0' && sessionIat > suffixedSessionIat) {\n      return false;\n    }\n\n    // Suffixed indicates signed out, but un-suffixed indicates signed in\n    // Trust un-suffixed because it gets set with both new and old clerk.js,\n    // so we can assume it's newer\n    if (suffixedClientUat === '0' && clientUat !== '0') {\n      return false;\n    }\n\n    // Suffixed indicates signed in, un-suffixed indicates signed out\n    // This is the tricky one\n\n    // In production, suffixed_uat should be set reliably, since it's\n    // set by FAPI and not clerk.js. So in the scenario where a developer\n    // downgrades, the state will look like this:\n    // - un-suffixed session cookie: empty\n    // - un-suffixed uat: 0\n    // - suffixed session cookie: (possibly filled, possibly empty)\n    // - suffixed uat: 0\n\n    // Our SDK honors client_uat over the session cookie, so we don't\n    // need a special case for production. We can rely on suffixed,\n    // and the fact that the suffixed uat is set properly means and\n    // suffixed session cookie will be ignored.\n\n    // The important thing to make sure we have a test that confirms\n    // the user ends up as signed out in this scenario, and the suffixed\n    // session cookie is ignored\n\n    // In development, suffixed_uat is not set reliably, since it's done\n    // by clerk.js. If the developer downgrades to a pinned version of\n    // clerk.js, the suffixed uat will no longer be updated\n\n    // The best we can do is look to see if the suffixed token is expired.\n    // This means that, if a developer downgrades, and then immediately\n    // signs out, all in the span of 1 minute, then they will inadvertently\n    // remain signed in for the rest of that minute. This is a known\n    // limitation of the strategy but seems highly unlikely.\n    if (this.instanceType !== 'production') {\n      const isSuffixedSessionExpired = this.sessionExpired(suffixedSessionData);\n      if (suffixedClientUat !== '0' && clientUat === '0' && isSuffixedSessionExpired) {\n        return false;\n      }\n    }\n\n    // If a suffixed session cookie exists but the corresponding client_uat cookie is missing, fallback to using\n    // unsuffixed cookies.\n    // This handles the scenario where an app has been deployed using an SDK version that supports suffixed\n    // cookies, but FAPI for its Clerk instance has the feature disabled (eg: if we need to temporarily disable the feature).\n    if (!suffixedClientUat && suffixedSession) {\n      return false;\n    }\n\n    return true;\n  }\n\n  private initPublishableKeyValues(options: AuthenticateRequestOptions) {\n    assertValidPublishableKey(options.publishableKey);\n    this.publishableKey = options.publishableKey;\n\n    const pk = parsePublishableKey(this.publishableKey, {\n      fatal: true,\n      proxyUrl: options.proxyUrl,\n      domain: options.domain,\n      isSatellite: options.isSatellite,\n    });\n    this.instanceType = pk.instanceType;\n    this.frontendApi = pk.frontendApi;\n  }\n\n  private initHeaderValues() {\n    this.tokenInHeader = this.parseAuthorizationHeader(this.getHeader(constants.Headers.Authorization));\n    this.origin = this.getHeader(constants.Headers.Origin);\n    this.host = this.getHeader(constants.Headers.Host);\n    this.forwardedHost = this.getHeader(constants.Headers.ForwardedHost);\n    this.forwardedProto =\n      this.getHeader(constants.Headers.CloudFrontForwardedProto) || this.getHeader(constants.Headers.ForwardedProto);\n    this.referrer = this.getHeader(constants.Headers.Referrer);\n    this.userAgent = this.getHeader(constants.Headers.UserAgent);\n    this.secFetchDest = this.getHeader(constants.Headers.SecFetchDest);\n    this.accept = this.getHeader(constants.Headers.Accept);\n  }\n\n  private initCookieValues() {\n    // suffixedCookies needs to be set first because it's used in getMultipleAppsCookie\n    this.sessionTokenInCookie = this.getSuffixedOrUnSuffixedCookie(constants.Cookies.Session);\n    this.refreshTokenInCookie = this.getSuffixedCookie(constants.Cookies.Refresh);\n    this.clientUat = Number.parseInt(this.getSuffixedOrUnSuffixedCookie(constants.Cookies.ClientUat) || '') || 0;\n  }\n\n  private initHandshakeValues() {\n    this.devBrowserToken =\n      this.getQueryParam(constants.QueryParameters.DevBrowser) ||\n      this.getSuffixedOrUnSuffixedCookie(constants.Cookies.DevBrowser);\n    // Using getCookie since we don't suffix the handshake token cookie\n    this.handshakeToken =\n      this.getQueryParam(constants.QueryParameters.Handshake) || this.getCookie(constants.Cookies.Handshake);\n    this.handshakeRedirectLoopCounter = Number(this.getCookie(constants.Cookies.RedirectCount)) || 0;\n    this.handshakeNonce =\n      this.getQueryParam(constants.QueryParameters.HandshakeNonce) || this.getCookie(constants.Cookies.HandshakeNonce);\n  }\n\n  private getQueryParam(name: string) {\n    return this.clerkRequest.clerkUrl.searchParams.get(name);\n  }\n\n  private getHeader(name: string) {\n    return this.clerkRequest.headers.get(name) || undefined;\n  }\n\n  private getCookie(name: string) {\n    return this.clerkRequest.cookies.get(name) || undefined;\n  }\n\n  private getSuffixedCookie(name: string) {\n    return this.getCookie(getSuffixedCookieName(name, this.cookieSuffix)) || undefined;\n  }\n\n  private getSuffixedOrUnSuffixedCookie(cookieName: string) {\n    if (this.usesSuffixedCookies()) {\n      return this.getSuffixedCookie(cookieName);\n    }\n    return this.getCookie(cookieName);\n  }\n\n  private parseAuthorizationHeader(authorizationHeader: string | undefined | null): string | undefined {\n    if (!authorizationHeader) {\n      return undefined;\n    }\n\n    const [scheme, token] = authorizationHeader.split(' ', 2);\n\n    if (!token) {\n      // No scheme specified, treat the entire value as the token\n      return scheme;\n    }\n\n    if (scheme === 'Bearer') {\n      return token;\n    }\n\n    // Skip all other schemes\n    return undefined;\n  }\n\n  private tokenHasIssuer(token: string): boolean {\n    const { data, errors } = decodeJwt(token);\n    if (errors) {\n      return false;\n    }\n    return !!data.payload.iss;\n  }\n\n  private tokenBelongsToInstance(token: string): boolean {\n    if (!token) {\n      return false;\n    }\n\n    const { data, errors } = decodeJwt(token);\n    if (errors) {\n      return false;\n    }\n    const tokenIssuer = data.payload.iss.replace(/https?:\\/\\//gi, '');\n    return this.frontendApi === tokenIssuer;\n  }\n\n  private sessionExpired(jwt: Jwt | undefined): boolean {\n    return !!jwt && jwt?.payload.exp <= (Date.now() / 1000) >> 0;\n  }\n}\n\nexport type { AuthenticateContext };\n\nexport const createAuthenticateContext = async (\n  clerkRequest: ClerkRequest,\n  options: AuthenticateRequestOptions,\n): Promise<AuthenticateContext> => {\n  const cookieSuffix = options.publishableKey\n    ? await getCookieSuffix(options.publishableKey, runtime.crypto.subtle)\n    : '';\n  return new AuthenticateContext(cookieSuffix, clerkRequest, options);\n};\n", "import { createCheckAuthorization } from '@clerk/shared/authorization';\nimport { __experimental_JWTPayloadToAuthObjectProperties } from '@clerk/shared/jwtPayloadParser';\nimport type {\n  CheckAuthorizationFromSessionClaims,\n  Jwt,\n  JwtPayload,\n  PendingSessionOptions,\n  ServerGetToken,\n  ServerGetTokenOptions,\n  SessionStatusClaim,\n  SharedSignedInAuthObjectProperties,\n} from '@clerk/types';\n\nimport type { APIKey, CreateBackendApiOptions, IdPOAuthAccessToken, MachineToken } from '../api';\nimport { createBackendApiClient } from '../api';\nimport { isTokenTypeAccepted } from '../internal';\nimport type { AuthenticateContext } from './authenticateContext';\nimport { isMachineTokenType } from './machine';\nimport type { MachineTokenType, SessionTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\nimport type { AuthenticateRequestOptions, MachineAuthType } from './types';\n\n/**\n * @inline\n */\ntype AuthObjectDebugData = Record<string, any>;\n/**\n * @inline\n */\ntype AuthObjectDebug = () => AuthObjectDebugData;\n\ntype Claims = Record<string, any>;\n\n/**\n * @internal\n */\nexport type SignedInAuthObjectOptions = CreateBackendApiOptions & {\n  token: string;\n};\n\n/**\n * @internal\n */\nexport type SignedInAuthObject = SharedSignedInAuthObjectProperties & {\n  /**\n   * The allowed token type.\n   */\n  tokenType: SessionTokenType;\n  /**\n   * A function that gets the current user's [session token](https://clerk.com/docs/backend-requests/resources/session-tokens) or a [custom JWT template](https://clerk.com/docs/backend-requests/jwt-templates).\n   */\n  getToken: ServerGetToken;\n  /**\n   * A function that checks if the user has an organization role or custom permission.\n   */\n  has: CheckAuthorizationFromSessionClaims;\n  /**\n   * Used to help debug issues when using Clerk in development.\n   */\n  debug: AuthObjectDebug;\n  isAuthenticated: true;\n};\n\n/**\n * @internal\n */\nexport type SignedOutAuthObject = {\n  sessionClaims: null;\n  sessionId: null;\n  sessionStatus: SessionStatusClaim | null;\n  actor: null;\n  tokenType: SessionTokenType;\n  userId: null;\n  orgId: null;\n  orgRole: null;\n  orgSlug: null;\n  orgPermissions: null;\n  factorVerificationAge: null;\n  getToken: ServerGetToken;\n  has: CheckAuthorizationFromSessionClaims;\n  debug: AuthObjectDebug;\n  isAuthenticated: false;\n};\n\n/**\n * Extended properties specific to each machine token type.\n * While all machine token types share common properties (id, name, subject, etc),\n * this type defines the additional properties that are unique to each token type.\n *\n * @template TAuthenticated - Whether the machine object is authenticated or not\n */\ntype MachineObjectExtendedProperties<TAuthenticated extends boolean> = {\n  api_key: TAuthenticated extends true\n    ?\n        | { name: string; claims: Claims | null; userId: string; orgId: null }\n        | { name: string; claims: Claims | null; userId: null; orgId: string }\n    : { name: null; claims: null; userId: null; orgId: null };\n  machine_token: {\n    name: TAuthenticated extends true ? string : null;\n    claims: TAuthenticated extends true ? Claims | null : null;\n    machineId: TAuthenticated extends true ? string : null;\n  };\n  oauth_token: {\n    userId: TAuthenticated extends true ? string : null;\n    clientId: TAuthenticated extends true ? string : null;\n  };\n};\n\n/**\n * @internal\n *\n * Uses `T extends any` to create a distributive conditional type.\n * This ensures that union types like `'api_key' | 'oauth_token'` are processed\n * individually, creating proper discriminated unions where each token type\n * gets its own distinct properties (e.g., oauth_token won't have claims).\n */\nexport type AuthenticatedMachineObject<T extends MachineTokenType = MachineTokenType> = T extends any\n  ? {\n      id: string;\n      subject: string;\n      scopes: string[];\n      getToken: () => Promise<string>;\n      has: CheckAuthorizationFromSessionClaims;\n      debug: AuthObjectDebug;\n      tokenType: T;\n      isAuthenticated: true;\n    } & MachineObjectExtendedProperties<true>[T]\n  : never;\n\n/**\n * @internal\n *\n * Uses `T extends any` to create a distributive conditional type.\n * This ensures that union types like `'api_key' | 'oauth_token'` are processed\n * individually, creating proper discriminated unions where each token type\n * gets its own distinct properties (e.g., oauth_token won't have claims).\n */\nexport type UnauthenticatedMachineObject<T extends MachineTokenType = MachineTokenType> = T extends any\n  ? {\n      id: null;\n      subject: null;\n      scopes: null;\n      getToken: () => Promise<null>;\n      has: CheckAuthorizationFromSessionClaims;\n      debug: AuthObjectDebug;\n      tokenType: T;\n      isAuthenticated: false;\n    } & MachineObjectExtendedProperties<false>[T]\n  : never;\n\nexport type InvalidTokenAuthObject = {\n  isAuthenticated: false;\n  tokenType: null;\n  getToken: () => Promise<null>;\n  has: () => false;\n  debug: AuthObjectDebug;\n};\n\n/**\n * @interface\n */\nexport type AuthObject =\n  | SignedInAuthObject\n  | SignedOutAuthObject\n  | AuthenticatedMachineObject\n  | UnauthenticatedMachineObject\n  | InvalidTokenAuthObject;\n\nconst createDebug = (data: AuthObjectDebugData | undefined) => {\n  return () => {\n    const res = { ...data };\n    res.secretKey = (res.secretKey || '').substring(0, 7);\n    res.jwtKey = (res.jwtKey || '').substring(0, 7);\n    return { ...res };\n  };\n};\n\n/**\n * @internal\n */\nexport function signedInAuthObject(\n  authenticateContext: Partial<AuthenticateContext>,\n  sessionToken: string,\n  sessionClaims: JwtPayload,\n): SignedInAuthObject {\n  const { actor, sessionId, sessionStatus, userId, orgId, orgRole, orgSlug, orgPermissions, factorVerificationAge } =\n    __experimental_JWTPayloadToAuthObjectProperties(sessionClaims);\n  const apiClient = createBackendApiClient(authenticateContext);\n  const getToken = createGetToken({\n    sessionId,\n    sessionToken,\n    fetcher: async (...args) => (await apiClient.sessions.getToken(...args)).jwt,\n  });\n  return {\n    tokenType: TokenType.SessionToken,\n    actor,\n    sessionClaims,\n    sessionId,\n    sessionStatus,\n    userId,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n    getToken,\n    has: createCheckAuthorization({\n      orgId,\n      orgRole,\n      orgPermissions,\n      userId,\n      factorVerificationAge,\n      features: (sessionClaims.fea as string) || '',\n      plans: (sessionClaims.pla as string) || '',\n    }),\n    debug: createDebug({ ...authenticateContext, sessionToken }),\n    isAuthenticated: true,\n  };\n}\n\n/**\n * @internal\n */\nexport function signedOutAuthObject(\n  debugData?: AuthObjectDebugData,\n  initialSessionStatus?: SessionStatusClaim,\n): SignedOutAuthObject {\n  return {\n    tokenType: TokenType.SessionToken,\n    sessionClaims: null,\n    sessionId: null,\n    sessionStatus: initialSessionStatus ?? null,\n    userId: null,\n    actor: null,\n    orgId: null,\n    orgRole: null,\n    orgSlug: null,\n    orgPermissions: null,\n    factorVerificationAge: null,\n    getToken: () => Promise.resolve(null),\n    has: () => false,\n    debug: createDebug(debugData),\n    isAuthenticated: false,\n  };\n}\n\n/**\n * @internal\n */\nexport function authenticatedMachineObject<T extends MachineTokenType>(\n  tokenType: T,\n  token: string,\n  verificationResult: MachineAuthType,\n  debugData?: AuthObjectDebugData,\n): AuthenticatedMachineObject<T> {\n  const baseObject = {\n    id: verificationResult.id,\n    subject: verificationResult.subject,\n    getToken: () => Promise.resolve(token),\n    has: () => false,\n    debug: createDebug(debugData),\n    isAuthenticated: true,\n  };\n\n  // Type assertions are safe here since we know the verification result type matches the tokenType.\n  // We need these assertions because TS can't infer the specific type\n  // just from the tokenType discriminator.\n\n  switch (tokenType) {\n    case TokenType.ApiKey: {\n      const result = verificationResult as APIKey;\n      return {\n        ...baseObject,\n        tokenType,\n        name: result.name,\n        claims: result.claims,\n        scopes: result.scopes,\n        userId: result.subject.startsWith('user_') ? result.subject : null,\n        orgId: result.subject.startsWith('org_') ? result.subject : null,\n      } as unknown as AuthenticatedMachineObject<T>;\n    }\n    case TokenType.MachineToken: {\n      const result = verificationResult as MachineToken;\n      return {\n        ...baseObject,\n        tokenType,\n        name: result.name,\n        claims: result.claims,\n        scopes: result.scopes,\n        machineId: result.subject,\n      } as unknown as AuthenticatedMachineObject<T>;\n    }\n    case TokenType.OAuthToken: {\n      const result = verificationResult as IdPOAuthAccessToken;\n      return {\n        ...baseObject,\n        tokenType,\n        scopes: result.scopes,\n        userId: result.subject,\n        clientId: result.clientId,\n      } as unknown as AuthenticatedMachineObject<T>;\n    }\n    default:\n      throw new Error(`Invalid token type: ${tokenType}`);\n  }\n}\n\n/**\n * @internal\n */\nexport function unauthenticatedMachineObject<T extends MachineTokenType>(\n  tokenType: T,\n  debugData?: AuthObjectDebugData,\n): UnauthenticatedMachineObject<T> {\n  const baseObject = {\n    id: null,\n    subject: null,\n    scopes: null,\n    has: () => false,\n    getToken: () => Promise.resolve(null),\n    debug: createDebug(debugData),\n    isAuthenticated: false,\n  };\n\n  switch (tokenType) {\n    case TokenType.ApiKey: {\n      return {\n        ...baseObject,\n        tokenType,\n        name: null,\n        claims: null,\n        scopes: null,\n        userId: null,\n        orgId: null,\n      } as unknown as UnauthenticatedMachineObject<T>;\n    }\n    case TokenType.MachineToken: {\n      return {\n        ...baseObject,\n        tokenType,\n        name: null,\n        claims: null,\n        scopes: null,\n        machineId: null,\n      } as unknown as UnauthenticatedMachineObject<T>;\n    }\n    case TokenType.OAuthToken: {\n      return {\n        ...baseObject,\n        tokenType,\n        scopes: null,\n        userId: null,\n        clientId: null,\n      } as unknown as UnauthenticatedMachineObject<T>;\n    }\n    default:\n      throw new Error(`Invalid token type: ${tokenType}`);\n  }\n}\n\n/**\n * @internal\n */\nexport function invalidTokenAuthObject(): InvalidTokenAuthObject {\n  return {\n    isAuthenticated: false,\n    tokenType: null,\n    getToken: () => Promise.resolve(null),\n    has: () => false,\n    debug: () => ({}),\n  };\n}\n\n/**\n * Auth objects moving through the server -> client boundary need to be serializable\n * as we need to ensure that they can be transferred via the network as pure strings.\n * Some frameworks like Remix or Next (/pages dir only) handle this serialization by simply\n * ignoring any non-serializable keys, however Nextjs /app directory is stricter and\n * throws an error if a non-serializable value is found.\n *\n * @internal\n */\nexport const makeAuthObjectSerializable = <T extends Record<string, unknown>>(obj: T): T => {\n  // remove any non-serializable props from the returned object\n\n  const { debug, getToken, has, ...rest } = obj as unknown as AuthObject;\n  return rest as unknown as T;\n};\n\ntype TokenFetcher = (sessionId: string, template: string) => Promise<string>;\n\ntype CreateGetToken = (params: { sessionId: string; sessionToken: string; fetcher: TokenFetcher }) => ServerGetToken;\n\nconst createGetToken: CreateGetToken = params => {\n  const { fetcher, sessionToken, sessionId } = params || {};\n\n  return async (options: ServerGetTokenOptions = {}) => {\n    if (!sessionId) {\n      return null;\n    }\n\n    if (options.template) {\n      return fetcher(sessionId, options.template);\n    }\n\n    return sessionToken;\n  };\n};\n\n/**\n * @internal\n */\nexport const getAuthObjectFromJwt = (\n  jwt: Jwt,\n  { treatPendingAsSignedOut = true, ...options }: PendingSessionOptions & Partial<AuthenticateContext>,\n) => {\n  const authObject = signedInAuthObject(options, jwt.raw.text, jwt.payload);\n\n  if (treatPendingAsSignedOut && authObject.sessionStatus === 'pending') {\n    return signedOutAuthObject(options, authObject.sessionStatus);\n  }\n\n  return authObject;\n};\n\n/**\n * @internal\n * Returns an auth object matching the requested token type(s).\n *\n * If the parsed token type does not match any in acceptsToken, returns:\n *   - an unauthenticated machine object for the first machine token type in acceptsToken (if present), or\n *   - a signed-out session object otherwise.\n *\n * This ensures the returned object always matches the developer's intent.\n */\nexport function getAuthObjectForAcceptedToken({\n  authObject,\n  acceptsToken = TokenType.SessionToken,\n}: {\n  authObject: AuthObject;\n  acceptsToken: AuthenticateRequestOptions['acceptsToken'];\n}): AuthObject {\n  if (acceptsToken === 'any') {\n    return authObject;\n  }\n\n  if (Array.isArray(acceptsToken)) {\n    if (!isTokenTypeAccepted(authObject.tokenType, acceptsToken)) {\n      // If the token is not in the accepted array, return invalid token auth object\n      return invalidTokenAuthObject();\n    }\n    return authObject;\n  }\n\n  // Single value: Intent based\n  if (!isTokenTypeAccepted(authObject.tokenType, acceptsToken)) {\n    if (isMachineTokenType(acceptsToken)) {\n      return unauthenticatedMachineObject(acceptsToken, authObject.debug);\n    }\n    return signedOutAuthObject(authObject.debug);\n  }\n\n  return authObject;\n}\n", "const SEPARATOR = '/';\nconst MULTIPLE_SEPARATOR_REGEX = new RegExp('(?<!:)' + SEPARATOR + '{1,}', 'g');\n\ntype PathString = string | null | undefined;\n\nexport function joinPaths(...args: PathString[]): string {\n  return args\n    .filter(p => p)\n    .join(SEPARATOR)\n    .replace(MULTIPLE_SEPARATOR_REGEX, SEPARATOR);\n}\n", "import type { RequestFunction } from '../request';\n\nexport abstract class AbstractAPI {\n  constructor(protected request: RequestFunction) {}\n\n  protected requireId(id: string) {\n    if (!id) {\n      throw new Error('A valid resource ID is required.');\n    }\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { ActorToken } from '../resources/ActorToken';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/actor_tokens';\n\ntype ActorTokenActorCreateParams = {\n  /**\n   * The ID of the actor.\n   */\n  sub: string;\n  /**\n   * Additional properties of the actor.\n   */\n  additionalProperties?: { [k: string]: any };\n};\n\ntype ActorTokenCreateParams = {\n  /**\n   * The ID of the user being impersonated.\n   */\n  userId: string;\n  /**\n   * The actor payload. It needs to include a sub property which should contain the ID of the actor.\n   *\n   * @remarks\n   * This whole payload will be also included in the JWT session token.\n   */\n  actor: ActorTokenActorCreateParams;\n  /**\n   * Optional parameter to specify the life duration of the actor token in seconds.\n   *\n   * @remarks\n   * By default, the duration is 1 hour.\n   */\n  expiresInSeconds?: number | undefined;\n  /**\n   * The maximum duration that the session which will be created by the generated actor token should last.\n   *\n   * @remarks\n   * By default, the duration of a session created via an actor token, lasts 30 minutes.\n   */\n  sessionMaxDurationInSeconds?: number | undefined;\n};\n\nexport class ActorTokenAPI extends AbstractAPI {\n  public async create(params: ActorTokenCreateParams) {\n    return this.request<ActorToken>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revoke(actorTokenId: string) {\n    this.requireId(actorTokenId);\n    return this.request<ActorToken>({\n      method: 'POST',\n      path: joinPaths(basePath, actorTokenId, 'revoke'),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { AccountlessApplication } from '../resources/AccountlessApplication';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/accountless_applications';\n\nexport class AccountlessApplicationAPI extends AbstractAPI {\n  public async createAccountlessApplication() {\n    return this.request<AccountlessApplication>({\n      method: 'POST',\n      path: basePath,\n    });\n  }\n\n  public async completeAccountlessApplicationOnboarding() {\n    return this.request<AccountlessApplication>({\n      method: 'POST',\n      path: joinPaths(basePath, 'complete'),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { AllowlistIdentifier } from '../resources/AllowlistIdentifier';\nimport type { DeletedObject } from '../resources/DeletedObject';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/allowlist_identifiers';\n\ntype AllowlistIdentifierCreateParams = {\n  identifier: string;\n  notify: boolean;\n};\n\nexport class AllowlistIdentifierAPI extends AbstractAPI {\n  public async getAllowlistIdentifierList(params: ClerkPaginationRequest = {}) {\n    return this.request<PaginatedResourceResponse<AllowlistIdentifier[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async createAllowlistIdentifier(params: AllowlistIdentifierCreateParams) {\n    return this.request<AllowlistIdentifier>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async deleteAllowlistIdentifier(allowlistIdentifierId: string) {\n    this.requireId(allowlistIdentifierId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, allowlistIdentifierId),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { APIKey } from '../resources/APIKey';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/api_keys';\n\nexport class APIKeysAPI extends AbstractAPI {\n  async verifySecret(secret: string) {\n    return this.request<APIKey>({\n      method: 'POST',\n      path: joinPaths(basePath, 'verify'),\n      bodyParams: { secret },\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/beta_features';\n\ntype ChangeDomainParams = {\n  /**\n   * The new home URL of the production instance e.g. https://www.example.com\n   */\n  homeUrl?: string;\n  /**\n   * Whether this is a domain for a secondary app, meaning that any subdomain\n   * provided is significant and will be stored as part of the domain. This is\n   * useful for supporting multiple apps (one primary and multiple secondaries)\n   * on the same root domain (eTLD+1).\n   */\n  isSecondary?: boolean;\n};\n\nexport class BetaFeaturesAPI extends AbstractAPI {\n  /**\n   * Change the domain of a production instance.\n   *\n   * Changing the domain requires updating the DNS records accordingly, deploying new SSL certificates,\n   * updating your Social Connection's redirect URLs and setting the new keys in your code.\n   *\n   * @remarks\n   * WARNING: Changing your domain will invalidate all current user sessions (i.e. users will be logged out).\n   *          Also, while your application is being deployed, a small downtime is expected to occur.\n   */\n  public async changeDomain(params: ChangeDomainParams) {\n    return this.request<void>({\n      method: 'POST',\n      path: joinPaths(basePath, 'change_domain'),\n      bodyParams: params,\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { BlocklistIdentifier } from '../resources/BlocklistIdentifier';\nimport type { DeletedObject } from '../resources/DeletedObject';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/blocklist_identifiers';\n\ntype BlocklistIdentifierCreateParams = {\n  identifier: string;\n};\n\nexport class BlocklistIdentifierAPI extends AbstractAPI {\n  public async getBlocklistIdentifierList(params: ClerkPaginationRequest = {}) {\n    return this.request<PaginatedResourceResponse<BlocklistIdentifier[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async createBlocklistIdentifier(params: BlocklistIdentifierCreateParams) {\n    return this.request<BlocklistIdentifier>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async deleteBlocklistIdentifier(blocklistIdentifierId: string) {\n    this.requireId(blocklistIdentifierId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, blocklistIdentifierId),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { Client } from '../resources/Client';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { HandshakePayload } from '../resources/HandshakePayload';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/clients';\n\ntype GetHandshakePayloadParams = {\n  nonce: string;\n};\n\nexport class ClientAPI extends AbstractAPI {\n  public async getClientList(params: ClerkPaginationRequest = {}) {\n    return this.request<PaginatedResourceResponse<Client[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async getClient(clientId: string) {\n    this.requireId(clientId);\n    return this.request<Client>({\n      method: 'GET',\n      path: joinPaths(basePath, clientId),\n    });\n  }\n\n  public verifyClient(token: string) {\n    return this.request<Client>({\n      method: 'POST',\n      path: joinPaths(basePath, 'verify'),\n      bodyParams: { token },\n    });\n  }\n\n  public async getHandshakePayload(queryParams: GetHandshakePayloadParams) {\n    return this.request<HandshakePayload>({\n      method: 'GET',\n      path: joinPaths(basePath, 'handshake_payload'),\n      queryParams,\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { DeletedObject } from '../resources/DeletedObject';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { Domain } from '../resources/Domain';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/domains';\n\ntype AddDomainParams = {\n  /**\n   * The new domain name. For development instances, can contain the port, i.e myhostname:3000. For production instances, must be a valid FQDN, i.e mysite.com. Cannot contain protocol scheme.\n   */\n  name: string;\n  /**\n   * Marks the new domain as satellite. Only true is accepted at the moment.\n   */\n  is_satellite: boolean;\n  /**\n   * The full URL of the proxy which will forward requests to the Clerk Frontend API for this domain. Applicable only to production instances.\n   */\n  proxy_url?: string | null;\n};\n\ntype UpdateDomainParams = Partial<Pick<AddDomainParams, 'name' | 'proxy_url'>> & {\n  /**\n   * The ID of the domain that will be updated.\n   */\n  domainId: string;\n  /**\n   * Whether this is a domain for a secondary app, meaning that any subdomain provided is significant\n   * and will be stored as part of the domain. This is useful for supporting multiple apps\n   * (one primary and multiple secondaries) on the same root domain (eTLD+1).\n   */\n  is_secondary?: boolean | null;\n};\n\nexport class DomainAPI extends AbstractAPI {\n  public async list() {\n    return this.request<PaginatedResourceResponse<Domain[]>>({\n      method: 'GET',\n      path: basePath,\n    });\n  }\n\n  public async add(params: AddDomainParams) {\n    return this.request<Domain>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async update(params: UpdateDomainParams) {\n    const { domainId, ...bodyParams } = params;\n\n    this.requireId(domainId);\n\n    return this.request<Domain>({\n      method: 'PATCH',\n      path: joinPaths(basePath, domainId),\n      bodyParams: bodyParams,\n    });\n  }\n\n  /**\n   * Deletes a satellite domain for the instance.\n   * It is currently not possible to delete the instance's primary domain.\n   */\n  public async delete(satelliteDomainId: string) {\n    return this.deleteDomain(satelliteDomainId);\n  }\n\n  /**\n   * @deprecated Use `delete` instead\n   */\n  public async deleteDomain(satelliteDomainId: string) {\n    this.requireId(satelliteDomainId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, satelliteDomainId),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { DeletedObject, EmailAddress } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/email_addresses';\n\ntype CreateEmailAddressParams = {\n  userId: string;\n  emailAddress: string;\n  verified?: boolean;\n  primary?: boolean;\n};\n\ntype UpdateEmailAddressParams = {\n  verified?: boolean;\n  primary?: boolean;\n};\n\nexport class EmailAddressAPI extends AbstractAPI {\n  public async getEmailAddress(emailAddressId: string) {\n    this.requireId(emailAddressId);\n\n    return this.request<EmailAddress>({\n      method: 'GET',\n      path: joinPaths(basePath, emailAddressId),\n    });\n  }\n\n  public async createEmailAddress(params: CreateEmailAddressParams) {\n    return this.request<EmailAddress>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updateEmailAddress(emailAddressId: string, params: UpdateEmailAddressParams = {}) {\n    this.requireId(emailAddressId);\n\n    return this.request<EmailAddress>({\n      method: 'PATCH',\n      path: joinPaths(basePath, emailAddressId),\n      bodyParams: params,\n    });\n  }\n\n  public async deleteEmailAddress(emailAddressId: string) {\n    this.requireId(emailAddressId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, emailAddressId),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { IdPOAuthAccessToken } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/oauth_applications/access_tokens';\n\nexport class IdPOAuthAccessToken<PERSON>pi extends AbstractAPI {\n  async verifyAccessToken(accessToken: string) {\n    return this.request<IdPOAuthAccessToken>({\n      method: 'POST',\n      path: joinPaths(basePath, 'verify'),\n      bodyParams: { access_token: accessToken },\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { Instance } from '../resources/Instance';\nimport type { InstanceRestrictions } from '../resources/InstanceRestrictions';\nimport type { OrganizationSettings } from '../resources/OrganizationSettings';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/instance';\n\ntype UpdateParams = {\n  /**\n   * Toggles test mode for this instance, allowing the use of test email addresses and phone numbers.\n   *\n   * @remarks Defaults to true for development instances.\n   */\n  testMode?: boolean | null | undefined;\n  /**\n   * Whether the instance should be using the HIBP service to check passwords for breaches\n   */\n  hibp?: boolean | null | undefined;\n  /**\n   * The \"enhanced_email_deliverability\" feature will send emails from \"<EMAIL>\" instead of your domain.\n   *\n   * @remarks This can be helpful if you do not have a high domain reputation.\n   */\n  enhancedEmailDeliverability?: boolean | null | undefined;\n  supportEmail?: string | null | undefined;\n  clerkJsVersion?: string | null | undefined;\n  developmentOrigin?: string | null | undefined;\n  /**\n   * For browser-like stacks such as browser extensions, Electron, or Capacitor.js the instance allowed origins need to be updated with the request origin value.\n   *\n   * @remarks For Chrome extensions popup, background, or service worker pages the origin is chrome-extension://extension_uiid. For Electron apps the default origin is http://localhost:3000. For Capacitor, the origin is capacitor://localhost.\n   */\n  allowedOrigins?: Array<string> | undefined;\n  /**\n   * Whether the instance should use URL-based session syncing in development mode (i.e. without third-party cookies).\n   */\n  urlBasedSessionSyncing?: boolean | null | undefined;\n};\n\ntype UpdateRestrictionsParams = {\n  allowlist?: boolean | null | undefined;\n  blocklist?: boolean | null | undefined;\n  blockEmailSubaddresses?: boolean | null | undefined;\n  blockDisposableEmailDomains?: boolean | null | undefined;\n  ignoreDotsForGmailAddresses?: boolean | null | undefined;\n};\n\ntype UpdateOrganizationSettingsParams = {\n  enabled?: boolean | null | undefined;\n  maxAllowedMemberships?: number | null | undefined;\n  adminDeleteEnabled?: boolean | null | undefined;\n  domainsEnabled?: boolean | null | undefined;\n  /**\n   * Specifies which [enrollment modes](https://clerk.com/docs/organizations/verified-domains#enrollment-mode) to enable for your Organization Domains.\n   *\n   * @remarks Supported modes are 'automatic_invitation' & 'automatic_suggestion'.\n   */\n  domainsEnrollmentModes?: Array<string> | undefined;\n  /**\n   * Specifies what the default organization role is for an organization creator.\n   */\n  creatorRoleId?: string | null | undefined;\n  /**\n   * Specifies what the default organization role is for the organization domains.\n   */\n  domainsDefaultRoleId?: string | null | undefined;\n};\n\nexport class InstanceAPI extends AbstractAPI {\n  public async get() {\n    return this.request<Instance>({\n      method: 'GET',\n      path: basePath,\n    });\n  }\n\n  public async update(params: UpdateParams) {\n    return this.request<void>({\n      method: 'PATCH',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updateRestrictions(params: UpdateRestrictionsParams) {\n    return this.request<InstanceRestrictions>({\n      method: 'PATCH',\n      path: joinPaths(basePath, 'restrictions'),\n      bodyParams: params,\n    });\n  }\n\n  public async updateOrganizationSettings(params: UpdateOrganizationSettingsParams) {\n    return this.request<OrganizationSettings>({\n      method: 'PATCH',\n      path: joinPaths(basePath, 'organization_settings'),\n      bodyParams: params,\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { InvitationStatus } from '../resources/Enums';\nimport type { Invitation } from '../resources/Invitation';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/invitations';\n\ntype CreateParams = {\n  emailAddress: string;\n  redirectUrl?: string;\n  publicMetadata?: UserPublicMetadata;\n  notify?: boolean;\n  ignoreExisting?: boolean;\n};\n\ntype GetInvitationListParams = ClerkPaginationRequest<{\n  /**\n   * Filters invitations based on their status.\n   *\n   * @example\n   * Get all revoked invitations\n   * ```ts\n   * import { createClerkClient } from '@clerk/backend';\n   * const clerkClient = createClerkClient(...)\n   * await clerkClient.invitations.getInvitationList({ status: 'revoked' })\n   * ```\n   */\n  status?: InvitationStatus;\n  /**\n   * Filters invitations based on `email_address` or `id`.\n   *\n   * @example\n   * Get all invitations for a specific email address\n   * ```ts\n   * import { createClerkClient } from '@clerk/backend';\n   * const clerkClient = createClerkClient(...)\n   * await clerkClient.invitations.getInvitationList({ query: '<EMAIL>' })\n   * ```\n   */\n  query?: string;\n}>;\n\nexport class InvitationAPI extends AbstractAPI {\n  public async getInvitationList(params: GetInvitationListParams = {}) {\n    return this.request<PaginatedResourceResponse<Invitation[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async createInvitation(params: CreateParams) {\n    return this.request<Invitation>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revokeInvitation(invitationId: string) {\n    this.requireId(invitationId);\n    return this.request<Invitation>({\n      method: 'POST',\n      path: joinPaths(basePath, invitationId, 'revoke'),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { MachineToken } from '../resources/MachineToken';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/m2m_tokens';\n\nexport class MachineTokensApi extends AbstractAPI {\n  async verifySecret(secret: string) {\n    return this.request<MachineToken>({\n      method: 'POST',\n      path: joinPaths(basePath, 'verify'),\n      bodyParams: { secret },\n    });\n  }\n}\n", "import type { JwksJSON } from '../resources/JSON';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/jwks';\n\nexport class JwksAPI extends AbstractAPI {\n  public async getJwks() {\n    return this.request<JwksJSON>({\n      method: 'GET',\n      path: basePath,\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\nimport { joinPaths } from 'src/util/path';\n\nimport type { DeletedObject, JwtTemplate } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/jwt_templates';\n\ntype Claims = object;\n\ntype CreateJWTTemplateParams = {\n  /**\n   * JWT template name\n   */\n  name: string;\n  /**\n   * JWT template claims in JSON format\n   */\n  claims: Claims;\n  /**\n   * JWT token lifetime\n   */\n  lifetime?: number | null | undefined;\n  /**\n   * JWT token allowed clock skew\n   */\n  allowedClockSkew?: number | null | undefined;\n  /**\n   * Whether a custom signing key/algorithm is also provided for this template\n   */\n  customSigningKey?: boolean | undefined;\n  /**\n   * The custom signing algorithm to use when minting JWTs. Required if `custom_signing_key` is `true`.\n   */\n  signingAlgorithm?: string | null | undefined;\n  /**\n   * The custom signing private key to use when minting JWTs. Required if `custom_signing_key` is `true`.\n   */\n  signingKey?: string | null | undefined;\n};\n\ntype UpdateJWTTemplateParams = CreateJWTTemplateParams & {\n  /**\n   * JWT template ID\n   */\n  templateId: string;\n};\n\nexport class JwtTemplatesApi extends AbstractAPI {\n  public async list(params: ClerkPaginationRequest = {}) {\n    return this.request<JwtTemplate[]>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async get(templateId: string) {\n    this.requireId(templateId);\n\n    return this.request<JwtTemplate>({\n      method: 'GET',\n      path: joinPaths(basePath, templateId),\n    });\n  }\n\n  public async create(params: CreateJWTTemplateParams) {\n    return this.request<JwtTemplate>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async update(params: UpdateJWTTemplateParams) {\n    const { templateId, ...bodyParams } = params;\n\n    this.requireId(templateId);\n    return this.request<JwtTemplate>({\n      method: 'PATCH',\n      path: joinPaths(basePath, templateId),\n      bodyParams,\n    });\n  }\n\n  public async delete(templateId: string) {\n    this.requireId(templateId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, templateId),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest, OrganizationEnrollmentMode } from '@clerk/types';\n\nimport { runtime } from '../../runtime';\nimport { joinPaths } from '../../util/path';\nimport type {\n  Organization,\n  OrganizationDomain,\n  OrganizationInvitation,\n  OrganizationInvitationStatus,\n  OrganizationMembership,\n} from '../resources';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { OrganizationMembershipRole } from '../resources/Enums';\nimport { AbstractAPI } from './AbstractApi';\nimport type { WithSign } from './util-types';\n\nconst basePath = '/organizations';\n\ntype MetadataParams<TPublic = OrganizationPublicMetadata, TPrivate = OrganizationPrivateMetadata> = {\n  publicMetadata?: TPublic;\n  privateMetadata?: TPrivate;\n};\n\ntype GetOrganizationListParams = ClerkPaginationRequest<{\n  includeMembersCount?: boolean;\n  query?: string;\n  orderBy?: WithSign<'name' | 'created_at' | 'members_count'>;\n  organizationId?: string[];\n}>;\n\ntype CreateParams = {\n  name: string;\n  slug?: string;\n  /* The User id for the user creating the organization. The user will become an administrator for the organization. */\n  createdBy?: string;\n  maxAllowedMemberships?: number;\n} & MetadataParams;\n\ntype GetOrganizationParams = ({ organizationId: string } | { slug: string }) & {\n  includeMembersCount?: boolean;\n};\n\ntype UpdateParams = {\n  name?: string;\n  slug?: string;\n  maxAllowedMemberships?: number;\n} & MetadataParams;\n\ntype UpdateLogoParams = {\n  file: Blob | File;\n  uploaderUserId?: string;\n};\n\ntype UpdateMetadataParams = MetadataParams;\n\ntype GetOrganizationMembershipListParams = ClerkPaginationRequest<{\n  organizationId: string;\n\n  /**\n   * Sorts organizations memberships by phone_number, email_address, created_at, first_name, last_name or username.\n   * By prepending one of those values with + or -, we can choose to sort in ascending (ASC) or descending (DESC) order.\n   */\n  orderBy?: WithSign<'phone_number' | 'email_address' | 'created_at' | 'first_name' | 'last_name' | 'username'>;\n\n  /**\n   * Returns users with the user ids specified. For each user id, the `+` and `-` can be\n   * prepended to the id, which denote whether the respective user id should be included or\n   * excluded from the result set. Accepts up to 100 user ids. Any user ids not found are ignored.\n   */\n  userId?: string[];\n\n  /* Returns users with the specified email addresses. Accepts up to 100 email addresses. Any email addresses not found are ignored. */\n  emailAddress?: string[];\n\n  /* Returns users with the specified phone numbers. Accepts up to 100 phone numbers. Any phone numbers not found are ignored. */\n  phoneNumber?: string[];\n\n  /* Returns users with the specified usernames. Accepts up to 100 usernames. Any usernames not found are ignored. */\n  username?: string[];\n\n  /* Returns users with the specified web3 wallet addresses. Accepts up to 100 web3 wallet addresses. Any web3 wallet addressed not found are ignored. */\n  web3Wallet?: string[];\n\n  /* Returns users with the specified roles. Accepts up to 100 roles. Any roles not found are ignored. */\n  role?: OrganizationMembershipRole[];\n\n  /**\n   * Returns users that match the given query.\n   * For possible matches, we check the email addresses, phone numbers, usernames, web3 wallets, user ids, first and last names.\n   * The query value doesn't need to match the exact value you are looking for, it is capable of partial matches as well.\n   */\n  query?: string;\n\n  /**\n   * Returns users with emails that match the given query, via case-insensitive partial match.\n   * For example, `email_address_query=ello` will match a user with the email `<EMAIL>`.\n   */\n  emailAddressQuery?: string;\n\n  /**\n   * Returns users with phone numbers that match the given query, via case-insensitive partial match.\n   * For example, `phone_number_query=555` will match a user with the phone number `+1555xxxxxxx`.\n   */\n  phoneNumberQuery?: string;\n\n  /**\n   * Returns users with usernames that match the given query, via case-insensitive partial match.\n   * For example, `username_query=CoolUser` will match a user with the username `SomeCoolUser`.\n   */\n  usernameQuery?: string;\n\n  /* Returns users with names that match the given query, via case-insensitive partial match. */\n  nameQuery?: string;\n\n  /**\n   * Returns users whose last session activity was before the given date (with millisecond precision).\n   * Example: use 1700690400000 to retrieve users whose last session activity was before 2023-11-23.\n   */\n  lastActiveAtBefore?: number;\n  /**\n   * Returns users whose last session activity was after the given date (with millisecond precision).\n   * Example: use 1700690400000 to retrieve users whose last session activity was after 2023-11-23.\n   */\n  lastActiveAtAfter?: number;\n\n  /**\n   * Returns users who have been created before the given date (with millisecond precision).\n   * Example: use 1730160000000 to retrieve users who have been created before 2024-10-29.\n   */\n  createdAtBefore?: number;\n\n  /**\n   * Returns users who have been created after the given date (with millisecond precision).\n   * Example: use 1730160000000 to retrieve users who have been created after 2024-10-29.\n   */\n  createdAtAfter?: number;\n}>;\n\ntype GetInstanceOrganizationMembershipListParams = ClerkPaginationRequest<{\n  /**\n   * Sorts organizations memberships by phone_number, email_address, created_at, first_name, last_name or username.\n   * By prepending one of those values with + or -, we can choose to sort in ascending (ASC) or descending (DESC) order.\n   */\n  orderBy?: WithSign<'phone_number' | 'email_address' | 'created_at' | 'first_name' | 'last_name' | 'username'>;\n}>;\n\ntype CreateOrganizationMembershipParams = {\n  organizationId: string;\n  userId: string;\n  role: OrganizationMembershipRole;\n};\n\ntype UpdateOrganizationMembershipParams = CreateOrganizationMembershipParams;\n\ntype UpdateOrganizationMembershipMetadataParams = {\n  organizationId: string;\n  userId: string;\n} & MetadataParams<OrganizationMembershipPublicMetadata>;\n\ntype DeleteOrganizationMembershipParams = {\n  organizationId: string;\n  userId: string;\n};\n\ntype CreateOrganizationInvitationParams = {\n  organizationId: string;\n  inviterUserId?: string;\n  emailAddress: string;\n  role: OrganizationMembershipRole;\n  redirectUrl?: string;\n  publicMetadata?: OrganizationInvitationPublicMetadata;\n};\n\ntype CreateBulkOrganizationInvitationParams = Array<{\n  inviterUserId?: string;\n  emailAddress: string;\n  role: OrganizationMembershipRole;\n  redirectUrl?: string;\n  publicMetadata?: OrganizationInvitationPublicMetadata;\n}>;\n\ntype GetOrganizationInvitationListParams = ClerkPaginationRequest<{\n  organizationId: string;\n  status?: OrganizationInvitationStatus[];\n}>;\n\ntype GetOrganizationInvitationParams = {\n  organizationId: string;\n  invitationId: string;\n};\n\ntype RevokeOrganizationInvitationParams = {\n  organizationId: string;\n  invitationId: string;\n  requestingUserId?: string;\n};\n\ntype GetOrganizationDomainListParams = {\n  organizationId: string;\n  limit?: number;\n  offset?: number;\n};\n\ntype CreateOrganizationDomainParams = {\n  organizationId: string;\n  name: string;\n  enrollmentMode: OrganizationEnrollmentMode;\n  verified?: boolean;\n};\n\ntype UpdateOrganizationDomainParams = {\n  organizationId: string;\n  domainId: string;\n} & Partial<CreateOrganizationDomainParams>;\n\ntype DeleteOrganizationDomainParams = {\n  organizationId: string;\n  domainId: string;\n};\n\nexport class OrganizationAPI extends AbstractAPI {\n  public async getOrganizationList(params?: GetOrganizationListParams) {\n    return this.request<PaginatedResourceResponse<Organization[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async createOrganization(params: CreateParams) {\n    return this.request<Organization>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async getOrganization(params: GetOrganizationParams) {\n    const { includeMembersCount } = params;\n    const organizationIdOrSlug = 'organizationId' in params ? params.organizationId : params.slug;\n    this.requireId(organizationIdOrSlug);\n\n    return this.request<Organization>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationIdOrSlug),\n      queryParams: {\n        includeMembersCount,\n      },\n    });\n  }\n\n  public async updateOrganization(organizationId: string, params: UpdateParams) {\n    this.requireId(organizationId);\n    return this.request<Organization>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId),\n      bodyParams: params,\n    });\n  }\n\n  public async updateOrganizationLogo(organizationId: string, params: UpdateLogoParams) {\n    this.requireId(organizationId);\n\n    const formData = new runtime.FormData();\n    formData.append('file', params?.file);\n    if (params?.uploaderUserId) {\n      formData.append('uploader_user_id', params?.uploaderUserId);\n    }\n\n    return this.request<Organization>({\n      method: 'PUT',\n      path: joinPaths(basePath, organizationId, 'logo'),\n      formData,\n    });\n  }\n\n  public async deleteOrganizationLogo(organizationId: string) {\n    this.requireId(organizationId);\n\n    return this.request<Organization>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId, 'logo'),\n    });\n  }\n\n  public async updateOrganizationMetadata(organizationId: string, params: UpdateMetadataParams) {\n    this.requireId(organizationId);\n\n    return this.request<Organization>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'metadata'),\n      bodyParams: params,\n    });\n  }\n\n  public async deleteOrganization(organizationId: string) {\n    return this.request<Organization>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId),\n    });\n  }\n\n  public async getOrganizationMembershipList(params: GetOrganizationMembershipListParams) {\n    const { organizationId, ...queryParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<PaginatedResourceResponse<OrganizationMembership[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'memberships'),\n      queryParams,\n    });\n  }\n\n  public async getInstanceOrganizationMembershipList(params: GetInstanceOrganizationMembershipListParams) {\n    return this.request<PaginatedResourceResponse<OrganizationMembership[]>>({\n      method: 'GET',\n      path: '/organization_memberships',\n      queryParams: params,\n    });\n  }\n\n  public async createOrganizationMembership(params: CreateOrganizationMembershipParams) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'memberships'),\n      bodyParams,\n    });\n  }\n\n  public async updateOrganizationMembership(params: UpdateOrganizationMembershipParams) {\n    const { organizationId, userId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'memberships', userId),\n      bodyParams,\n    });\n  }\n\n  public async updateOrganizationMembershipMetadata(params: UpdateOrganizationMembershipMetadataParams) {\n    const { organizationId, userId, ...bodyParams } = params;\n\n    return this.request<OrganizationMembership>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'memberships', userId, 'metadata'),\n      bodyParams,\n    });\n  }\n\n  public async deleteOrganizationMembership(params: DeleteOrganizationMembershipParams) {\n    const { organizationId, userId } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId, 'memberships', userId),\n    });\n  }\n\n  public async getOrganizationInvitationList(params: GetOrganizationInvitationListParams) {\n    const { organizationId, ...queryParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<PaginatedResourceResponse<OrganizationInvitation[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'invitations'),\n      queryParams,\n    });\n  }\n\n  public async createOrganizationInvitation(params: CreateOrganizationInvitationParams) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'invitations'),\n      bodyParams,\n    });\n  }\n\n  public async createOrganizationInvitationBulk(\n    organizationId: string,\n    params: CreateBulkOrganizationInvitationParams,\n  ) {\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation[]>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'invitations', 'bulk'),\n      bodyParams: params,\n    });\n  }\n\n  public async getOrganizationInvitation(params: GetOrganizationInvitationParams) {\n    const { organizationId, invitationId } = params;\n    this.requireId(organizationId);\n    this.requireId(invitationId);\n\n    return this.request<OrganizationInvitation>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'invitations', invitationId),\n    });\n  }\n\n  public async revokeOrganizationInvitation(params: RevokeOrganizationInvitationParams) {\n    const { organizationId, invitationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'invitations', invitationId, 'revoke'),\n      bodyParams,\n    });\n  }\n\n  public async getOrganizationDomainList(params: GetOrganizationDomainListParams) {\n    const { organizationId, ...queryParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<PaginatedResourceResponse<OrganizationDomain[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'domains'),\n      queryParams,\n    });\n  }\n\n  public async createOrganizationDomain(params: CreateOrganizationDomainParams) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationDomain>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'domains'),\n      bodyParams: {\n        ...bodyParams,\n        verified: bodyParams.verified ?? true,\n      },\n    });\n  }\n\n  public async updateOrganizationDomain(params: UpdateOrganizationDomainParams) {\n    const { organizationId, domainId, ...bodyParams } = params;\n    this.requireId(organizationId);\n    this.requireId(domainId);\n\n    return this.request<OrganizationDomain>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'domains', domainId),\n      bodyParams,\n    });\n  }\n\n  public async deleteOrganizationDomain(params: DeleteOrganizationDomainParams) {\n    const { organizationId, domainId } = params;\n    this.requireId(organizationId);\n    this.requireId(domainId);\n\n    return this.request<OrganizationDomain>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId, 'domains', domainId),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { DeletedObject } from '../resources';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { OAuthApplication } from '../resources/OAuthApplication';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/oauth_applications';\n\ntype CreateOAuthApplicationParams = {\n  /**\n   * The name of the new OAuth application.\n   *\n   * @remarks Max length: 256\n   */\n  name: string;\n  /**\n   * An array of redirect URIs of the new OAuth application\n   */\n  redirectUris?: Array<string> | null | undefined;\n  /**\n   * Define the allowed scopes for the new OAuth applications that dictate the user payload of the OAuth user info endpoint. Available scopes are `profile`, `email`, `public_metadata`, `private_metadata`. Provide the requested scopes as a string, separated by spaces.\n   */\n  scopes?: string | null | undefined;\n  /**\n   * If true, this client is public and you can use the Proof Key of Code Exchange (PKCE) flow.\n   */\n  public?: boolean | null | undefined;\n};\n\ntype UpdateOAuthApplicationParams = CreateOAuthApplicationParams & {\n  /**\n   * The ID of the OAuth application to update\n   */\n  oauthApplicationId: string;\n};\n\nexport class OAuthApplicationsApi extends AbstractAPI {\n  public async list(params: ClerkPaginationRequest = {}) {\n    return this.request<PaginatedResourceResponse<OAuthApplication[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async get(oauthApplicationId: string) {\n    this.requireId(oauthApplicationId);\n\n    return this.request<OAuthApplication>({\n      method: 'GET',\n      path: joinPaths(basePath, oauthApplicationId),\n    });\n  }\n\n  public async create(params: CreateOAuthApplicationParams) {\n    return this.request<OAuthApplication>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async update(params: UpdateOAuthApplicationParams) {\n    const { oauthApplicationId, ...bodyParams } = params;\n\n    this.requireId(oauthApplicationId);\n\n    return this.request<OAuthApplication>({\n      method: 'PATCH',\n      path: joinPaths(basePath, oauthApplicationId),\n      bodyParams,\n    });\n  }\n\n  public async delete(oauthApplicationId: string) {\n    this.requireId(oauthApplicationId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, oauthApplicationId),\n    });\n  }\n\n  public async rotateSecret(oauthApplicationId: string) {\n    this.requireId(oauthApplicationId);\n\n    return this.request<OAuthApplication>({\n      method: 'POST',\n      path: joinPaths(basePath, oauthApplicationId, 'rotate_secret'),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { DeletedObject, PhoneNumber } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/phone_numbers';\n\ntype CreatePhoneNumberParams = {\n  userId: string;\n  phoneNumber: string;\n  verified?: boolean;\n  primary?: boolean;\n  reservedForSecondFactor?: boolean;\n};\n\ntype UpdatePhoneNumberParams = {\n  verified?: boolean;\n  primary?: boolean;\n  reservedForSecondFactor?: boolean;\n};\n\nexport class PhoneNumberAPI extends AbstractAPI {\n  public async getPhoneNumber(phoneNumberId: string) {\n    this.requireId(phoneNumberId);\n\n    return this.request<PhoneNumber>({\n      method: 'GET',\n      path: joinPaths(basePath, phoneNumberId),\n    });\n  }\n\n  public async createPhoneNumber(params: CreatePhoneNumberParams) {\n    return this.request<PhoneNumber>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updatePhoneNumber(phoneNumberId: string, params: UpdatePhoneNumberParams = {}) {\n    this.requireId(phoneNumberId);\n\n    return this.request<PhoneNumber>({\n      method: 'PATCH',\n      path: joinPaths(basePath, phoneNumberId),\n      bodyParams: params,\n    });\n  }\n\n  public async deletePhoneNumber(phoneNumberId: string) {\n    this.requireId(phoneNumberId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, phoneNumberId),\n    });\n  }\n}\n", "import type { ProxyCheck } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/proxy_checks';\n\ntype VerifyParams = {\n  domainId: string;\n  proxyUrl: string;\n};\n\nexport class ProxyCheckAPI extends AbstractAPI {\n  public async verify(params: VerifyParams) {\n    return this.request<ProxyCheck>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { RedirectUrl } from '../resources/RedirectUrl';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/redirect_urls';\n\ntype CreateRedirectUrlParams = {\n  url: string;\n};\n\nexport class RedirectUrlAPI extends AbstractAPI {\n  public async getRedirectUrlList() {\n    return this.request<PaginatedResourceResponse<RedirectUrl[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { paginated: true },\n    });\n  }\n\n  public async getRedirectUrl(redirectUrlId: string) {\n    this.requireId(redirectUrlId);\n    return this.request<RedirectUrl>({\n      method: 'GET',\n      path: joinPaths(basePath, redirectUrlId),\n    });\n  }\n\n  public async createRedirectUrl(params: CreateRedirectUrlParams) {\n    return this.request<RedirectUrl>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async deleteRedirectUrl(redirectUrlId: string) {\n    this.requireId(redirectUrlId);\n    return this.request<RedirectUrl>({\n      method: 'DELETE',\n      path: joinPaths(basePath, redirectUrlId),\n    });\n  }\n}\n", "import type { SamlIdpSlug } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { SamlConnection } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/saml_connections';\n\ntype SamlConnectionListParams = {\n  limit?: number;\n  offset?: number;\n};\ntype CreateSamlConnectionParams = {\n  name: string;\n  provider: SamlIdpSlug;\n  domain: string;\n  organizationId?: string;\n  idpEntityId?: string;\n  idpSsoUrl?: string;\n  idpCertificate?: string;\n  idpMetadataUrl?: string;\n  idpMetadata?: string;\n  attributeMapping?: {\n    emailAddress?: string;\n    firstName?: string;\n    lastName?: string;\n    userId?: string;\n  };\n};\n\ntype UpdateSamlConnectionParams = {\n  name?: string;\n  provider?: SamlIdpSlug;\n  domain?: string;\n  organizationId?: string;\n  idpEntityId?: string;\n  idpSsoUrl?: string;\n  idpCertificate?: string;\n  idpMetadataUrl?: string;\n  idpMetadata?: string;\n  attributeMapping?: {\n    emailAddress?: string;\n    firstName?: string;\n    lastName?: string;\n    userId?: string;\n  };\n  active?: boolean;\n  syncUserAttributes?: boolean;\n  allowSubdomains?: boolean;\n  allowIdpInitiated?: boolean;\n};\n\nexport class SamlConnectionAPI extends AbstractAPI {\n  public async getSamlConnectionList(params: SamlConnectionListParams = {}) {\n    return this.request<SamlConnection[]>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async createSamlConnection(params: CreateSamlConnectionParams) {\n    return this.request<SamlConnection>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async getSamlConnection(samlConnectionId: string) {\n    this.requireId(samlConnectionId);\n    return this.request<SamlConnection>({\n      method: 'GET',\n      path: joinPaths(basePath, samlConnectionId),\n    });\n  }\n\n  public async updateSamlConnection(samlConnectionId: string, params: UpdateSamlConnectionParams = {}) {\n    this.requireId(samlConnectionId);\n\n    return this.request<SamlConnection>({\n      method: 'PATCH',\n      path: joinPaths(basePath, samlConnectionId),\n      bodyParams: params,\n    });\n  }\n  public async deleteSamlConnection(samlConnectionId: string) {\n    this.requireId(samlConnectionId);\n    return this.request<SamlConnection>({\n      method: 'DELETE',\n      path: joinPaths(basePath, samlConnectionId),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest, SessionStatus } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { Cookies } from '../resources/Cookies';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { Session } from '../resources/Session';\nimport type { Token } from '../resources/Token';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/sessions';\n\ntype SessionListParams = ClerkPaginationRequest<{\n  clientId?: string;\n  userId?: string;\n  status?: SessionStatus;\n}>;\n\ntype RefreshTokenParams = {\n  expired_token: string;\n  refresh_token: string;\n  request_origin: string;\n  request_originating_ip?: string;\n  request_headers?: Record<string, string[]>;\n  suffixed_cookies?: boolean;\n  format?: 'token' | 'cookie';\n};\n\ntype CreateSessionParams = {\n  userId: string;\n};\n\nexport class SessionAPI extends AbstractAPI {\n  public async getSessionList(params: SessionListParams = {}) {\n    return this.request<PaginatedResourceResponse<Session[]>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: { ...params, paginated: true },\n    });\n  }\n\n  public async getSession(sessionId: string) {\n    this.requireId(sessionId);\n    return this.request<Session>({\n      method: 'GET',\n      path: joinPaths(basePath, sessionId),\n    });\n  }\n\n  public async createSession(params: CreateSessionParams) {\n    return this.request<Session>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revokeSession(sessionId: string) {\n    this.requireId(sessionId);\n    return this.request<Session>({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'revoke'),\n    });\n  }\n\n  public async verifySession(sessionId: string, token: string) {\n    this.requireId(sessionId);\n    return this.request<Session>({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'verify'),\n      bodyParams: { token },\n    });\n  }\n\n  public async getToken(sessionId: string, template: string) {\n    this.requireId(sessionId);\n    return this.request<Token>({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'tokens', template || ''),\n    });\n  }\n\n  public async refreshSession(sessionId: string, params: RefreshTokenParams & { format: 'token' }): Promise<Token>;\n  public async refreshSession(sessionId: string, params: RefreshTokenParams & { format: 'cookie' }): Promise<Cookies>;\n  public async refreshSession(sessionId: string, params: RefreshTokenParams): Promise<Token>;\n  public async refreshSession(sessionId: string, params: RefreshTokenParams): Promise<Token | Cookies> {\n    this.requireId(sessionId);\n    const { suffixed_cookies, ...restParams } = params;\n    return this.request({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'refresh'),\n      bodyParams: restParams,\n      queryParams: { suffixed_cookies },\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { SignInToken } from '../resources/SignInTokens';\nimport { AbstractAPI } from './AbstractApi';\n\ntype CreateSignInTokensParams = {\n  userId: string;\n  expiresInSeconds: number;\n};\n\nconst basePath = '/sign_in_tokens';\n\nexport class SignInTokenAPI extends AbstractAPI {\n  public async createSignInToken(params: CreateSignInTokensParams) {\n    return this.request<SignInToken>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revokeSignInToken(signInTokenId: string) {\n    this.requireId(signInTokenId);\n    return this.request<SignInToken>({\n      method: 'POST',\n      path: joinPaths(basePath, signInTokenId, 'revoke'),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { SignUpAttempt } from '../resources/SignUpAttempt';\nimport { AbstractAPI } from './AbstractApi';\n\ntype UpdateSignUpParams = {\n  signUpAttemptId: string;\n  externalId?: string | null;\n  customAction?: boolean | null;\n};\n\nconst basePath = '/sign_ups';\n\nexport class SignUpAPI extends AbstractAPI {\n  public async get(signUpAttemptId: string) {\n    this.requireId(signUpAttemptId);\n\n    return this.request<SignUpAttempt>({\n      method: 'GET',\n      path: joinPaths(basePath, signUpAttemptId),\n    });\n  }\n\n  public async update(params: UpdateSignUpParams) {\n    const { signUpAttemptId, ...bodyParams } = params;\n\n    return this.request<SignUpAttempt>({\n      method: 'PATCH',\n      path: joinPaths(basePath, signUpAttemptId),\n      bodyParams,\n    });\n  }\n}\n", "import type { TestingToken } from '../resources/TestingToken';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/testing_tokens';\n\nexport class TestingTokenAPI extends AbstractAPI {\n  public async createTestingToken() {\n    return this.request<TestingToken>({\n      method: 'POST',\n      path: basePath,\n    });\n  }\n}\n", "import type { ClerkPaginationRequest, OAuthProvider, OrganizationInvitationStatus } from '@clerk/types';\n\nimport { runtime } from '../../runtime';\nimport { joinPaths } from '../../util/path';\nimport { deprecated } from '../../util/shared';\nimport type {\n  DeletedObject,\n  OauthAccessToken,\n  OrganizationInvitation,\n  OrganizationMembership,\n  User,\n} from '../resources';\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport { AbstractAPI } from './AbstractApi';\nimport type { WithSign } from './util-types';\n\nconst basePath = '/users';\n\ntype UserCountParams = {\n  emailAddress?: string[];\n  phoneNumber?: string[];\n  username?: string[];\n  web3Wallet?: string[];\n  query?: string;\n  userId?: string[];\n  externalId?: string[];\n};\n\ntype UserListParams = ClerkPaginationRequest<\n  UserCountParams & {\n    orderBy?: WithSign<\n      | 'created_at'\n      | 'updated_at'\n      | 'email_address'\n      | 'web3wallet'\n      | 'first_name'\n      | 'last_name'\n      | 'phone_number'\n      | 'username'\n      | 'last_active_at'\n      | 'last_sign_in_at'\n    >;\n    last_active_at_since?: number;\n    organizationId?: string[];\n  }\n>;\n\ntype UserMetadataParams = {\n  publicMetadata?: UserPublicMetadata;\n  privateMetadata?: UserPrivateMetadata;\n  unsafeMetadata?: UserUnsafeMetadata;\n};\n\ntype PasswordHasher =\n  | 'argon2i'\n  | 'argon2id'\n  | 'awscognito'\n  | 'bcrypt'\n  | 'bcrypt_sha256_django'\n  | 'md5'\n  | 'pbkdf2_sha256'\n  | 'pbkdf2_sha256_django'\n  | 'pbkdf2_sha1'\n  | 'phpass'\n  | 'scrypt_firebase'\n  | 'scrypt_werkzeug'\n  | 'sha256'\n  | 'md5_phpass'\n  | 'ldap_ssha';\n\ntype UserPasswordHashingParams = {\n  passwordDigest: string;\n  passwordHasher: PasswordHasher;\n};\n\ntype CreateUserParams = {\n  externalId?: string;\n  emailAddress?: string[];\n  phoneNumber?: string[];\n  username?: string;\n  password?: string;\n  firstName?: string;\n  lastName?: string;\n  skipPasswordChecks?: boolean;\n  skipPasswordRequirement?: boolean;\n  skipLegalChecks?: boolean;\n  legalAcceptedAt?: Date;\n  totpSecret?: string;\n  backupCodes?: string[];\n  createdAt?: Date;\n} & UserMetadataParams &\n  (UserPasswordHashingParams | object);\n\ntype UpdateUserParams = {\n  /** The first name to assign to the user. */\n  firstName?: string;\n\n  /** The last name of the user. */\n  lastName?: string;\n\n  /** The username to give to the user. It must be unique across your instance. */\n  username?: string;\n\n  /** The plaintext password to give the user. Must be at least 8 characters long, and can not be in any list of hacked passwords. */\n  password?: string;\n\n  /** Set it to true if you're updating the user's password and want to skip any password policy settings check. This parameter can only be used when providing a password. */\n  skipPasswordChecks?: boolean;\n\n  /** Set to true to sign out the user from all their active sessions once their password is updated. This parameter can only be used when providing a password. */\n  signOutOfOtherSessions?: boolean;\n\n  /** The ID of the email address to set as primary. It must be verified, and present on the current user. */\n  primaryEmailAddressID?: string;\n\n  /** If set to true, the user will be notified that their primary email address has changed. By default, no notification is sent. */\n  notifyPrimaryEmailAddressChanged?: boolean;\n\n  /** The ID of the phone number to set as primary. It must be verified, and present on the current user. */\n  primaryPhoneNumberID?: string;\n\n  /** The ID of the web3 wallets to set as primary. It must be verified, and present on the current user. */\n  primaryWeb3WalletID?: string;\n\n  /** The ID of the image to set as the user's profile image */\n  profileImageID?: string;\n\n  /**\n   * In case TOTP is configured on the instance, you can provide the secret to enable it on the specific user without the need to reset it.\n   * Please note that currently the supported options are:\n   * - Period: 30 seconds\n   * - Code length: 6 digits\n   * - Algorithm: SHA1\n   */\n  totpSecret?: string;\n\n  /** If Backup Codes are configured on the instance, you can provide them to enable it on the specific user without the need to reset them. You must provide the backup codes in plain format or the corresponding bcrypt digest. */\n  backupCodes?: string[];\n\n  /** The ID of the user as used in your external systems or your previous authentication solution. Must be unique across your instance. */\n  externalId?: string;\n\n  /** A custom timestamp denoting when the user signed up to the application, specified in RFC3339 format (e.g. 2012-10-20T07:15:20.902Z). */\n  createdAt?: Date;\n\n  /** When set to true all legal checks are skipped. It is not recommended to skip legal checks unless you are migrating a user to Clerk. */\n  skipLegalChecks?: boolean;\n\n  /** A custom timestamp denoting when the user accepted legal requirements, specified in RFC3339 format (e.g. 2012-10-20T07:15:20.902Z). */\n  legalAcceptedAt?: Date;\n\n  /** If true, the user can delete themselves with the Frontend API. */\n  deleteSelfEnabled?: boolean;\n\n  /** If true, the user can create organizations with the Frontend API. */\n  createOrganizationEnabled?: boolean;\n\n  /** The maximum number of organizations the user can create. 0 means unlimited. */\n  createOrganizationsLimit?: number;\n} & UserMetadataParams &\n  (UserPasswordHashingParams | object);\n\ntype GetOrganizationMembershipListParams = ClerkPaginationRequest<{\n  userId: string;\n}>;\n\ntype GetOrganizationInvitationListParams = ClerkPaginationRequest<{\n  userId: string;\n  status?: OrganizationInvitationStatus;\n}>;\n\ntype VerifyPasswordParams = {\n  userId: string;\n  password: string;\n};\n\ntype VerifyTOTPParams = {\n  userId: string;\n  code: string;\n};\n\ntype DeleteUserPasskeyParams = {\n  userId: string;\n  passkeyIdentificationId: string;\n};\n\ntype DeleteWeb3WalletParams = {\n  userId: string;\n  web3WalletIdentificationId: string;\n};\n\ntype DeleteUserExternalAccountParams = {\n  userId: string;\n  externalAccountId: string;\n};\n\ntype UserID = {\n  userId: string;\n};\n\nexport class UserAPI extends AbstractAPI {\n  public async getUserList(params: UserListParams = {}) {\n    const { limit, offset, orderBy, ...userCountParams } = params;\n    // TODO(dimkl): Temporary change to populate totalCount using a 2nd BAPI call to /users/count endpoint\n    // until we update the /users endpoint to be paginated in a next BAPI version.\n    // In some edge cases the data.length != totalCount due to a creation of a user between the 2 api responses\n    const [data, totalCount] = await Promise.all([\n      this.request<User[]>({\n        method: 'GET',\n        path: basePath,\n        queryParams: params,\n      }),\n      this.getCount(userCountParams),\n    ]);\n    return { data, totalCount } as PaginatedResourceResponse<User[]>;\n  }\n\n  public async getUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'GET',\n      path: joinPaths(basePath, userId),\n    });\n  }\n\n  public async createUser(params: CreateUserParams) {\n    return this.request<User>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updateUser(userId: string, params: UpdateUserParams = {}) {\n    this.requireId(userId);\n\n    return this.request<User>({\n      method: 'PATCH',\n      path: joinPaths(basePath, userId),\n      bodyParams: params,\n    });\n  }\n\n  public async updateUserProfileImage(userId: string, params: { file: Blob | File }) {\n    this.requireId(userId);\n\n    const formData = new runtime.FormData();\n    formData.append('file', params?.file);\n\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'profile_image'),\n      formData,\n    });\n  }\n\n  public async updateUserMetadata(userId: string, params: UserMetadataParams) {\n    this.requireId(userId);\n\n    return this.request<User>({\n      method: 'PATCH',\n      path: joinPaths(basePath, userId, 'metadata'),\n      bodyParams: params,\n    });\n  }\n\n  public async deleteUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId),\n    });\n  }\n\n  public async getCount(params: UserCountParams = {}) {\n    return this.request<number>({\n      method: 'GET',\n      path: joinPaths(basePath, 'count'),\n      queryParams: params,\n    });\n  }\n\n  /** @deprecated Use `getUserOauthAccessToken` without the `oauth_` provider prefix . */\n  public async getUserOauthAccessToken(\n    userId: string,\n    provider: `oauth_${OAuthProvider}`,\n  ): Promise<PaginatedResourceResponse<OauthAccessToken[]>>;\n  public async getUserOauthAccessToken(\n    userId: string,\n    provider: OAuthProvider,\n  ): Promise<PaginatedResourceResponse<OauthAccessToken[]>>;\n  public async getUserOauthAccessToken(userId: string, provider: `oauth_${OAuthProvider}` | OAuthProvider) {\n    this.requireId(userId);\n    const hasPrefix = provider.startsWith('oauth_');\n    const _provider = hasPrefix ? provider : `oauth_${provider}`;\n\n    if (hasPrefix) {\n      deprecated(\n        'getUserOauthAccessToken(userId, provider)',\n        'Remove the `oauth_` prefix from the `provider` argument.',\n      );\n    }\n\n    return this.request<PaginatedResourceResponse<OauthAccessToken[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, userId, 'oauth_access_tokens', _provider),\n      queryParams: { paginated: true },\n    });\n  }\n\n  public async disableUserMFA(userId: string) {\n    this.requireId(userId);\n    return this.request<UserID>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId, 'mfa'),\n    });\n  }\n\n  public async getOrganizationMembershipList(params: GetOrganizationMembershipListParams) {\n    const { userId, limit, offset } = params;\n    this.requireId(userId);\n\n    return this.request<PaginatedResourceResponse<OrganizationMembership[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, userId, 'organization_memberships'),\n      queryParams: { limit, offset },\n    });\n  }\n\n  public async getOrganizationInvitationList(params: GetOrganizationInvitationListParams) {\n    const { userId, ...queryParams } = params;\n    this.requireId(userId);\n\n    return this.request<PaginatedResourceResponse<OrganizationInvitation[]>>({\n      method: 'GET',\n      path: joinPaths(basePath, userId, 'organization_invitations'),\n      queryParams,\n    });\n  }\n\n  public async verifyPassword(params: VerifyPasswordParams) {\n    const { userId, password } = params;\n    this.requireId(userId);\n\n    return this.request<{ verified: true }>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'verify_password'),\n      bodyParams: { password },\n    });\n  }\n\n  public async verifyTOTP(params: VerifyTOTPParams) {\n    const { userId, code } = params;\n    this.requireId(userId);\n\n    return this.request<{ verified: true; code_type: 'totp' }>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'verify_totp'),\n      bodyParams: { code },\n    });\n  }\n\n  public async banUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'ban'),\n    });\n  }\n\n  public async unbanUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'unban'),\n    });\n  }\n\n  public async lockUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'lock'),\n    });\n  }\n\n  public async unlockUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'unlock'),\n    });\n  }\n\n  public async deleteUserProfileImage(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId, 'profile_image'),\n    });\n  }\n\n  public async deleteUserPasskey(params: DeleteUserPasskeyParams) {\n    this.requireId(params.userId);\n    this.requireId(params.passkeyIdentificationId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, params.userId, 'passkeys', params.passkeyIdentificationId),\n    });\n  }\n\n  public async deleteUserWeb3Wallet(params: DeleteWeb3WalletParams) {\n    this.requireId(params.userId);\n    this.requireId(params.web3WalletIdentificationId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, params.userId, 'web3_wallets', params.web3WalletIdentificationId),\n    });\n  }\n\n  public async deleteUserExternalAccount(params: DeleteUserExternalAccountParams) {\n    this.requireId(params.userId);\n    this.requireId(params.externalAccountId);\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, params.userId, 'external_accounts', params.externalAccountId),\n    });\n  }\n\n  public async deleteUserBackupCodes(userId: string) {\n    this.requireId(userId);\n    return this.request<UserID>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId, 'backup_code'),\n    });\n  }\n\n  public async deleteUserTOTP(userId: string) {\n    this.requireId(userId);\n    return this.request<UserID>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId, 'totp'),\n    });\n  }\n}\n", "import type { ClerkPaginationRequest } from '@clerk/types';\n\nimport type { PaginatedResourceResponse } from '../resources/Deserializer';\nimport type { WaitlistEntryStatus } from '../resources/Enums';\nimport type { WaitlistEntry } from '../resources/WaitlistEntry';\nimport { AbstractAPI } from './AbstractApi';\nimport type { WithSign } from './util-types';\n\nconst basePath = '/waitlist_entries';\n\ntype WaitlistEntryListParams = ClerkPaginationRequest<{\n  /**\n   * Filter waitlist entries by `email_address` or `id`\n   */\n  query?: string;\n  status?: WaitlistEntryStatus;\n  orderBy?: WithSign<'created_at' | 'invited_at' | 'email_address'>;\n}>;\n\ntype WaitlistEntryCreateParams = {\n  emailAddress: string;\n  notify?: boolean;\n};\n\nexport class WaitlistEntryAPI extends AbstractAPI {\n  public async list(params: WaitlistEntryListParams = {}) {\n    return this.request<PaginatedResourceResponse<WaitlistEntry>>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async create(params: WaitlistEntryCreateParams) {\n    return this.request<WaitlistEntry>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { WebhooksSvixJSON } from '../resources/JSON';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/webhooks';\n\nexport class WebhookAPI extends Abstract<PERSON>I {\n  public async createSvixApp() {\n    return this.request<WebhooksSvixJSON>({\n      method: 'POST',\n      path: joinPaths(basePath, 'svix'),\n    });\n  }\n\n  public async generateSvixAuthURL() {\n    return this.request<WebhooksSvixJSON>({\n      method: 'POST',\n      path: joinPaths(basePath, 'svix_url'),\n    });\n  }\n\n  public async deleteSvixApp() {\n    return this.request<void>({\n      method: 'DELETE',\n      path: joinPaths(basePath, 'svix'),\n    });\n  }\n}\n", "import { ClerkAPIResponseError, parseError } from '@clerk/shared/error';\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '@clerk/types';\nimport snakecaseKeys from 'snakecase-keys';\n\nimport { API_URL, API_VERSION, constants, SUPPORTED_BAPI_VERSION, USER_AGENT } from '../constants';\nimport { runtime } from '../runtime';\nimport { assertValidSecretKey } from '../util/optionsAssertions';\nimport { joinPaths } from '../util/path';\nimport { deserialize } from './resources/Deserializer';\n\nexport type ClerkBackendApiRequestOptions = {\n  method: 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'PUT';\n  queryParams?: Record<string, unknown>;\n  headerParams?: Record<string, string>;\n  bodyParams?: Record<string, unknown> | Array<Record<string, unknown>>;\n  formData?: FormData;\n} & (\n  | {\n      url: string;\n      path?: string;\n    }\n  | {\n      url?: string;\n      path: string;\n    }\n);\n\nexport type ClerkBackendApiResponse<T> =\n  | {\n      data: T;\n      errors: null;\n      totalCount?: number;\n    }\n  | {\n      data: null;\n      errors: ClerkAPIError[];\n      totalCount?: never;\n      clerkTraceId?: string;\n      status?: number;\n      statusText?: string;\n      retryAfter?: number;\n    };\n\nexport type RequestFunction = ReturnType<typeof buildRequest>;\n\ntype BuildRequestOptions = {\n  /* Secret Key */\n  secretKey?: string;\n  /* Backend API URL */\n  apiUrl?: string;\n  /* Backend API version */\n  apiVersion?: string;\n  /* Library/SDK name */\n  userAgent?: string;\n  /**\n   * Allow requests without specifying a secret key. In most cases this should be set to `false`.\n   * @default true\n   */\n  requireSecretKey?: boolean;\n};\n\nexport function buildRequest(options: BuildRequestOptions) {\n  const requestFn = async <T>(requestOptions: ClerkBackendApiRequestOptions): Promise<ClerkBackendApiResponse<T>> => {\n    const {\n      secretKey,\n      requireSecretKey = true,\n      apiUrl = API_URL,\n      apiVersion = API_VERSION,\n      userAgent = USER_AGENT,\n    } = options;\n    const { path, method, queryParams, headerParams, bodyParams, formData } = requestOptions;\n\n    if (requireSecretKey) {\n      assertValidSecretKey(secretKey);\n    }\n\n    const url = joinPaths(apiUrl, apiVersion, path);\n\n    // Build final URL with search parameters\n    const finalUrl = new URL(url);\n\n    if (queryParams) {\n      // Snakecase query parameters\n      const snakecasedQueryParams = snakecaseKeys({ ...queryParams });\n\n      // Support array values for queryParams such as { foo: [42, 43] }\n      for (const [key, val] of Object.entries(snakecasedQueryParams)) {\n        if (val) {\n          [val].flat().forEach(v => finalUrl.searchParams.append(key, v as string));\n        }\n      }\n    }\n\n    // Build headers\n    const headers: Record<string, any> = {\n      'Clerk-API-Version': SUPPORTED_BAPI_VERSION,\n      'User-Agent': userAgent,\n      ...headerParams,\n    };\n\n    if (secretKey) {\n      headers.Authorization = `Bearer ${secretKey}`;\n    }\n\n    let res: Response | undefined;\n    try {\n      if (formData) {\n        res = await runtime.fetch(finalUrl.href, {\n          method,\n          headers,\n          body: formData,\n        });\n      } else {\n        // Enforce application/json for all non form-data requests\n        headers['Content-Type'] = 'application/json';\n\n        const buildBody = () => {\n          const hasBody = method !== 'GET' && bodyParams && Object.keys(bodyParams).length > 0;\n          if (!hasBody) {\n            return null;\n          }\n\n          const formatKeys = (object: Parameters<typeof snakecaseKeys>[0]) => snakecaseKeys(object, { deep: false });\n\n          return {\n            body: JSON.stringify(Array.isArray(bodyParams) ? bodyParams.map(formatKeys) : formatKeys(bodyParams)),\n          };\n        };\n\n        res = await runtime.fetch(finalUrl.href, {\n          method,\n          headers,\n          ...buildBody(),\n        });\n      }\n\n      // TODO: Parse JSON or Text response based on a response header\n      const isJSONResponse =\n        res?.headers && res.headers?.get(constants.Headers.ContentType) === constants.ContentTypes.Json;\n      const responseBody = await (isJSONResponse ? res.json() : res.text());\n\n      if (!res.ok) {\n        return {\n          data: null,\n          errors: parseErrors(responseBody),\n          status: res?.status,\n          statusText: res?.statusText,\n          clerkTraceId: getTraceId(responseBody, res?.headers),\n          retryAfter: getRetryAfter(res?.headers),\n        };\n      }\n\n      return {\n        ...deserialize<T>(responseBody),\n        errors: null,\n      };\n    } catch (err) {\n      if (err instanceof Error) {\n        return {\n          data: null,\n          errors: [\n            {\n              code: 'unexpected_error',\n              message: err.message || 'Unexpected error',\n            },\n          ],\n          clerkTraceId: getTraceId(err, res?.headers),\n        };\n      }\n\n      return {\n        data: null,\n        errors: parseErrors(err),\n        status: res?.status,\n        statusText: res?.statusText,\n        clerkTraceId: getTraceId(err, res?.headers),\n        retryAfter: getRetryAfter(res?.headers),\n      };\n    }\n  };\n\n  return withLegacyRequestReturn(requestFn);\n}\n\n// Returns either clerk_trace_id if present in response json, otherwise defaults to CF-Ray header\n// If the request failed before receiving a response, returns undefined\nfunction getTraceId(data: unknown, headers?: Headers): string {\n  if (data && typeof data === 'object' && 'clerk_trace_id' in data && typeof data.clerk_trace_id === 'string') {\n    return data.clerk_trace_id;\n  }\n\n  const cfRay = headers?.get('cf-ray');\n  return cfRay || '';\n}\n\nfunction getRetryAfter(headers?: Headers): number | undefined {\n  const retryAfter = headers?.get('Retry-After');\n  if (!retryAfter) return;\n\n  const value = parseInt(retryAfter, 10);\n  if (isNaN(value)) return;\n\n  return value;\n}\n\nfunction parseErrors(data: unknown): ClerkAPIError[] {\n  if (!!data && typeof data === 'object' && 'errors' in data) {\n    const errors = data.errors as ClerkAPIErrorJSON[];\n    return errors.length > 0 ? errors.map(parseError) : [];\n  }\n  return [];\n}\n\ntype LegacyRequestFunction = <T>(requestOptions: ClerkBackendApiRequestOptions) => Promise<T>;\n\n// TODO(dimkl): Will be probably be dropped in next major version\nfunction withLegacyRequestReturn(cb: any): LegacyRequestFunction {\n  return async (...args) => {\n    const { data, errors, totalCount, status, statusText, clerkTraceId, retryAfter } = await cb(...args);\n    if (errors) {\n      // instead of passing `data: errors`, we have set the `error.errors` because\n      // the errors returned from callback is already parsed and passing them as `data`\n      // will not be able to assign them to the instance\n      const error = new ClerkAPIResponseError(statusText || '', {\n        data: [],\n        status,\n        clerkTraceId,\n        retryAfter,\n      });\n      error.errors = errors;\n      throw error;\n    }\n\n    if (typeof totalCount !== 'undefined') {\n      return { data, totalCount };\n    }\n\n    return data;\n  };\n}\n", "import type { AccountlessApplicationJSON } from './JSON';\n\nexport class AccountlessApplication {\n  constructor(\n    readonly publishableKey: string,\n    readonly secretKey: string,\n    readonly claimUrl: string,\n    readonly apiKeysUrl: string,\n  ) {}\n\n  static fromJSON(data: AccountlessApplicationJSON): AccountlessApplication {\n    return new AccountlessApplication(data.publishable_key, data.secret_key, data.claim_url, data.api_keys_url);\n  }\n}\n", "import type { ActorTokenStatus } from './Enums';\nimport type { ActorTokenJSON } from './JSON';\n\nexport class ActorToken {\n  constructor(\n    readonly id: string,\n    readonly status: ActorTokenStatus,\n    readonly userId: string,\n    readonly actor: Record<string, unknown> | null,\n    readonly token: string | null | undefined,\n    readonly url: string | null | undefined,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: ActorTokenJSON): ActorToken {\n    return new ActorToken(\n      data.id,\n      data.status,\n      data.user_id,\n      data.actor,\n      data.token,\n      data.url,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { AllowlistIdentifierType } from './Enums';\nimport type { AllowlistIdentifierJSON } from './JSON';\n\n/**\n * The Backend `AllowlistIdentifier` object represents an identifier that has been added to the allowlist of your application. The Backend `AllowlistIdentifier` object is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Allow-list-Block-list#operation/ListAllowlistIdentifiers) and is not directly accessible from the Frontend API.\n */\nexport class AllowlistIdentifier {\n  constructor(\n    /**\n     * A unique ID for the allowlist identifier.\n     */\n    readonly id: string,\n    /**\n     * The [identifier](https://clerk.com/docs/authentication/configuration/sign-up-sign-in-options#identifiers) that was added to the allowlist.\n     */\n    readonly identifier: string,\n    /**\n     * The type of the allowlist identifier.\n     */\n    readonly identifierType: AllowlistIdentifierType,\n    /**\n     * The date when the allowlist identifier was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the allowlist identifier was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The ID of the instance that this allowlist identifier belongs to.\n     */\n    readonly instanceId?: string,\n    /**\n     * The ID of the invitation sent to the identifier.\n     */\n    readonly invitationId?: string,\n  ) {}\n\n  static fromJSON(data: AllowlistIdentifierJSON): AllowlistIdentifier {\n    return new AllowlistIdentifier(\n      data.id,\n      data.identifier,\n      data.identifier_type,\n      data.created_at,\n      data.updated_at,\n      data.instance_id,\n      data.invitation_id,\n    );\n  }\n}\n", "import type { <PERSON><PERSON>eyJSON } from './JSON';\n\nexport class <PERSON><PERSON>ey {\n  constructor(\n    readonly id: string,\n    readonly type: string,\n    readonly name: string,\n    readonly subject: string,\n    readonly scopes: string[],\n    readonly claims: Record<string, any> | null,\n    readonly revoked: boolean,\n    readonly revocationReason: string | null,\n    readonly expired: boolean,\n    readonly expiration: number | null,\n    readonly createdBy: string | null,\n    readonly description: string | null,\n    readonly lastUsedAt: number | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: APIKeyJSON) {\n    return new APIKey(\n      data.id,\n      data.type,\n      data.name,\n      data.subject,\n      data.scopes,\n      data.claims,\n      data.revoked,\n      data.revocation_reason,\n      data.expired,\n      data.expiration,\n      data.created_by,\n      data.description,\n      data.last_used_at,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { BlocklistIdentifierType } from './Enums';\nimport type { BlocklistIdentifierJSON } from './JSON';\n\nexport class BlocklistIdentifier {\n  constructor(\n    readonly id: string,\n    readonly identifier: string,\n    readonly identifierType: BlocklistIdentifierType,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly instanceId?: string,\n  ) {}\n\n  static fromJSON(data: BlocklistIdentifierJSON): BlocklistIdentifier {\n    return new BlocklistIdentifier(\n      data.id,\n      data.identifier,\n      data.identifier_type,\n      data.created_at,\n      data.updated_at,\n      data.instance_id,\n    );\n  }\n}\n", "import type { SessionActivityJSO<PERSON>, SessionJSON } from './JSON';\n\n/**\n * The Backend `SessionActivity` object models the activity of a user session, capturing details such as the device type, browser information, and geographical location.\n */\nexport class SessionActivity {\n  constructor(\n    /**\n     * The unique identifier for the session activity record.\n     */\n    readonly id: string,\n    /**\n     * Will be set to `true` if the session activity came from a mobile device. Set to `false` otherwise.\n     */\n    readonly isMobile: boolean,\n    /**\n     * The IP address from which this session activity originated.\n     */\n    readonly ipAddress?: string,\n    /**\n     * The city from which this session activity occurred. Resolved by IP address geo-location.\n     */\n    readonly city?: string,\n    /**\n     * The country from which this session activity occurred. Resolved by IP address geo-location.\n     */\n    readonly country?: string,\n    /**\n     * The version of the browser from which this session activity occurred.\n     */\n    readonly browserVersion?: string,\n    /**\n     * The name of the browser from which this session activity occurred.\n     */\n    readonly browserName?: string,\n    /**\n     * The type of the device which was used in this session activity.\n     */\n    readonly deviceType?: string,\n  ) {}\n\n  static fromJSON(data: SessionActivityJSON): SessionActivity {\n    return new SessionActivity(\n      data.id,\n      data.is_mobile,\n      data.ip_address,\n      data.city,\n      data.country,\n      data.browser_version,\n      data.browser_name,\n      data.device_type,\n    );\n  }\n}\n\n/**\n * The Backend `Session` object is similar to the [`Session`](https://clerk.com/docs/references/javascript/session) object as it is an abstraction over an HTTP session and models the period of information exchange between a user and the server. However, the Backend `Session` object is different as it is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Sessions#operation/GetSessionList) and is not directly accessible from the Frontend API.\n */\nexport class Session {\n  constructor(\n    /**\n     * The unique identifier for the `Session`.\n     */\n    readonly id: string,\n    /**\n     * The ID of the client associated with the `Session`.\n     */\n    readonly clientId: string,\n    /**\n     * The ID of the user associated with the `Session`.\n     */\n    readonly userId: string,\n    /**\n     * The current state of the `Session`.\n     */\n    readonly status: string,\n    /**\n     * The time the session was last active on the [`Client`](https://clerk.com/docs/references/backend/types/backend-client).\n     */\n    readonly lastActiveAt: number,\n    /**\n     * The date when the `Session` will expire.\n     */\n    readonly expireAt: number,\n    /**\n     * The date when the `Session` will be abandoned.\n     */\n    readonly abandonAt: number,\n    /**\n     * The date when the `Session` was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the `Session` was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The ID of the last active organization.\n     */\n    readonly lastActiveOrganizationId?: string,\n    /**\n     * An object that provides additional information about this session, focused around user activity data.\n     */\n    readonly latestActivity?: SessionActivity,\n    /**\n     * The JWT actor for the session. Holds identifier for the user that is impersonating the current user. Read more about [impersonation](https://clerk.com/docs/users/user-impersonation).\n     */\n    readonly actor: Record<string, unknown> | null = null,\n  ) {}\n\n  static fromJSON(data: SessionJSON): Session {\n    return new Session(\n      data.id,\n      data.client_id,\n      data.user_id,\n      data.status,\n      data.last_active_at,\n      data.expire_at,\n      data.abandon_at,\n      data.created_at,\n      data.updated_at,\n      data.last_active_organization_id,\n      data.latest_activity && SessionActivity.fromJSON(data.latest_activity),\n      data.actor,\n    );\n  }\n}\n", "import type { <PERSON><PERSON><PERSON>SON } from './JSON';\nimport { Session } from './Session';\n\n/**\n * The Backend `Client` object is similar to the [`Client`](https://clerk.com/docs/references/javascript/client) object as it holds information about the authenticated sessions in the current device. However, the Backend `Client` object is different from the `Client` object in that it is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Clients#operation/GetClient) and is not directly accessible from the Frontend API.\n */\nexport class Client {\n  constructor(\n    /**\n     * The unique identifier for the `Client`.\n     */\n    readonly id: string,\n    /**\n     * An array of [Session](https://clerk.com/docs/references/backend/types/backend-session){{ target: '_blank' }} IDs associated with the `Client`.\n     */\n    readonly sessionIds: string[],\n    /**\n     * An array of [Session](https://clerk.com/docs/references/backend/types/backend-session){{ target: '_blank' }} objects associated with the `Client`.\n     */\n    readonly sessions: Session[],\n    /**\n     * The ID of the [`SignIn`](https://clerk.com/docs/references/javascript/sign-in){{ target: '_blank' }}.\n     */\n    readonly signInId: string | null,\n    /**\n     * The ID of the [`SignUp`](https://clerk.com/docs/references/javascript/sign-up){{ target: '_blank' }}.\n     */\n    readonly signUpId: string | null,\n    /**\n     * The ID of the last active [Session](https://clerk.com/docs/references/backend/types/backend-session).\n     */\n    readonly lastActiveSessionId: string | null,\n    /**\n     * The date when the `Client` was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the `Client` was last updated.\n     */\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: ClientJSON): Client {\n    return new Client(\n      data.id,\n      data.session_ids,\n      data.sessions.map(x => Session.fromJSON(x)),\n      data.sign_in_id,\n      data.sign_up_id,\n      data.last_active_session_id,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { CnameTargetJSON } from './JSON';\n\nexport class CnameTarget {\n  constructor(\n    readonly host: string,\n    readonly value: string,\n    readonly required: boolean,\n  ) {}\n\n  static fromJSON(data: CnameTargetJSON): CnameTarget {\n    return new CnameTarget(data.host, data.value, data.required);\n  }\n}\n", "import type { CookiesJSON } from './JSON';\n\nexport class Cookies {\n  constructor(readonly cookies: string[]) {}\n\n  static fromJSON(data: CookiesJSON): Cookies {\n    return new Cookies(data.cookies);\n  }\n}\n", "import type { DeletedObjectJSON } from './JSON';\n\nexport class DeletedObject {\n  constructor(\n    readonly object: string,\n    readonly id: string | null,\n    readonly slug: string | null,\n    readonly deleted: boolean,\n  ) {}\n\n  static fromJSON(data: DeletedObjectJSON) {\n    return new DeletedObject(data.object, data.id || null, data.slug || null, data.deleted);\n  }\n}\n", "import { CnameTarget } from './CnameTarget';\nimport type { DomainJSON } from './JSON';\n\nexport class Domain {\n  constructor(\n    readonly id: string,\n    readonly name: string,\n    readonly isSatellite: boolean,\n    readonly frontendApiUrl: string,\n    readonly developmentOrigin: string,\n    readonly cnameTargets: CnameTarget[],\n    readonly accountsPortalUrl?: string | null,\n    readonly proxyUrl?: string | null,\n  ) {}\n\n  static fromJSON(data: DomainJSON): Domain {\n    return new Domain(\n      data.id,\n      data.name,\n      data.is_satellite,\n      data.frontend_api_url,\n      data.development_origin,\n      data.cname_targets && data.cname_targets.map(x => CnameTarget.fromJSON(x)),\n      data.accounts_portal_url,\n      data.proxy_url,\n    );\n  }\n}\n", "import type { EmailJSON } from './JSON';\n\nexport class Email {\n  constructor(\n    readonly id: string,\n    readonly fromEmailName: string,\n    readonly emailAddressId: string | null,\n    readonly toEmailAddress?: string,\n    readonly subject?: string,\n    readonly body?: string,\n    readonly bodyPlain?: string | null,\n    readonly status?: string,\n    readonly slug?: string | null,\n    readonly data?: Record<string, any> | null,\n    readonly deliveredByClerk?: boolean,\n  ) {}\n\n  static fromJSON(data: EmailJSON): Email {\n    return new Email(\n      data.id,\n      data.from_email_name,\n      data.email_address_id,\n      data.to_email_address,\n      data.subject,\n      data.body,\n      data.body_plain,\n      data.status,\n      data.slug,\n      data.data,\n      data.delivered_by_clerk,\n    );\n  }\n}\n", "import type { IdentificationLinkJSON } from './JSON';\n\n/**\n * Contains information about any identifications that might be linked to the email address.\n */\nexport class IdentificationLink {\n  constructor(\n    /**\n     * The unique identifier for the identification link.\n     */\n    readonly id: string,\n    /**\n     * The type of the identification link, e.g., `\"email_address\"`, `\"phone_number\"`, etc.\n     */\n    readonly type: string,\n  ) {}\n\n  static fromJSON(data: IdentificationLinkJSON): IdentificationLink {\n    return new IdentificationLink(data.id, data.type);\n  }\n}\n", "import type { VerificationStatus } from '@clerk/types';\n\nimport type { OrganizationDomainVerificationJSON, VerificationJSON } from './JSON';\n\n/**\n * The Backend `Verification` object describes the state of the verification process of a sign-in or sign-up attempt.\n */\nexport class Verification {\n  constructor(\n    /**\n     * The state of the verification.\n     *\n     * <ul>\n     *  <li>`unverified`: The verification has not been verified yet.</li>\n     *  <li>`verified`: The verification has been verified.</li>\n     *  <li>`transferable`: The verification is transferable to between sign-in and sign-up flows.</li>\n     *  <li>`failed`: The verification has failed.</li>\n     *  <li>`expired`: The verification has expired.</li>\n     * </ul>\n     */\n    readonly status: VerificationStatus,\n    /**\n     * The strategy pertaining to the parent sign-up or sign-in attempt.\n     */\n    readonly strategy: string,\n    /**\n     * The redirect URL for an external verification.\n     */\n    readonly externalVerificationRedirectURL: URL | null = null,\n    /**\n     * The number of attempts related to the verification.\n     */\n    readonly attempts: number | null = null,\n    /**\n     * The time the verification will expire at.\n     */\n    readonly expireAt: number | null = null,\n    /**\n     * The [nonce](https://en.wikipedia.org/wiki/Cryptographic_nonce) pertaining to the verification.\n     */\n    readonly nonce: string | null = null,\n    /**\n     * The message that will be presented to the user's Web3 wallet for signing during authentication. This follows the [Sign-In with Ethereum (SIWE) protocol format](https://docs.login.xyz/general-information/siwe-overview/eip-4361#example-message-to-be-signed), which typically includes details like the requesting service, wallet address, terms acceptance, nonce, timestamp, and any additional resources.\n     */\n    readonly message: string | null = null,\n  ) {}\n\n  static fromJSON(data: VerificationJSON): Verification {\n    return new Verification(\n      data.status,\n      data.strategy,\n      data.external_verification_redirect_url ? new URL(data.external_verification_redirect_url) : null,\n      data.attempts,\n      data.expire_at,\n      data.nonce,\n    );\n  }\n}\n\nexport class OrganizationDomainVerification {\n  constructor(\n    readonly status: string,\n    readonly strategy: string,\n    readonly attempts: number | null = null,\n    readonly expireAt: number | null = null,\n  ) {}\n\n  static fromJSON(data: OrganizationDomainVerificationJSON): OrganizationDomainVerification {\n    return new OrganizationDomainVerification(data.status, data.strategy, data.attempts, data.expires_at);\n  }\n}\n", "import { IdentificationLink } from './IdentificationLink';\nimport type { EmailAddressJSON } from './JSON';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `EmailAddress` object is a model around an email address. Email addresses are one of the [identifiers](https://clerk.com/docs/authentication/configuration/sign-up-sign-in-options#identifiers) used to provide identification for users.\n *\n * Email addresses must be **verified** to ensure that they are assigned to their rightful owners. The `EmailAddress` object holds all necessary state around the verification process.\n *\n * For implementation examples for adding and verifying email addresses, see the [email link custom flow](https://clerk.com/docs/custom-flows/email-links) and [email code custom flow](https://clerk.com/docs/custom-flows/add-email) guides.\n */\nexport class EmailAddress {\n  constructor(\n    /**\n     * The unique identifier for the email address.\n     */\n    readonly id: string,\n    /**\n     * The value of the email address.\n     */\n    readonly emailAddress: string,\n    /**\n     * An object holding information on the verification of the email address.\n     */\n    readonly verification: Verification | null,\n    /**\n     * An array of objects containing information about any identifications that might be linked to the email address.\n     */\n    readonly linkedTo: IdentificationLink[],\n  ) {}\n\n  static fromJSON(data: EmailAddressJSON): EmailAddress {\n    return new EmailAddress(\n      data.id,\n      data.email_address,\n      data.verification && Verification.fromJSON(data.verification),\n      data.linked_to.map(link => IdentificationLink.fromJSON(link)),\n    );\n  }\n}\n", "import type { ExternalAccountJSON } from './JSON';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `ExternalAccount` object is a model around an identification obtained by an external provider (e.g. a social provider such as Google).\n *\n * External account must be verified, so that you can make sure they can be assigned to their rightful owners. The `ExternalAccount` object holds all necessary state around the verification process.\n */\nexport class ExternalAccount {\n  constructor(\n    /**\n     * The unique identifier for this external account.\n     */\n    readonly id: string,\n    /**\n     * The provider name (e.g., `google`).\n     */\n    readonly provider: string,\n    /**\n     * The identification with which this external account is associated.\n     */\n    readonly identificationId: string,\n    /**\n     * The unique ID of the user in the provider.\n     */\n    readonly externalId: string,\n    /**\n     * The scopes that the user has granted access to.\n     */\n    readonly approvedScopes: string,\n    /**\n     * The user's email address.\n     */\n    readonly emailAddress: string,\n    /**\n     * The user's first name.\n     */\n    readonly firstName: string,\n    /**\n     * The user's last name.\n     */\n    readonly lastName: string,\n    /**\n     * The user's image URL.\n     */\n    readonly imageUrl: string,\n    /**\n     * The user's username.\n     */\n    readonly username: string | null,\n    /**\n     * The phone number related to this specific external account.\n     */\n    readonly phoneNumber: string | null,\n    /**\n     * Metadata that can be read from the Frontend API and Backend API and can be set only from the Backend API.\n     */\n    readonly publicMetadata: Record<string, unknown> | null = {},\n    /**\n     * A descriptive label to differentiate multiple external accounts of the same user for the same provider.\n     */\n    readonly label: string | null,\n    /**\n     * An object holding information on the verification of this external account.\n     */\n    readonly verification: Verification | null,\n  ) {}\n\n  static fromJSON(data: ExternalAccountJSON): ExternalAccount {\n    return new ExternalAccount(\n      data.id,\n      data.provider,\n      data.identification_id,\n      data.provider_user_id,\n      data.approved_scopes,\n      data.email_address,\n      data.first_name,\n      data.last_name,\n      data.image_url || '',\n      data.username,\n      data.phone_number,\n      data.public_metadata,\n      data.label,\n      data.verification && Verification.fromJSON(data.verification),\n    );\n  }\n}\n", "import type { IdPOAuthAccessTokenJSON } from './JSON';\n\nexport class IdPOAuthAccessToken {\n  constructor(\n    readonly id: string,\n    readonly clientId: string,\n    readonly type: string,\n    readonly subject: string,\n    readonly scopes: string[],\n    readonly revoked: boolean,\n    readonly revocationReason: string | null,\n    readonly expired: boolean,\n    readonly expiration: number | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: IdPOAuthAccessTokenJSON) {\n    return new IdPOAuthAccessToken(\n      data.id,\n      data.client_id,\n      data.type,\n      data.subject,\n      data.scopes,\n      data.revoked,\n      data.revocation_reason,\n      data.expired,\n      data.expiration,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { InstanceJSON } from './JSON';\n\nexport class Instance {\n  constructor(\n    readonly id: string,\n    readonly environmentType: string,\n    readonly allowedOrigins: Array<string> | null,\n  ) {}\n\n  static fromJSON(data: InstanceJSON): Instance {\n    return new Instance(data.id, data.environment_type, data.allowed_origins);\n  }\n}\n", "import type { InstanceRestrictionsJSON } from './JSON';\n\nexport class InstanceRestrictions {\n  constructor(\n    readonly allowlist: boolean,\n    readonly blocklist: boolean,\n    readonly blockEmailSubaddresses: boolean,\n    readonly blockDisposableEmailDomains: boolean,\n    readonly ignoreDotsForGmailAddresses: boolean,\n  ) {}\n\n  static fromJSON(data: InstanceRestrictionsJSON): InstanceRestrictions {\n    return new InstanceRestrictions(\n      data.allowlist,\n      data.blocklist,\n      data.block_email_subaddresses,\n      data.block_disposable_email_domains,\n      data.ignore_dots_for_gmail_addresses,\n    );\n  }\n}\n", "import type { InstanceSettingsJSON } from './JSON';\n\nexport class InstanceSettings {\n  constructor(\n    readonly id?: string | undefined,\n    readonly restrictedToAllowlist?: boolean | undefined,\n    readonly fromEmailAddress?: string | undefined,\n    readonly progressiveSignUp?: boolean | undefined,\n    readonly enhancedEmailDeliverability?: boolean | undefined,\n  ) {}\n\n  static fromJSON(data: InstanceSettingsJSON): InstanceSettings {\n    return new InstanceSettings(\n      data.id,\n      data.restricted_to_allowlist,\n      data.from_email_address,\n      data.progressive_sign_up,\n      data.enhanced_email_deliverability,\n    );\n  }\n}\n", "import type { InvitationStatus } from './Enums';\nimport type { InvitationJSON } from './JSON';\n\n/**\n * The Backend `Invitation` object represents an invitation to join your application.\n */\nexport class Invitation {\n  private _raw: InvitationJSON | null = null;\n\n  public get raw(): InvitationJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the `Invitation`.\n     */\n    readonly id: string,\n    /**\n     * The email address that the invitation was sent to.\n     */\n    readonly emailAddress: string,\n    /**\n     * [Metadata](https://clerk.com/docs/references/javascript/types/metadata#user-public-metadata){{ target: '_blank' }} that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API. Once the user accepts the invitation and signs up, these metadata will end up in the user's public metadata.\n     */\n    readonly publicMetadata: Record<string, unknown> | null,\n    /**\n     * The date when the `Invitation` was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the `Invitation` was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The status of the `Invitation`.\n     */\n    readonly status: InvitationStatus,\n    /**\n     * The URL that the user can use to accept the invitation.\n     */\n    readonly url?: string,\n    /**\n     * Whether the `Invitation` has been revoked.\n     */\n    readonly revoked?: boolean,\n  ) {}\n\n  static fromJSON(data: InvitationJSON): Invitation {\n    const res = new Invitation(\n      data.id,\n      data.email_address,\n      data.public_metadata,\n      data.created_at,\n      data.updated_at,\n      data.status,\n      data.url,\n      data.revoked,\n    );\n    res._raw = data;\n    return res;\n  }\n}\n", "import type { SignUpStatus, VerificationStatus } from '@clerk/types';\n\nimport type {\n  ActorTokenStatus,\n  AllowlistIdentifierType,\n  BlocklistIdentifierType,\n  DomainsEnrollmentModes,\n  InvitationStatus,\n  OrganizationDomainVerificationStatus,\n  OrganizationDomainVerificationStrategy,\n  OrganizationEnrollmentMode,\n  OrganizationInvitationStatus,\n  OrganizationMembershipRole,\n  SignInStatus,\n  SignUpVerificationNextAction,\n  WaitlistEntryStatus,\n} from './Enums';\n\nexport const ObjectType = {\n  AccountlessApplication: 'accountless_application',\n  ActorToken: 'actor_token',\n  AllowlistIdentifier: 'allowlist_identifier',\n  ApiKey: 'api_key',\n  BlocklistIdentifier: 'blocklist_identifier',\n  Client: 'client',\n  Cookies: 'cookies',\n  Domain: 'domain',\n  Email: 'email',\n  EmailAddress: 'email_address',\n  ExternalAccount: 'external_account',\n  FacebookAccount: 'facebook_account',\n  GoogleAccount: 'google_account',\n  Instance: 'instance',\n  InstanceRestrictions: 'instance_restrictions',\n  InstanceSettings: 'instance_settings',\n  Invitation: 'invitation',\n  MachineToken: 'machine_to_machine_token',\n  JwtTemplate: 'jwt_template',\n  OauthAccessToken: 'oauth_access_token',\n  IdpOAuthAccessToken: 'clerk_idp_oauth_access_token',\n  OAuthApplication: 'oauth_application',\n  Organization: 'organization',\n  OrganizationDomain: 'organization_domain',\n  OrganizationInvitation: 'organization_invitation',\n  OrganizationMembership: 'organization_membership',\n  OrganizationSettings: 'organization_settings',\n  PhoneNumber: 'phone_number',\n  ProxyCheck: 'proxy_check',\n  RedirectUrl: 'redirect_url',\n  SamlAccount: 'saml_account',\n  SamlConnection: 'saml_connection',\n  Session: 'session',\n  SignInAttempt: 'sign_in_attempt',\n  SignInToken: 'sign_in_token',\n  SignUpAttempt: 'sign_up_attempt',\n  SmsMessage: 'sms_message',\n  User: 'user',\n  WaitlistEntry: 'waitlist_entry',\n  Web3Wallet: 'web3_wallet',\n  Token: 'token',\n  TotalCount: 'total_count',\n  TestingToken: 'testing_token',\n  Role: 'role',\n  Permission: 'permission',\n} as const;\n\nexport type ObjectType = (typeof ObjectType)[keyof typeof ObjectType];\n\nexport interface ClerkResourceJSON {\n  /**\n   * The type of the resource.\n   */\n  object: ObjectType;\n  /**\n   * The unique identifier for the resource.\n   */\n  id: string;\n}\n\nexport interface CookiesJSON {\n  object: typeof ObjectType.Cookies;\n  cookies: string[];\n}\n\nexport interface TokenJSON {\n  object: typeof ObjectType.Token;\n  jwt: string;\n}\n\nexport interface AccountlessApplicationJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.AccountlessApplication;\n  publishable_key: string;\n  secret_key: string;\n  claim_url: string;\n  api_keys_url: string;\n}\n\nexport interface ActorTokenJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.ActorToken;\n  id: string;\n  status: ActorTokenStatus;\n  user_id: string;\n  actor: Record<string, unknown> | null;\n  token?: string | null;\n  url?: string | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface AllowlistIdentifierJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.AllowlistIdentifier;\n  identifier: string;\n  identifier_type: AllowlistIdentifierType;\n  instance_id?: string;\n  invitation_id?: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface BlocklistIdentifierJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.BlocklistIdentifier;\n  identifier: string;\n  identifier_type: BlocklistIdentifierType;\n  instance_id?: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface ClientJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Client;\n  session_ids: string[];\n  sessions: SessionJSON[];\n  sign_in_id: string | null;\n  sign_up_id: string | null;\n  last_active_session_id: string | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface CnameTargetJSON {\n  host: string;\n  value: string;\n  /**\n   * Denotes whether this CNAME target is required to be set in order for the domain to be considered deployed.\n   */\n  required: boolean;\n}\n\nexport interface DomainJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Domain;\n  id: string;\n  name: string;\n  is_satellite: boolean;\n  frontend_api_url: string;\n  /**\n   * null for satellite domains\n   */\n  accounts_portal_url?: string | null;\n  proxy_url?: string;\n  development_origin: string;\n  cname_targets: CnameTargetJSON[];\n}\n\nexport interface EmailJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Email;\n  slug?: string | null;\n  from_email_name: string;\n  to_email_address?: string;\n  email_address_id: string | null;\n  user_id?: string | null;\n  subject?: string;\n  body?: string;\n  body_plain?: string | null;\n  status?: string;\n  data?: Record<string, any> | null;\n  delivered_by_clerk: boolean;\n}\n\nexport interface EmailAddressJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.EmailAddress;\n  email_address: string;\n  verification: VerificationJSON | null;\n  linked_to: IdentificationLinkJSON[];\n}\n\nexport interface ExternalAccountJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.ExternalAccount;\n  provider: string;\n  identification_id: string;\n  provider_user_id: string;\n  approved_scopes: string;\n  email_address: string;\n  first_name: string;\n  last_name: string;\n  image_url?: string;\n  username: string | null;\n  phone_number: string | null;\n  public_metadata?: Record<string, unknown> | null;\n  label: string | null;\n  verification: VerificationJSON | null;\n}\n\nexport interface JwksJSON {\n  keys?: JwksKeyJSON[];\n}\n\nexport interface JwksKeyJSON {\n  use: string;\n  kty: string;\n  kid: string;\n  alg: string;\n  n: string;\n  e: string;\n}\n\nexport interface JwtTemplateJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.JwtTemplate;\n  id: string;\n  name: string;\n  claims: object;\n  lifetime: number;\n  allowed_clock_skew: number;\n  custom_signing_key: boolean;\n  signing_algorithm: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SamlAccountJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SamlAccount;\n  provider: string;\n  provider_user_id: string | null;\n  active: boolean;\n  email_address: string;\n  first_name: string;\n  last_name: string;\n  verification: VerificationJSON | null;\n  saml_connection: SamlAccountConnectionJSON | null;\n}\n\nexport interface IdentificationLinkJSON extends ClerkResourceJSON {\n  type: string;\n}\n\nexport interface OrganizationSettingsJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.OrganizationSettings;\n  enabled: boolean;\n  max_allowed_memberships: number;\n  max_allowed_roles: number;\n  max_allowed_permissions: number;\n  creator_role: string;\n  admin_delete_enabled: boolean;\n  domains_enabled: boolean;\n  domains_enrollment_modes: Array<DomainsEnrollmentModes>;\n  domains_default_role: string;\n}\n\nexport interface InstanceJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Instance;\n  id: string;\n  environment_type: string;\n  allowed_origins: Array<string> | null;\n}\n\nexport interface InstanceRestrictionsJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.InstanceRestrictions;\n  allowlist: boolean;\n  blocklist: boolean;\n  block_email_subaddresses: boolean;\n  block_disposable_email_domains: boolean;\n  ignore_dots_for_gmail_addresses: boolean;\n}\n\nexport interface InstanceSettingsJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.InstanceSettings;\n  id: string;\n  restricted_to_allowlist: boolean;\n  from_email_address: string;\n  progressive_sign_up: boolean;\n  enhanced_email_deliverability: boolean;\n}\n\nexport interface InvitationJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Invitation;\n  email_address: string;\n  public_metadata: Record<string, unknown> | null;\n  revoked?: boolean;\n  status: InvitationStatus;\n  url?: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface OauthAccessTokenJSON {\n  external_account_id: string;\n  object: typeof ObjectType.OauthAccessToken;\n  token: string;\n  provider: string;\n  public_metadata: Record<string, unknown>;\n  label: string | null;\n  // Only set in OAuth 2.0 tokens\n  scopes?: string[];\n  // Only set in OAuth 1.0 tokens\n  token_secret?: string;\n  expires_at?: number;\n}\n\nexport interface OAuthApplicationJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.OAuthApplication;\n  id: string;\n  instance_id: string;\n  name: string;\n  client_id: string;\n  public: boolean;\n  scopes: string;\n  redirect_uris: Array<string>;\n  authorize_url: string;\n  token_fetch_url: string;\n  user_info_url: string;\n  discovery_url: string;\n  token_introspection_url: string;\n  created_at: number;\n  updated_at: number;\n  client_secret?: string;\n}\n\nexport interface OrganizationJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Organization;\n  name: string;\n  slug: string;\n  image_url?: string;\n  has_image: boolean;\n  members_count?: number;\n  pending_invitations_count?: number;\n  max_allowed_memberships: number;\n  admin_delete_enabled: boolean;\n  public_metadata: OrganizationPublicMetadata | null;\n  private_metadata?: OrganizationPrivateMetadata;\n  created_by?: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface OrganizationDomainJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.OrganizationDomain;\n  id: string;\n  name: string;\n  organization_id: string;\n  enrollment_mode: OrganizationEnrollmentMode;\n  verification: OrganizationDomainVerificationJSON | null;\n  affiliation_email_address: string | null;\n  created_at: number;\n  updated_at: number;\n  total_pending_invitations: number;\n  total_pending_suggestions: number;\n}\n\nexport interface OrganizationDomainVerificationJSON {\n  status: OrganizationDomainVerificationStatus;\n  strategy: OrganizationDomainVerificationStrategy;\n  attempts: number;\n  expires_at: number;\n}\n\nexport interface OrganizationInvitationJSON extends ClerkResourceJSON {\n  email_address: string;\n  role: OrganizationMembershipRole;\n  role_name: string;\n  organization_id: string;\n  public_organization_data?: PublicOrganizationDataJSON | null;\n  status?: OrganizationInvitationStatus;\n  public_metadata: OrganizationInvitationPublicMetadata;\n  private_metadata: OrganizationInvitationPrivateMetadata;\n  url: string | null;\n  created_at: number;\n  updated_at: number;\n  expires_at: number;\n}\n\n/**\n * @interface\n */\nexport interface PublicOrganizationDataJSON extends ClerkResourceJSON {\n  /**\n   * The name of the organization.\n   */\n  name: string;\n  /**\n   * The slug of the organization.\n   */\n  slug: string;\n  /**\n   * Holds the default organization profile image. Compatible with Clerk's [Image Optimization](https://clerk.com/docs/guides/image-optimization).\n   */\n  image_url?: string;\n  /**\n   * Whether the organization has a profile image.\n   */\n  has_image: boolean;\n}\n\nexport interface OrganizationMembershipJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.OrganizationMembership;\n  public_metadata: OrganizationMembershipPublicMetadata;\n  private_metadata?: OrganizationMembershipPrivateMetadata;\n  role: OrganizationMembershipRole;\n  permissions: string[];\n  created_at: number;\n  updated_at: number;\n  organization: OrganizationJSON;\n  public_user_data: OrganizationMembershipPublicUserDataJSON;\n}\n\nexport interface OrganizationMembershipPublicUserDataJSON {\n  identifier: string;\n  first_name: string | null;\n  last_name: string | null;\n  image_url: string;\n  has_image: boolean;\n  user_id: string;\n}\n\nexport interface PhoneNumberJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.PhoneNumber;\n  phone_number: string;\n  reserved_for_second_factor: boolean;\n  default_second_factor: boolean;\n  reserved: boolean;\n  verification: VerificationJSON | null;\n  linked_to: IdentificationLinkJSON[];\n  backup_codes: string[];\n}\n\nexport type ProxyCheckJSON = {\n  object: typeof ObjectType.ProxyCheck;\n  id: string;\n  domain_id: string;\n  last_run_at: number | null;\n  proxy_url: string;\n  successful: boolean;\n  created_at: number;\n  updated_at: number;\n};\n\nexport interface RedirectUrlJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.RedirectUrl;\n  url: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SessionActivityJSON extends ClerkResourceJSON {\n  id: string;\n  device_type?: string;\n  is_mobile: boolean;\n  browser_name?: string;\n  browser_version?: string;\n  ip_address?: string;\n  city?: string;\n  country?: string;\n}\n\nexport interface SessionJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Session;\n  client_id: string;\n  user_id: string;\n  status: string;\n  last_active_organization_id?: string;\n  actor: Record<string, unknown> | null;\n  latest_activity?: SessionActivityJSON;\n  last_active_at: number;\n  expire_at: number;\n  abandon_at: number;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SignInJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SignInToken;\n  status: SignInStatus;\n  identifier: string;\n  created_session_id: string | null;\n}\n\nexport interface SignInTokenJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SignInToken;\n  user_id: string;\n  token: string;\n  status: 'pending' | 'accepted' | 'revoked';\n  url: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SignUpJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SignUpAttempt;\n  id: string;\n  status: SignUpStatus;\n  required_fields: string[];\n  optional_fields: string[];\n  missing_fields: string[];\n  unverified_fields: string[];\n  verifications: SignUpVerificationsJSON;\n  username: string | null;\n  email_address: string | null;\n  phone_number: string | null;\n  web3_wallet: string | null;\n  password_enabled: boolean;\n  first_name: string | null;\n  last_name: string | null;\n  public_metadata?: Record<string, unknown> | null;\n  unsafe_metadata?: Record<string, unknown> | null;\n  custom_action: boolean;\n  external_id: string | null;\n  created_session_id: string | null;\n  created_user_id: string | null;\n  abandon_at: number | null;\n  legal_accepted_at: number | null;\n\n  /**\n   * @deprecated Please use `verifications.external_account` instead\n   */\n  external_account: object | null;\n}\n\nexport interface SignUpVerificationsJSON {\n  email_address: SignUpVerificationJSON;\n  phone_number: SignUpVerificationJSON;\n  web3_wallet: SignUpVerificationJSON;\n  external_account: VerificationJSON;\n}\n\nexport interface SignUpVerificationJSON {\n  next_action: SignUpVerificationNextAction;\n  supported_strategies: string[];\n}\n\nexport interface SMSMessageJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SmsMessage;\n  from_phone_number: string;\n  to_phone_number: string;\n  phone_number_id: string | null;\n  user_id?: string;\n  message: string;\n  status: string;\n  slug?: string | null;\n  data?: Record<string, any> | null;\n  delivered_by_clerk: boolean;\n}\n\nexport interface UserJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.User;\n  username: string | null;\n  first_name: string | null;\n  last_name: string | null;\n  image_url: string;\n  has_image: boolean;\n  primary_email_address_id: string | null;\n  primary_phone_number_id: string | null;\n  primary_web3_wallet_id: string | null;\n  password_enabled: boolean;\n  two_factor_enabled: boolean;\n  totp_enabled: boolean;\n  backup_code_enabled: boolean;\n  email_addresses: EmailAddressJSON[];\n  phone_numbers: PhoneNumberJSON[];\n  web3_wallets: Web3WalletJSON[];\n  organization_memberships: OrganizationMembershipJSON[] | null;\n  external_accounts: ExternalAccountJSON[];\n  saml_accounts: SamlAccountJSON[];\n  password_last_updated_at: number | null;\n  public_metadata: UserPublicMetadata;\n  private_metadata: UserPrivateMetadata;\n  unsafe_metadata: UserUnsafeMetadata;\n  external_id: string | null;\n  last_sign_in_at: number | null;\n  banned: boolean;\n  locked: boolean;\n  lockout_expires_in_seconds: number | null;\n  verification_attempts_remaining: number | null;\n  created_at: number;\n  updated_at: number;\n  last_active_at: number | null;\n  create_organization_enabled: boolean;\n  create_organizations_limit: number | null;\n  delete_self_enabled: boolean;\n  legal_accepted_at: number | null;\n}\n\nexport interface VerificationJSON extends ClerkResourceJSON {\n  status: VerificationStatus;\n  strategy: string;\n  attempts: number | null;\n  expire_at: number | null;\n  verified_at_client?: string;\n  external_verification_redirect_url?: string | null;\n  nonce?: string | null;\n  message?: string | null;\n}\n\nexport interface WaitlistEntryJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.WaitlistEntry;\n  id: string;\n  status: WaitlistEntryStatus;\n  email_address: string;\n  invitation: InvitationJSON | null;\n  is_locked: boolean;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface Web3WalletJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Web3Wallet;\n  web3_wallet: string;\n  verification: VerificationJSON | null;\n}\n\nexport interface DeletedObjectJSON {\n  object: string;\n  id?: string;\n  slug?: string;\n  deleted: boolean;\n}\n\nexport interface PaginatedResponseJSON {\n  data: object[];\n  total_count?: number;\n}\n\nexport interface SamlConnectionJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.SamlConnection;\n  name: string;\n  domain: string;\n  organization_id: string | null;\n  idp_entity_id: string;\n  idp_sso_url: string;\n  idp_certificate: string;\n  idp_metadata_url: string;\n  idp_metadata: string;\n  acs_url: string;\n  sp_entity_id: string;\n  sp_metadata_url: string;\n  active: boolean;\n  provider: string;\n  user_count: number;\n  sync_user_attributes: boolean;\n  allow_subdomains: boolean;\n  allow_idp_initiated: boolean;\n  created_at: number;\n  updated_at: number;\n  attribute_mapping: AttributeMappingJSON;\n}\n\nexport interface AttributeMappingJSON {\n  user_id: string;\n  email_address: string;\n  first_name: string;\n  last_name: string;\n}\n\nexport interface TestingTokenJSON {\n  object: typeof ObjectType.TestingToken;\n  token: string;\n  expires_at: number;\n}\n\nexport interface RoleJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Role;\n  key: string;\n  name: string;\n  description: string;\n  permissions: PermissionJSON[];\n  is_creator_eligible: boolean;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface PermissionJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.Permission;\n  key: string;\n  name: string;\n  description: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SamlAccountConnectionJSON extends ClerkResourceJSON {\n  id: string;\n  name: string;\n  domain: string;\n  active: boolean;\n  provider: string;\n  sync_user_attributes: boolean;\n  allow_subdomains: boolean;\n  allow_idp_initiated: boolean;\n  disable_additional_identifications: boolean;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface MachineTokenJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.MachineToken;\n  name: string;\n  subject: string;\n  scopes: string[];\n  claims: Record<string, any> | null;\n  revoked: boolean;\n  revocation_reason: string | null;\n  expired: boolean;\n  expiration: number | null;\n  created_by: string | null;\n  creation_reason: string | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface APIKeyJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.ApiKey;\n  type: string;\n  name: string;\n  subject: string;\n  scopes: string[];\n  claims: Record<string, any> | null;\n  revoked: boolean;\n  revocation_reason: string | null;\n  expired: boolean;\n  expiration: number | null;\n  created_by: string | null;\n  description: string | null;\n  last_used_at: number | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface IdPOAuthAccessTokenJSON extends ClerkResourceJSON {\n  object: typeof ObjectType.IdpOAuthAccessToken;\n  client_id: string;\n  type: string;\n  subject: string;\n  scopes: string[];\n  revoked: boolean;\n  revocation_reason: string | null;\n  expired: boolean;\n  expiration: number | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface WebhooksSvixJSON {\n  svix_url: string;\n}\n", "import type { MachineTokenJSON } from './JSON';\n\nexport class MachineToken {\n  constructor(\n    readonly id: string,\n    readonly name: string,\n    readonly subject: string,\n    readonly scopes: string[],\n    readonly claims: Record<string, any> | null,\n    readonly revoked: boolean,\n    readonly revocationReason: string | null,\n    readonly expired: boolean,\n    readonly expiration: number | null,\n    readonly createdBy: string | null,\n    readonly creationReason: string | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: MachineTokenJSON) {\n    return new MachineToken(\n      data.id,\n      data.name,\n      data.subject,\n      data.scopes,\n      data.claims,\n      data.revoked,\n      data.revocation_reason,\n      data.expired,\n      data.expiration,\n      data.created_by,\n      data.creation_reason,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { JwtTemplateJSON } from './JSON';\n\nexport class JwtTemplate {\n  constructor(\n    readonly id: string,\n    readonly name: string,\n    readonly claims: object,\n    readonly lifetime: number,\n    readonly allowedClockSkew: number,\n    readonly customSigningKey: boolean,\n    readonly signingAlgorithm: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: JwtTemplateJSON): JwtTemplate {\n    return new JwtTemplate(\n      data.id,\n      data.name,\n      data.claims,\n      data.lifetime,\n      data.allowed_clock_skew,\n      data.custom_signing_key,\n      data.signing_algorithm,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { OauthAccessTokenJSON } from './JSON';\n\nexport class OauthAccessToken {\n  constructor(\n    readonly externalAccountId: string,\n    readonly provider: string,\n    readonly token: string,\n    readonly publicMetadata: Record<string, unknown> = {},\n    readonly label: string,\n    readonly scopes?: string[],\n    readonly tokenSecret?: string,\n    readonly expiresAt?: number,\n  ) {}\n\n  static fromJSON(data: OauthAccessTokenJSON) {\n    return new OauthAccessToken(\n      data.external_account_id,\n      data.provider,\n      data.token,\n      data.public_metadata,\n      data.label || '',\n      data.scopes,\n      data.token_secret,\n      data.expires_at,\n    );\n  }\n}\n", "import type { OAuthApplicationJSON } from './JSON';\n\nexport class OAuthApplication {\n  constructor(\n    readonly id: string,\n    readonly instanceId: string,\n    readonly name: string,\n    readonly clientId: string,\n    readonly isPublic: boolean, // NOTE: `public` is reserved\n    readonly scopes: string,\n    readonly redirectUris: Array<string>,\n    readonly authorizeUrl: string,\n    readonly tokenFetchUrl: string,\n    readonly userInfoUrl: string,\n    readonly discoveryUrl: string,\n    readonly tokenIntrospectionUrl: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly clientSecret?: string,\n  ) {}\n\n  static fromJSON(data: OAuthApplicationJSON) {\n    return new OAuthApplication(\n      data.id,\n      data.instance_id,\n      data.name,\n      data.client_id,\n      data.public,\n      data.scopes,\n      data.redirect_uris,\n      data.authorize_url,\n      data.token_fetch_url,\n      data.user_info_url,\n      data.discovery_url,\n      data.token_introspection_url,\n      data.created_at,\n      data.updated_at,\n      data.client_secret,\n    );\n  }\n}\n", "import type { OrganizationJSON } from './JSON';\n\n/**\n * The Backend `Organization` object is similar to the [`Organization`](https://clerk.com/docs/references/javascript/organization) object as it holds information about an organization, as well as methods for managing it. However, the Backend `Organization` object is different in that it is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Organizations#operation/ListOrganizations){{ target: '_blank' }} and is not directly accessible from the Frontend API.\n */\nexport class Organization {\n  private _raw: OrganizationJSON | null = null;\n\n  public get raw(): OrganizationJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the organization.\n     */\n    readonly id: string,\n    /**\n     * The name of the organization.\n     */\n    readonly name: string,\n    /**\n     * The URL-friendly identifier of the user's active organization. If supplied, it must be unique for the instance.\n     */\n    readonly slug: string,\n    /**\n     * Holds the organization's logo. Compatible with Clerk's [Image Optimization](https://clerk.com/docs/guides/image-optimization).\n     */\n    readonly imageUrl: string,\n    /**\n     * Whether the organization has an image.\n     */\n    readonly hasImage: boolean,\n    /**\n     * The date when the organization was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the organization was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * Metadata that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API.\n     */\n    readonly publicMetadata: OrganizationPublicMetadata | null = {},\n    /**\n     * Metadata that can be read and set only from the [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }}.\n     */\n    readonly privateMetadata: OrganizationPrivateMetadata = {},\n    /**\n     * The maximum number of memberships allowed in the organization.\n     */\n    readonly maxAllowedMemberships: number,\n    /**\n     * Whether the organization allows admins to delete users.\n     */\n    readonly adminDeleteEnabled: boolean,\n    /**\n     * The number of members in the organization.\n     */\n    readonly membersCount?: number,\n    /**\n     * The ID of the user who created the organization.\n     */\n    readonly createdBy?: string,\n  ) {}\n\n  static fromJSON(data: OrganizationJSON): Organization {\n    const res = new Organization(\n      data.id,\n      data.name,\n      data.slug,\n      data.image_url || '',\n      data.has_image,\n      data.created_at,\n      data.updated_at,\n      data.public_metadata,\n      data.private_metadata,\n      data.max_allowed_memberships,\n      data.admin_delete_enabled,\n      data.members_count,\n      data.created_by,\n    );\n    res._raw = data;\n    return res;\n  }\n}\n", "import type { OrganizationInvitationStatus, OrganizationMembershipRole } from './Enums';\nimport type { OrganizationInvitationJSON, PublicOrganizationDataJSON } from './JSON';\n\n/**\n * The Backend `OrganizationInvitation` object is similar to the [`OrganizationInvitation`](https://clerk.com/docs/references/javascript/types/organization-invitation) object as it's the model around an organization invitation. However, the Backend `OrganizationInvitation` object is different in that it's used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Organization-Invitations#operation/CreateOrganizationInvitation){{ target: '_blank' }} and is not directly accessible from the Frontend API.\n */\nexport class OrganizationInvitation {\n  private _raw: OrganizationInvitationJSON | null = null;\n\n  public get raw(): OrganizationInvitationJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the `OrganizationInvitation`.\n     */\n    readonly id: string,\n    /**\n     * The email address of the user who is invited to the [`Organization`](https://clerk.com/docs/references/backend/types/backend-organization).\n     */\n    readonly emailAddress: string,\n    /**\n     * The role of the invited user.\n     */\n    readonly role: OrganizationMembershipRole,\n    /**\n     * The name of the role of the invited user.\n     */\n    readonly roleName: string,\n    /**\n     * The ID of the [`Organization`](https://clerk.com/docs/references/backend/types/backend-organization) that the user is invited to.\n     */\n    readonly organizationId: string,\n    /**\n     * The date when the invitation was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the invitation was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The date when the invitation expires.\n     */\n    readonly expiresAt: number,\n    /**\n     * The URL that the user can use to accept the invitation.\n     */\n    readonly url: string | null,\n    /**\n     * The status of the invitation.\n     */\n    readonly status?: OrganizationInvitationStatus,\n    /**\n     * Metadata that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API.\n     */\n    readonly publicMetadata: OrganizationInvitationPublicMetadata = {},\n    /**\n     * Metadata that can be read and set only from the [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }}.\n     */\n    readonly privateMetadata: OrganizationInvitationPrivateMetadata = {},\n    /**\n     * Public data about the organization that the user is invited to.\n     */\n    readonly publicOrganizationData?: PublicOrganizationDataJSON | null,\n  ) {}\n\n  static fromJSON(data: OrganizationInvitationJSON) {\n    const res = new OrganizationInvitation(\n      data.id,\n      data.email_address,\n      data.role,\n      data.role_name,\n      data.organization_id,\n      data.created_at,\n      data.updated_at,\n      data.expires_at,\n      data.url,\n      data.status,\n      data.public_metadata,\n      data.private_metadata,\n      data.public_organization_data,\n    );\n    res._raw = data;\n    return res;\n  }\n}\n", "import { Organization } from '../resources';\nimport type { OrganizationMembershipRole } from './Enums';\nimport type { OrganizationMembershipJSON, OrganizationMembershipPublicUserDataJSON } from './JSON';\n\n/**\n * The Backend `OrganizationMembership` object is similar to the [`OrganizationMembership`](https://clerk.com/docs/references/javascript/types/organization-membership) object as it's the model around an organization membership entity and describes the relationship between users and organizations. However, the Backend `OrganizationMembership` object is different in that it's used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Organization-Memberships#operation/CreateOrganizationMembership){{ target: '_blank' }} and is not directly accessible from the Frontend API.\n */\nexport class OrganizationMembership {\n  private _raw: OrganizationMembershipJSON | null = null;\n\n  public get raw(): OrganizationMembershipJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the membership.\n     */\n    readonly id: string,\n    /**\n     * The role of the user.\n     */\n    readonly role: OrganizationMembershipRole,\n    /**\n     * The permissions granted to the user in the organization.\n     */\n    readonly permissions: string[],\n    /**\n     * Metadata that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API.\n     */\n    readonly publicMetadata: OrganizationMembershipPublicMetadata = {},\n    /**\n     * Metadata that can be read and set only from the [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }}.\n     */\n    readonly privateMetadata: OrganizationMembershipPrivateMetadata = {},\n    /**\n     * The date when the membership was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the membership was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The organization that the user is a member of.\n     */\n    readonly organization: Organization,\n    /**\n     * Public information about the user that this membership belongs to.\n     */\n    readonly publicUserData?: OrganizationMembershipPublicUserData | null,\n  ) {}\n\n  static fromJSON(data: OrganizationMembershipJSON) {\n    const res = new OrganizationMembership(\n      data.id,\n      data.role,\n      data.permissions,\n      data.public_metadata,\n      data.private_metadata,\n      data.created_at,\n      data.updated_at,\n      Organization.fromJSON(data.organization),\n      OrganizationMembershipPublicUserData.fromJSON(data.public_user_data),\n    );\n    res._raw = data;\n    return res;\n  }\n}\n\n/**\n * @class\n */\nexport class OrganizationMembershipPublicUserData {\n  constructor(\n    /**\n     * The [identifier](https://clerk.com/docs/authentication/configuration/sign-up-sign-in-options#identifiers) of the user.\n     */\n    readonly identifier: string,\n    /**\n     * The first name of the user.\n     */\n    readonly firstName: string | null,\n    /**\n     * The last name of the user.\n     */\n    readonly lastName: string | null,\n    /**\n     * Holds the default avatar or user's uploaded profile image. Compatible with Clerk's [Image Optimization](https://clerk.com/docs/guides/image-optimization).\n     */\n    readonly imageUrl: string,\n    /**\n     * Whether the user has a profile picture.\n     */\n    readonly hasImage: boolean,\n    /**\n     * The ID of the user that this public data belongs to.\n     */\n    readonly userId: string,\n  ) {}\n\n  static fromJSON(data: OrganizationMembershipPublicUserDataJSON) {\n    return new OrganizationMembershipPublicUserData(\n      data.identifier,\n      data.first_name,\n      data.last_name,\n      data.image_url,\n      data.has_image,\n      data.user_id,\n    );\n  }\n}\n", "import type { DomainsEnrollmentModes } from './Enums';\nimport type { OrganizationSettingsJSON } from './JSON';\n\nexport class OrganizationSettings {\n  constructor(\n    readonly enabled: boolean,\n    readonly maxAllowedMemberships: number,\n    readonly maxAllowedRoles: number,\n    readonly maxAllowedPermissions: number,\n    readonly creatorRole: string,\n    readonly adminDeleteEnabled: boolean,\n    readonly domainsEnabled: boolean,\n    readonly domainsEnrollmentModes: Array<DomainsEnrollmentModes>,\n    readonly domainsDefaultRole: string,\n  ) {}\n\n  static fromJSON(data: OrganizationSettingsJSON): OrganizationSettings {\n    return new OrganizationSettings(\n      data.enabled,\n      data.max_allowed_memberships,\n      data.max_allowed_roles,\n      data.max_allowed_permissions,\n      data.creator_role,\n      data.admin_delete_enabled,\n      data.domains_enabled,\n      data.domains_enrollment_modes,\n      data.domains_default_role,\n    );\n  }\n}\n", "import { IdentificationLink } from './IdentificationLink';\nimport type { PhoneNumberJSON } from './JSON';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `PhoneNumber` object describes a phone number. Phone numbers can be used as a proof of identification for users, or simply as a means of contacting users.\n *\n * Phone numbers must be **verified** to ensure that they can be assigned to their rightful owners. The `PhoneNumber` object holds all the necessary state around the verification process.\n *\n * Finally, phone numbers can be used as part of [multi-factor authentication](https://clerk.com/docs/authentication/configuration/sign-up-sign-in-options#multi-factor-authentication). During sign in, users can opt in to an extra verification step where they will receive an SMS message with a one-time code. This code must be entered to complete the sign in process.\n */\nexport class PhoneNumber {\n  constructor(\n    /**\n     * The unique identifier for this phone number.\n     */\n    readonly id: string,\n    /**\n     * The value of this phone number, in [E.164 format](https://en.wikipedia.org/wiki/E.164).\n     */\n    readonly phoneNumber: string,\n    /**\n     * Set to `true` if this phone number is reserved for multi-factor authentication (2FA). Set to `false` otherwise.\n     */\n    readonly reservedForSecondFactor: boolean,\n    /**\n     * Set to `true` if this phone number is the default second factor. Set to `false` otherwise. A user must have exactly one default second factor, if multi-factor authentication (2FA) is enabled.\n     */\n    readonly defaultSecondFactor: boolean,\n    /**\n     * An object holding information on the verification of this phone number.\n     */\n    readonly verification: Verification | null,\n    /**\n     * An object containing information about any other identification that might be linked to this phone number.\n     */\n    readonly linkedTo: IdentificationLink[],\n  ) {}\n\n  static fromJSON(data: PhoneNumberJSON): PhoneNumber {\n    return new PhoneNumber(\n      data.id,\n      data.phone_number,\n      data.reserved_for_second_factor,\n      data.default_second_factor,\n      data.verification && Verification.fromJSON(data.verification),\n      data.linked_to.map(link => IdentificationLink.fromJSON(link)),\n    );\n  }\n}\n", "import type { Proxy<PERSON>heckJSON } from './JSON';\n\nexport class Proxy<PERSON>heck {\n  constructor(\n    readonly id: string,\n    readonly domainId: string,\n    readonly lastRunAt: number | null,\n    readonly proxyUrl: string,\n    readonly successful: boolean,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: ProxyCheckJSON): ProxyCheck {\n    return new ProxyCheck(\n      data.id,\n      data.domain_id,\n      data.last_run_at,\n      data.proxy_url,\n      data.successful,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { RedirectUrlJSON } from './JSON';\n\n/**\n * Redirect URLs are whitelisted URLs that facilitate secure authentication flows in native applications (e.g. React Native, Expo). In these contexts, Clerk ensures that security-critical nonces are passed only to the whitelisted URLs.\n\nThe Backend `RedirectUrl` object represents a redirect URL in your application. This object is used in the Backend API.\n */\nexport class RedirectUrl {\n  constructor(\n    /**\n     * The unique identifier for the redirect URL.\n     */\n    readonly id: string,\n    /**\n     * The full URL value prefixed with `https://` or a custom scheme.\n     * @example https://my-app.com/oauth-callback\n     * @example my-app://oauth-callback\n     */\n    readonly url: string,\n    /**\n     * The date when the redirect URL was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the redirect URL was last updated.\n     */\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: RedirectUrlJSON): RedirectUrl {\n    return new RedirectUrl(data.id, data.url, data.created_at, data.updated_at);\n  }\n}\n", "import type { Attribute<PERSON>appingJSON, SamlAccountConnectionJSON, SamlConnectionJSON } from './JSON';\n\n/**\n * The Backend `SamlConnection` object holds information about a SAML connection for an organization.\n */\nexport class SamlConnection {\n  constructor(\n    /**\n     * The unique identifier for the connection.\n     */\n    readonly id: string,\n    /**\n     * The name to use as a label for the connection.\n     */\n    readonly name: string,\n    /**\n     * The domain of your organization. Sign in flows using an email with this domain will use the connection.\n     */\n    readonly domain: string,\n    /**\n     * The organization ID of the organization.\n     */\n    readonly organizationId: string | null,\n    /**\n     * The Entity ID as provided by the Identity Provider (IdP).\n     */\n    readonly idpEntityId: string | null,\n    /**\n     * The Single-Sign On URL as provided by the Identity Provider (IdP).\n     */\n    readonly idpSsoUrl: string | null,\n    /**\n     * The X.509 certificate as provided by the Identity Provider (IdP).\n     */\n    readonly idpCertificate: string | null,\n    /**\n     * The URL which serves the Identity Provider (IdP) metadata. If present, it takes priority over the corresponding individual properties.\n     */\n    readonly idpMetadataUrl: string | null,\n    /**\n     * The XML content of the Identity Provider (IdP) metadata file. If present, it takes priority over the corresponding individual properties.\n     */\n    readonly idpMetadata: string | null,\n    /**\n     * The Assertion Consumer Service (ACS) URL of the connection.\n     */\n    readonly acsUrl: string,\n    /**\n     * The Entity ID as provided by the Service Provider (Clerk).\n     */\n    readonly spEntityId: string,\n    /**\n     * The metadata URL as provided by the Service Provider (Clerk).\n     */\n    readonly spMetadataUrl: string,\n    /**\n     * Indicates whether the connection is active or not.\n     */\n    readonly active: boolean,\n    /**\n     * The Identity Provider (IdP) of the connection.\n     */\n    readonly provider: string,\n    /**\n     * The number of users associated with the connection.\n     */\n    readonly userCount: number,\n    /**\n     * Indicates whether the connection syncs user attributes between the Service Provider (SP) and Identity Provider (IdP) or not.\n     */\n    readonly syncUserAttributes: boolean,\n    /**\n     * Indicates whether users with an email address subdomain are allowed to use this connection in order to authenticate or not.\n     */\n    readonly allowSubdomains: boolean,\n    /**\n     * Indicates whether the connection allows Identity Provider (IdP) initiated flows or not.\n     */\n    readonly allowIdpInitiated: boolean,\n    /**\n     * The date when the connection was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the SAML connection was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * Defines the attribute name mapping between the Identity Provider (IdP) and Clerk's [`User`](https://clerk.com/docs/references/javascript/user) properties.\n     */\n    readonly attributeMapping: AttributeMapping,\n  ) {}\n  static fromJSON(data: SamlConnectionJSON): SamlConnection {\n    return new SamlConnection(\n      data.id,\n      data.name,\n      data.domain,\n      data.organization_id,\n      data.idp_entity_id,\n      data.idp_sso_url,\n      data.idp_certificate,\n      data.idp_metadata_url,\n      data.idp_metadata,\n      data.acs_url,\n      data.sp_entity_id,\n      data.sp_metadata_url,\n      data.active,\n      data.provider,\n      data.user_count,\n      data.sync_user_attributes,\n      data.allow_subdomains,\n      data.allow_idp_initiated,\n      data.created_at,\n      data.updated_at,\n      data.attribute_mapping && AttributeMapping.fromJSON(data.attribute_mapping),\n    );\n  }\n}\n\nexport class SamlAccountConnection {\n  constructor(\n    readonly id: string,\n    readonly name: string,\n    readonly domain: string,\n    readonly active: boolean,\n    readonly provider: string,\n    readonly syncUserAttributes: boolean,\n    readonly allowSubdomains: boolean,\n    readonly allowIdpInitiated: boolean,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n  static fromJSON(data: SamlAccountConnectionJSON): SamlAccountConnection {\n    return new SamlAccountConnection(\n      data.id,\n      data.name,\n      data.domain,\n      data.active,\n      data.provider,\n      data.sync_user_attributes,\n      data.allow_subdomains,\n      data.allow_idp_initiated,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n\nclass AttributeMapping {\n  constructor(\n    /**\n     * The user ID attribute name.\n     */\n    readonly userId: string,\n    /**\n     * The email address attribute name.\n     */\n    readonly emailAddress: string,\n    /**\n     * The first name attribute name.\n     */\n    readonly firstName: string,\n    /**\n     * The last name attribute name.\n     */\n    readonly lastName: string,\n  ) {}\n\n  static fromJSON(data: AttributeMappingJSON): AttributeMapping {\n    return new AttributeMapping(data.user_id, data.email_address, data.first_name, data.last_name);\n  }\n}\n", "import type { SamlA<PERSON>untJSON } from './JSON';\nimport { SamlAccountConnection } from './SamlConnection';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `SamlAccount` object describes a SAML account.\n */\nexport class SamlAccount {\n  constructor(\n    /**\n     * The unique identifier for the SAML account.\n     */\n    readonly id: string,\n    /**\n     * The provider of the SAML account.\n     */\n    readonly provider: string,\n    /**\n     * The user's ID as used in the provider.\n     */\n    readonly providerUserId: string | null,\n    /**\n     * A boolean that indicates whether the SAML account is active.\n     */\n    readonly active: boolean,\n    /**\n     * The email address of the SAML account.\n     */\n    readonly emailAddress: string,\n    /**\n     * The first name of the SAML account.\n     */\n    readonly firstName: string,\n    /**\n     * The last name of the SAML account.\n     */\n    readonly lastName: string,\n    /**\n     * The verification of the SAML account.\n     */\n    readonly verification: Verification | null,\n    /**\n     * The SAML connection of the SAML account.\n     */\n    readonly samlConnection: SamlAccountConnection | null,\n  ) {}\n\n  static fromJSON(data: SamlAccountJSON): SamlAccount {\n    return new SamlAccount(\n      data.id,\n      data.provider,\n      data.provider_user_id,\n      data.active,\n      data.email_address,\n      data.first_name,\n      data.last_name,\n      data.verification && Verification.fromJSON(data.verification),\n      data.saml_connection && SamlAccountConnection.fromJSON(data.saml_connection),\n    );\n  }\n}\n", "import type { SignInTokenJSON } from './JSON';\n\nexport class SignInToken {\n  constructor(\n    readonly id: string,\n    readonly userId: string,\n    readonly token: string,\n    readonly status: string,\n    readonly url: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: SignInTokenJSON): SignInToken {\n    return new SignInToken(data.id, data.user_id, data.token, data.status, data.url, data.created_at, data.updated_at);\n  }\n}\n", "import type { SignUpStatus } from '@clerk/types';\n\nimport type { SignUpVerificationNextAction } from './Enums';\nimport type { SignUpJSON, SignUpVerificationJSON, SignUpVerificationsJSON } from './JSON';\n\nexport class SignUpAttemptVerification {\n  constructor(\n    readonly nextAction: SignUpVerificationNextAction,\n    readonly supportedStrategies: string[],\n  ) {}\n\n  static fromJSON(data: SignUpVerificationJSON): SignUpAttemptVerification {\n    return new SignUpAttemptVerification(data.next_action, data.supported_strategies);\n  }\n}\n\nexport class SignUpAttemptVerifications {\n  constructor(\n    readonly emailAddress: SignUpAttemptVerification | null,\n    readonly phoneNumber: SignUpAttemptVerification | null,\n    readonly web3Wallet: SignUpAttemptVerification | null,\n    readonly externalAccount: object | null,\n  ) {}\n\n  static fromJSON(data: SignUpVerificationsJSON): SignUpAttemptVerifications {\n    return new SignUpAttemptVerifications(\n      data.email_address && SignUpAttemptVerification.fromJSON(data.email_address),\n      data.phone_number && SignUpAttemptVerification.fromJSON(data.phone_number),\n      data.web3_wallet && SignUpAttemptVerification.fromJSON(data.web3_wallet),\n      data.external_account,\n    );\n  }\n}\n\nexport class SignUpAttempt {\n  constructor(\n    readonly id: string,\n    readonly status: SignUpStatus,\n    readonly requiredFields: string[],\n    readonly optionalFields: string[],\n    readonly missingFields: string[],\n    readonly unverifiedFields: string[],\n    readonly verifications: SignUpAttemptVerifications | null,\n    readonly username: string | null,\n    readonly emailAddress: string | null,\n    readonly phoneNumber: string | null,\n    readonly web3Wallet: string | null,\n    readonly passwordEnabled: boolean,\n    readonly firstName: string | null,\n    readonly lastName: string | null,\n    readonly customAction: boolean,\n    readonly externalId: string | null,\n    readonly createdSessionId: string | null,\n    readonly createdUserId: string | null,\n    readonly abandonAt: number | null,\n    readonly legalAcceptedAt: number | null,\n    readonly publicMetadata?: Record<string, unknown> | null,\n    readonly unsafeMetadata?: Record<string, unknown> | null,\n  ) {}\n\n  static fromJSON(data: SignUpJSON): SignUpAttempt {\n    return new SignUpAttempt(\n      data.id,\n      data.status,\n      data.required_fields,\n      data.optional_fields,\n      data.missing_fields,\n      data.unverified_fields,\n      data.verifications ? SignUpAttemptVerifications.fromJSON(data.verifications) : null,\n      data.username,\n      data.email_address,\n      data.phone_number,\n      data.web3_wallet,\n      data.password_enabled,\n      data.first_name,\n      data.last_name,\n      data.custom_action,\n      data.external_id,\n      data.created_session_id,\n      data.created_user_id,\n      data.abandon_at,\n      data.legal_accepted_at,\n      data.public_metadata,\n      data.unsafe_metadata,\n    );\n  }\n}\n", "import type { SMSMessageJSON } from './JSON';\n\nexport class SMSMessage {\n  constructor(\n    readonly id: string,\n    readonly fromPhoneNumber: string,\n    readonly toPhoneNumber: string,\n    readonly message: string,\n    readonly status: string,\n    readonly phoneNumberId: string | null,\n    readonly data?: Record<string, any> | null,\n  ) {}\n\n  static fromJSON(data: SMSMessageJSON): SMSMessage {\n    return new SMSMessage(\n      data.id,\n      data.from_phone_number,\n      data.to_phone_number,\n      data.message,\n      data.status,\n      data.phone_number_id,\n      data.data,\n    );\n  }\n}\n", "import type { TokenJSON } from './JSON';\n\nexport class Token {\n  constructor(readonly jwt: string) {}\n\n  static fromJSON(data: TokenJSON): Token {\n    return new Token(data.jwt);\n  }\n}\n", "import type { Web3WalletJSON } from './JSON';\nimport { Verification } from './Verification';\n\n/**\n * The Backend `Web3Wallet` object describes a Web3 wallet address. The address can be used as a proof of identification for users.\n *\n * Web3 addresses must be verified to ensure that they can be assigned to their rightful owners. The verification is completed via Web3 wallet browser extensions, such as [Metamask](https://metamask.io/), [Coinbase Wallet](https://www.coinbase.com/wallet), and [OKX Wallet](https://www.okx.com/help/section/faq-web3-wallet). The `Web3Wallet3` object holds all the necessary state around the verification process.\n */\nexport class Web3Wallet {\n  constructor(\n    /**\n     * The unique ID for the Web3 wallet.\n     */\n    readonly id: string,\n    /**\n     * The Web3 wallet address, made up of 0x + 40 hexadecimal characters.\n     */\n    readonly web3Wallet: string,\n    /**\n     * An object holding information on the verification of this Web3 wallet.\n     */\n    readonly verification: Verification | null,\n  ) {}\n\n  static fromJSON(data: Web3WalletJSON): Web3Wallet {\n    return new Web3Wallet(data.id, data.web3_wallet, data.verification && Verification.fromJSON(data.verification));\n  }\n}\n", "import { EmailAddress } from './EmailAddress';\nimport { ExternalAccount } from './ExternalAccount';\nimport type { ExternalAccountJSON, SamlAccountJSON, UserJSON } from './JSON';\nimport { PhoneNumber } from './PhoneNumber';\nimport { SamlAccount } from './SamlAccount';\nimport { Web3Wallet } from './Web3Wallet';\n\n/**\n * The Backend `User` object is similar to the `User` object as it holds information about a user of your application, such as their unique identifier, name, email addresses, phone numbers, and more. However, the Backend `User` object is different from the `User` object in that it is used in the [Backend API](https://clerk.com/docs/reference/backend-api/tag/Users#operation/GetUser){{ target: '_blank' }} and is not directly accessible from the Frontend API.\n */\nexport class User {\n  private _raw: UserJSON | null = null;\n\n  public get raw(): UserJSON | null {\n    return this._raw;\n  }\n\n  constructor(\n    /**\n     * The unique identifier for the user.\n     */\n    readonly id: string,\n    /**\n     * A boolean indicating whether the user has a password on their account.\n     */\n    readonly passwordEnabled: boolean,\n    /**\n     * A boolean indicating whether the user has enabled TOTP by generating a TOTP secret and verifying it via an authenticator app.\n     */\n    readonly totpEnabled: boolean,\n    /**\n     * A boolean indicating whether the user has enabled Backup codes.\n     */\n    readonly backupCodeEnabled: boolean,\n    /**\n     * A boolean indicating whether the user has enabled two-factor authentication.\n     */\n    readonly twoFactorEnabled: boolean,\n    /**\n     * A boolean indicating whether the user is banned or not.\n     */\n    readonly banned: boolean,\n    /**\n     * A boolean indicating whether the user is banned or not.\n     */\n    readonly locked: boolean,\n    /**\n     * The date when the user was first created.\n     */\n    readonly createdAt: number,\n    /**\n     * The date when the user was last updated.\n     */\n    readonly updatedAt: number,\n    /**\n     * The URL of the user's profile image.\n     */\n    readonly imageUrl: string,\n    /**\n     * A getter boolean to check if the user has uploaded an image or one was copied from OAuth. Returns `false` if Clerk is displaying an avatar for the user.\n     */\n    readonly hasImage: boolean,\n    /**\n     * The ID for the `EmailAddress` that the user has set as primary.\n     */\n    readonly primaryEmailAddressId: string | null,\n    /**\n     * The ID for the `PhoneNumber` that the user has set as primary.\n     */\n    readonly primaryPhoneNumberId: string | null,\n    /**\n     * The ID for the [`Web3Wallet`](https://clerk.com/docs/references/backend/types/backend-web3-wallet) that the user signed up with.\n     */\n    readonly primaryWeb3WalletId: string | null,\n    /**\n     * The date when the user last signed in. May be empty if the user has never signed in.\n     */\n    readonly lastSignInAt: number | null,\n    /**\n     * The ID of the user as used in your external systems. Must be unique across your instance.\n     */\n    readonly externalId: string | null,\n    /**\n     * The user's username.\n     */\n    readonly username: string | null,\n    /**\n     * The user's first name.\n     */\n    readonly firstName: string | null,\n    /**\n     * The user's last name.\n     */\n    readonly lastName: string | null,\n    /**\n     * Metadata that can be read from the Frontend API and [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} and can be set only from the Backend API.\n     */\n    readonly publicMetadata: UserPublicMetadata = {},\n    /**\n     * Metadata that can be read and set only from the [Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }}.\n     */\n    readonly privateMetadata: UserPrivateMetadata = {},\n    /**\n     * Metadata that can be read and set from the Frontend API. It's considered unsafe because it can be modified from the frontend.\n     */\n    readonly unsafeMetadata: UserUnsafeMetadata = {},\n    /**\n     * An array of all the `EmailAddress` objects associated with the user. Includes the primary.\n     */\n    readonly emailAddresses: EmailAddress[] = [],\n    /**\n     * An array of all the `PhoneNumber` objects associated with the user. Includes the primary.\n     */\n    readonly phoneNumbers: PhoneNumber[] = [],\n    /**\n     * An array of all the `Web3Wallet` objects associated with the user. Includes the primary.\n     */\n    readonly web3Wallets: Web3Wallet[] = [],\n    /**\n     * An array of all the `ExternalAccount` objects associated with the user via OAuth. **Note**: This includes both verified & unverified external accounts.\n     */\n    readonly externalAccounts: ExternalAccount[] = [],\n    /**\n     * An array of all the `SamlAccount` objects associated with the user via SAML.\n     */\n    readonly samlAccounts: SamlAccount[] = [],\n    /**\n     * Date when the user was last active.\n     */\n    readonly lastActiveAt: number | null,\n    /**\n     * A boolean indicating whether the organization creation is enabled for the user or not.\n     */\n    readonly createOrganizationEnabled: boolean,\n    /**\n     * An integer indicating the number of organizations that can be created by the user. If the value is `0`, then the user can create unlimited organizations. Default is `null`.\n     */\n    readonly createOrganizationsLimit: number | null = null,\n    /**\n     * A boolean indicating whether the user can delete their own account.\n     */\n    readonly deleteSelfEnabled: boolean,\n    /**\n     * The unix timestamp of when the user accepted the legal requirements. `null` if [**Require express consent to legal documents**](https://clerk.com/docs/authentication/configuration/legal-compliance) is not enabled.\n     */\n    readonly legalAcceptedAt: number | null,\n  ) {}\n\n  static fromJSON(data: UserJSON): User {\n    const res = new User(\n      data.id,\n      data.password_enabled,\n      data.totp_enabled,\n      data.backup_code_enabled,\n      data.two_factor_enabled,\n      data.banned,\n      data.locked,\n      data.created_at,\n      data.updated_at,\n      data.image_url,\n      data.has_image,\n      data.primary_email_address_id,\n      data.primary_phone_number_id,\n      data.primary_web3_wallet_id,\n      data.last_sign_in_at,\n      data.external_id,\n      data.username,\n      data.first_name,\n      data.last_name,\n      data.public_metadata,\n      data.private_metadata,\n      data.unsafe_metadata,\n      (data.email_addresses || []).map(x => EmailAddress.fromJSON(x)),\n      (data.phone_numbers || []).map(x => PhoneNumber.fromJSON(x)),\n      (data.web3_wallets || []).map(x => Web3Wallet.fromJSON(x)),\n      (data.external_accounts || []).map((x: ExternalAccountJSON) => ExternalAccount.fromJSON(x)),\n      (data.saml_accounts || []).map((x: SamlAccountJSON) => SamlAccount.fromJSON(x)),\n      data.last_active_at,\n      data.create_organization_enabled,\n      data.create_organizations_limit,\n      data.delete_self_enabled,\n      data.legal_accepted_at,\n    );\n    res._raw = data;\n    return res;\n  }\n\n  /**\n   * The primary email address of the user.\n   */\n  get primaryEmailAddress() {\n    return this.emailAddresses.find(({ id }) => id === this.primaryEmailAddressId) ?? null;\n  }\n\n  /**\n   * The primary phone number of the user.\n   */\n  get primaryPhoneNumber() {\n    return this.phoneNumbers.find(({ id }) => id === this.primaryPhoneNumberId) ?? null;\n  }\n\n  /**\n   * The primary web3 wallet of the user.\n   */\n  get primaryWeb3Wallet() {\n    return this.web3Wallets.find(({ id }) => id === this.primaryWeb3WalletId) ?? null;\n  }\n\n  /**\n   * The full name of the user.\n   */\n  get fullName() {\n    return [this.firstName, this.lastName].join(' ').trim() || null;\n  }\n}\n", "import type { WaitlistEntryStatus } from './Enums';\nimport { Invitation } from './Invitation';\nimport type { WaitlistEntryJSON } from './JSON';\n\nexport class WaitlistEntry {\n  constructor(\n    readonly id: string,\n    readonly emailAddress: string,\n    readonly status: WaitlistEntryStatus,\n    readonly invitation: Invitation | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly isLocked?: boolean,\n  ) {}\n\n  static fromJSON(data: WaitlistEntryJSON): WaitlistEntry {\n    return new WaitlistEntry(\n      data.id,\n      data.email_address,\n      data.status,\n      data.invitation && Invitation.fromJSON(data.invitation),\n      data.created_at,\n      data.updated_at,\n      data.is_locked,\n    );\n  }\n}\n", "import {\n  ActorToken,\n  AllowlistIdentifier,\n  APIKey,\n  BlocklistIdentifier,\n  Client,\n  Cookies,\n  DeletedObject,\n  Domain,\n  Email,\n  EmailAddress,\n  IdPOAuthAccessToken,\n  Instance,\n  InstanceRestrictions,\n  InstanceSettings,\n  Invitation,\n  JwtTemplate,\n  MachineToken,\n  OauthAccessToken,\n  OAuthApplication,\n  Organization,\n  OrganizationInvitation,\n  OrganizationMembership,\n  OrganizationSettings,\n  PhoneNumber,\n  ProxyCheck,\n  RedirectUrl,\n  SamlConnection,\n  Session,\n  SignInToken,\n  SignUpAttempt,\n  SMSMessage,\n  Token,\n  User,\n} from '.';\nimport { AccountlessApplication } from './AccountlessApplication';\nimport type { PaginatedResponseJSON } from './JSON';\nimport { ObjectType } from './JSON';\nimport { WaitlistEntry } from './WaitlistEntry';\n\ntype ResourceResponse<T> = {\n  /**\n   * An array that contains the fetched data.\n   */\n  data: T;\n};\n\n/**\n * An interface that describes the response of a method that returns a paginated list of resources.\n *\n * If the promise resolves, you will get back the [properties](#properties) listed below. `data` will be an array of the resource type you requested. You can use the `totalCount` property to determine how many total items exist remotely.\n *\n * Some methods that return this type allow pagination with the `limit` and `offset` parameters, in which case the first 10 items will be returned by default. For methods such as [`getAllowlistIdentifierList()`](https://clerk.com/docs/references/backend/allowlist/get-allowlist-identifier-list), which do not take a `limit` or `offset`, all items will be returned.\n *\n * If the promise is rejected, you will receive a `ClerkAPIResponseError` or network error.\n *\n * @interface\n */\nexport type PaginatedResourceResponse<T> = ResourceResponse<T> & {\n  /**\n   * The total count of data that exist remotely.\n   */\n  totalCount: number;\n};\n\nexport function deserialize<U = any>(payload: unknown): PaginatedResourceResponse<U> | ResourceResponse<U> {\n  let data, totalCount: number | undefined;\n\n  if (Array.isArray(payload)) {\n    const data = payload.map(item => jsonToObject(item)) as U;\n    return { data };\n  } else if (isPaginated(payload)) {\n    data = payload.data.map(item => jsonToObject(item)) as U;\n    totalCount = payload.total_count;\n\n    return { data, totalCount };\n  } else {\n    return { data: jsonToObject(payload) };\n  }\n}\n\nfunction isPaginated(payload: unknown): payload is PaginatedResponseJSON {\n  if (!payload || typeof payload !== 'object' || !('data' in payload)) {\n    return false;\n  }\n\n  return Array.isArray(payload.data) && payload.data !== undefined;\n}\n\nfunction getCount(item: PaginatedResponseJSON) {\n  return item.total_count;\n}\n\n// TODO: Revise response deserialization\nfunction jsonToObject(item: any): any {\n  // Special case: DeletedObject\n  // TODO: Improve this check\n  if (typeof item !== 'string' && 'object' in item && 'deleted' in item) {\n    return DeletedObject.fromJSON(item);\n  }\n\n  switch (item.object) {\n    case ObjectType.AccountlessApplication:\n      return AccountlessApplication.fromJSON(item);\n    case ObjectType.ActorToken:\n      return ActorToken.fromJSON(item);\n    case ObjectType.AllowlistIdentifier:\n      return AllowlistIdentifier.fromJSON(item);\n    case ObjectType.ApiKey:\n      return APIKey.fromJSON(item);\n    case ObjectType.BlocklistIdentifier:\n      return BlocklistIdentifier.fromJSON(item);\n    case ObjectType.Client:\n      return Client.fromJSON(item);\n    case ObjectType.Cookies:\n      return Cookies.fromJSON(item);\n    case ObjectType.Domain:\n      return Domain.fromJSON(item);\n    case ObjectType.EmailAddress:\n      return EmailAddress.fromJSON(item);\n    case ObjectType.Email:\n      return Email.fromJSON(item);\n    case ObjectType.IdpOAuthAccessToken:\n      return IdPOAuthAccessToken.fromJSON(item);\n    case ObjectType.Instance:\n      return Instance.fromJSON(item);\n    case ObjectType.InstanceRestrictions:\n      return InstanceRestrictions.fromJSON(item);\n    case ObjectType.InstanceSettings:\n      return InstanceSettings.fromJSON(item);\n    case ObjectType.Invitation:\n      return Invitation.fromJSON(item);\n    case ObjectType.JwtTemplate:\n      return JwtTemplate.fromJSON(item);\n    case ObjectType.MachineToken:\n      return MachineToken.fromJSON(item);\n    case ObjectType.OauthAccessToken:\n      return OauthAccessToken.fromJSON(item);\n    case ObjectType.OAuthApplication:\n      return OAuthApplication.fromJSON(item);\n    case ObjectType.Organization:\n      return Organization.fromJSON(item);\n    case ObjectType.OrganizationInvitation:\n      return OrganizationInvitation.fromJSON(item);\n    case ObjectType.OrganizationMembership:\n      return OrganizationMembership.fromJSON(item);\n    case ObjectType.OrganizationSettings:\n      return OrganizationSettings.fromJSON(item);\n    case ObjectType.PhoneNumber:\n      return PhoneNumber.fromJSON(item);\n    case ObjectType.ProxyCheck:\n      return ProxyCheck.fromJSON(item);\n    case ObjectType.RedirectUrl:\n      return RedirectUrl.fromJSON(item);\n    case ObjectType.SamlConnection:\n      return SamlConnection.fromJSON(item);\n    case ObjectType.SignInToken:\n      return SignInToken.fromJSON(item);\n    case ObjectType.SignUpAttempt:\n      return SignUpAttempt.fromJSON(item);\n    case ObjectType.Session:\n      return Session.fromJSON(item);\n    case ObjectType.SmsMessage:\n      return SMSMessage.fromJSON(item);\n    case ObjectType.Token:\n      return Token.fromJSON(item);\n    case ObjectType.TotalCount:\n      return getCount(item);\n    case ObjectType.User:\n      return User.fromJSON(item);\n    case ObjectType.WaitlistEntry:\n      return WaitlistEntry.fromJSON(item);\n    default:\n      return item;\n  }\n}\n", "import {\n  AccountlessApplicationAP<PERSON>,\n  ActorTokenAPI,\n  AllowlistIdentifierAPI,\n  APIKeysAPI,\n  BetaFeaturesAPI,\n  BlocklistIdentifierAPI,\n  ClientAPI,\n  DomainAPI,\n  EmailAddressAPI,\n  IdPOAuthAccessTokenApi,\n  InstanceAPI,\n  InvitationAPI,\n  JwksAPI,\n  JwtTemplatesApi,\n  MachineTokensApi,\n  OAuthApplicationsApi,\n  OrganizationAPI,\n  PhoneNumberAPI,\n  ProxyCheckAPI,\n  RedirectUrlAPI,\n  SamlConnectionAPI,\n  SessionAPI,\n  SignInTokenAPI,\n  SignUpAPI,\n  TestingTokenAPI,\n  UserAPI,\n  WaitlistEntryAPI,\n  WebhookAPI,\n} from './endpoints';\nimport { buildRequest } from './request';\n\nexport type CreateBackendApiOptions = Parameters<typeof buildRequest>[0];\n\nexport type ApiClient = ReturnType<typeof createBackendApiClient>;\n\nexport function createBackendApiClient(options: CreateBackendApiOptions) {\n  const request = buildRequest(options);\n\n  return {\n    __experimental_accountlessApplications: new AccountlessApplicationAPI(\n      buildRequest({ ...options, requireSecretKey: false }),\n    ),\n    actorTokens: new ActorTokenAPI(request),\n    allowlistIdentifiers: new AllowlistIdentifierAPI(request),\n    betaFeatures: new BetaFeaturesAPI(request),\n    blocklistIdentifiers: new BlocklistIdentifierAPI(request),\n    clients: new ClientAPI(request),\n    domains: new DomainAPI(request),\n    emailAddresses: new EmailAddressAPI(request),\n    instance: new InstanceAPI(request),\n    invitations: new InvitationAPI(request),\n    // Using \"/\" instead of an actual version since they're bapi-proxy endpoints.\n    // bapi-proxy connects directly to C1 without URL versioning,\n    // while API versioning is handled through the Clerk-API-Version header.\n    machineTokens: new MachineTokensApi(\n      buildRequest({\n        ...options,\n        apiVersion: '/',\n      }),\n    ),\n    idPOAuthAccessToken: new IdPOAuthAccessTokenApi(\n      buildRequest({\n        ...options,\n        apiVersion: '/',\n      }),\n    ),\n    apiKeys: new APIKeysAPI(\n      buildRequest({\n        ...options,\n        apiVersion: '/',\n      }),\n    ),\n    jwks: new JwksAPI(request),\n    jwtTemplates: new JwtTemplatesApi(request),\n    oauthApplications: new OAuthApplicationsApi(request),\n    organizations: new OrganizationAPI(request),\n    phoneNumbers: new PhoneNumberAPI(request),\n    proxyChecks: new ProxyCheckAPI(request),\n    redirectUrls: new RedirectUrlAPI(request),\n    samlConnections: new SamlConnectionAPI(request),\n    sessions: new SessionAPI(request),\n    signInTokens: new SignInTokenAPI(request),\n    signUps: new SignUpAPI(request),\n    testingTokens: new TestingTokenAPI(request),\n    users: new UserAPI(request),\n    waitlistEntries: new WaitlistEntryAPI(request),\n    webhooks: new WebhookAPI(request),\n  };\n}\n", "export const TokenType = {\n  SessionToken: 'session_token',\n  Api<PERSON><PERSON>: 'api_key',\n  MachineToken: 'machine_token',\n  OAuthToken: 'oauth_token',\n} as const;\n\n/**\n * @inline\n */\nexport type TokenType = (typeof TokenType)[keyof typeof TokenType];\n\n/**\n * @inline\n */\nexport type SessionTokenType = typeof TokenType.SessionToken;\n/**\n * @inline\n */\nexport type MachineTokenType = Exclude<TokenType, SessionTokenType>;\n", "import type { AuthenticateRequestOptions } from '../tokens/types';\nimport type { MachineTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\n\nexport const M2M_TOKEN_PREFIX = 'mt_';\nexport const OAUTH_TOKEN_PREFIX = 'oat_';\nexport const API_KEY_PREFIX = 'ak_';\n\nconst MACHINE_TOKEN_PREFIXES = [M2M_TOKEN_PREFIX, OAUTH_TOKEN_PREFIX, API_KEY_PREFIX] as const;\n\n/**\n * Checks if a token is a machine token by looking at its prefix.\n *\n * @remarks\n * In the future, this will support custom prefixes that can be prepended to the base prefixes\n * (e.g. \"org_a_m2m_\", \"org_a_oauth_access_\", \"org_a_api_key_\")\n *\n * @param token - The token string to check\n * @returns true if the token starts with a recognized machine token prefix\n */\nexport function isMachineTokenByPrefix(token: string): boolean {\n  return MACHINE_TOKEN_PREFIXES.some(prefix => token.startsWith(prefix));\n}\n\n/**\n * Gets the specific type of machine token based on its prefix.\n *\n * @remarks\n * In the future, this will support custom prefixes that can be prepended to the base prefixes\n * (e.g. \"org_a_m2m_\", \"org_a_oauth_access_\", \"org_a_api_key_\")\n *\n * @param token - The token string to check\n * @returns The specific MachineTokenType\n * @throws Error if the token doesn't match any known machine token prefix\n */\nexport function getMachineTokenType(token: string): MachineTokenType {\n  if (token.startsWith(M2M_TOKEN_PREFIX)) {\n    return TokenType.MachineToken;\n  }\n\n  if (token.startsWith(OAUTH_TOKEN_PREFIX)) {\n    return TokenType.OAuthToken;\n  }\n\n  if (token.startsWith(API_KEY_PREFIX)) {\n    return TokenType.ApiKey;\n  }\n\n  throw new Error('Unknown machine token type');\n}\n\n/**\n * Check if a token type is accepted given a requested token type or list of token types.\n *\n * @param tokenType - The token type to check (can be null if the token is invalid)\n * @param acceptsToken - The requested token type or list of token types\n * @returns true if the token type is accepted\n */\nexport const isTokenTypeAccepted = (\n  tokenType: TokenType | null,\n  acceptsToken: NonNullable<AuthenticateRequestOptions['acceptsToken']>,\n): boolean => {\n  if (!tokenType) {\n    return false;\n  }\n\n  if (acceptsToken === 'any') {\n    return true;\n  }\n\n  const tokenTypes = Array.isArray(acceptsToken) ? acceptsToken : [acceptsToken];\n  return tokenTypes.includes(tokenType);\n};\n\n/**\n * Checks if a token type string is a machine token type (api_key, machine_token, or oauth_token).\n *\n * @param type - The token type string to check\n * @returns true if the type is a machine token type\n */\nexport function isMachineTokenType(type: string): type is MachineTokenType {\n  return type === TokenType.ApiKey || type === TokenType.MachineToken || type === TokenType.OAuthToken;\n}\n", "import type { JwtPayload, PendingSessionOptions } from '@clerk/types';\n\nimport { constants } from '../constants';\nimport type { TokenVerificationErrorReason } from '../errors';\nimport type { AuthenticateContext } from './authenticateContext';\nimport type {\n  AuthenticatedMachineObject,\n  SignedInAuthObject,\n  SignedOutAuthObject,\n  UnauthenticatedMachineObject,\n} from './authObjects';\nimport {\n  authenticatedMachineObject,\n  signedInAuthObject,\n  signedOutAuthObject,\n  unauthenticatedMachineObject,\n} from './authObjects';\nimport type { MachineTokenType, SessionTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\nimport type { MachineAuthType } from './types';\n\nexport const AuthStatus = {\n  SignedIn: 'signed-in',\n  SignedOut: 'signed-out',\n  Handshake: 'handshake',\n} as const;\n\nexport type AuthStatus = (typeof AuthStatus)[keyof typeof AuthStatus];\n\ntype ToAuth<T extends TokenType, Authenticated extends boolean> = T extends SessionTokenType\n  ? Authenticated extends true\n    ? (opts?: PendingSessionOptions) => SignedInAuthObject\n    : () => SignedOutAuthObject\n  : Authenticated extends true\n    ? () => AuthenticatedMachineObject<Exclude<T, SessionTokenType>>\n    : () => UnauthenticatedMachineObject<Exclude<T, SessionTokenType>>;\n\nexport type AuthenticatedState<T extends TokenType = SessionTokenType> = {\n  status: typeof AuthStatus.SignedIn;\n  reason: null;\n  message: null;\n  proxyUrl?: string;\n  publishableKey: string;\n  isSatellite: boolean;\n  domain: string;\n  signInUrl: string;\n  signUpUrl: string;\n  afterSignInUrl: string;\n  afterSignUpUrl: string;\n  /**\n   * @deprecated Use `isAuthenticated` instead.\n   */\n  isSignedIn: true;\n  isAuthenticated: true;\n  headers: Headers;\n  token: string;\n  tokenType: T;\n  toAuth: ToAuth<T, true>;\n};\n\nexport type UnauthenticatedState<T extends TokenType = SessionTokenType> = {\n  status: typeof AuthStatus.SignedOut;\n  reason: AuthReason;\n  message: string;\n  proxyUrl?: string;\n  publishableKey: string;\n  isSatellite: boolean;\n  domain: string;\n  signInUrl: string;\n  signUpUrl: string;\n  afterSignInUrl: string;\n  afterSignUpUrl: string;\n  /**\n   * @deprecated Use `isAuthenticated` instead.\n   */\n  isSignedIn: false;\n  isAuthenticated: false;\n  tokenType: T;\n  headers: Headers;\n  token: null;\n  toAuth: ToAuth<T, false>;\n};\n\nexport type HandshakeState = Omit<UnauthenticatedState<SessionTokenType>, 'status' | 'toAuth' | 'tokenType'> & {\n  tokenType: SessionTokenType;\n  status: typeof AuthStatus.Handshake;\n  headers: Headers;\n  toAuth: () => null;\n};\n\n/**\n * @deprecated Use AuthenticatedState instead\n */\nexport type SignedInState = AuthenticatedState<SessionTokenType>;\n\n/**\n * @deprecated Use UnauthenticatedState instead\n */\nexport type SignedOutState = UnauthenticatedState<SessionTokenType>;\n\nexport const AuthErrorReason = {\n  ClientUATWithoutSessionToken: 'client-uat-but-no-session-token',\n  DevBrowserMissing: 'dev-browser-missing',\n  DevBrowserSync: 'dev-browser-sync',\n  PrimaryRespondsToSyncing: 'primary-responds-to-syncing',\n  SatelliteCookieNeedsSyncing: 'satellite-needs-syncing',\n  SessionTokenAndUATMissing: 'session-token-and-uat-missing',\n  SessionTokenMissing: 'session-token-missing',\n  SessionTokenExpired: 'session-token-expired',\n  SessionTokenIATBeforeClientUAT: 'session-token-iat-before-client-uat',\n  SessionTokenNBF: 'session-token-nbf',\n  SessionTokenIatInTheFuture: 'session-token-iat-in-the-future',\n  SessionTokenWithoutClientUAT: 'session-token-but-no-client-uat',\n  ActiveOrganizationMismatch: 'active-organization-mismatch',\n  TokenTypeMismatch: 'token-type-mismatch',\n  UnexpectedError: 'unexpected-error',\n} as const;\n\nexport type AuthErrorReason = (typeof AuthErrorReason)[keyof typeof AuthErrorReason];\n\nexport type AuthReason = AuthErrorReason | TokenVerificationErrorReason;\n\nexport type RequestState<T extends TokenType = SessionTokenType> =\n  | AuthenticatedState<T>\n  | UnauthenticatedState<T>\n  | (T extends SessionTokenType ? HandshakeState : never);\n\ntype BaseSignedInParams = {\n  authenticateContext: AuthenticateContext;\n  headers?: Headers;\n  token: string;\n  tokenType: TokenType;\n};\n\ntype SignedInParams =\n  | (BaseSignedInParams & { tokenType: SessionTokenType; sessionClaims: JwtPayload })\n  | (BaseSignedInParams & { tokenType: MachineTokenType; machineData: MachineAuthType });\n\nexport function signedIn<T extends TokenType>(params: SignedInParams & { tokenType: T }): AuthenticatedState<T> {\n  const { authenticateContext, headers = new Headers(), token } = params;\n\n  const toAuth = (({ treatPendingAsSignedOut = true } = {}) => {\n    if (params.tokenType === TokenType.SessionToken) {\n      const { sessionClaims } = params as { sessionClaims: JwtPayload };\n      const authObject = signedInAuthObject(authenticateContext, token, sessionClaims);\n\n      if (treatPendingAsSignedOut && authObject.sessionStatus === 'pending') {\n        return signedOutAuthObject(undefined, authObject.sessionStatus);\n      }\n\n      return authObject;\n    }\n\n    const { machineData } = params as { machineData: MachineAuthType };\n    return authenticatedMachineObject(params.tokenType, token, machineData, authenticateContext);\n  }) as ToAuth<T, true>;\n\n  return {\n    status: AuthStatus.SignedIn,\n    reason: null,\n    message: null,\n    proxyUrl: authenticateContext.proxyUrl || '',\n    publishableKey: authenticateContext.publishableKey || '',\n    isSatellite: authenticateContext.isSatellite || false,\n    domain: authenticateContext.domain || '',\n    signInUrl: authenticateContext.signInUrl || '',\n    signUpUrl: authenticateContext.signUpUrl || '',\n    afterSignInUrl: authenticateContext.afterSignInUrl || '',\n    afterSignUpUrl: authenticateContext.afterSignUpUrl || '',\n    isSignedIn: true,\n    isAuthenticated: true,\n    tokenType: params.tokenType,\n    toAuth,\n    headers,\n    token,\n  };\n}\n\ntype SignedOutParams = Omit<BaseSignedInParams, 'token'> & {\n  reason: AuthReason;\n  message?: string;\n};\n\nexport function signedOut<T extends TokenType>(params: SignedOutParams & { tokenType: T }): UnauthenticatedState<T> {\n  const { authenticateContext, headers = new Headers(), reason, message = '', tokenType } = params;\n\n  const toAuth = (() => {\n    if (tokenType === TokenType.SessionToken) {\n      return signedOutAuthObject({ ...authenticateContext, status: AuthStatus.SignedOut, reason, message });\n    }\n\n    return unauthenticatedMachineObject(tokenType, { reason, message, headers });\n  }) as ToAuth<T, false>;\n\n  return withDebugHeaders({\n    status: AuthStatus.SignedOut,\n    reason,\n    message,\n    proxyUrl: authenticateContext.proxyUrl || '',\n    publishableKey: authenticateContext.publishableKey || '',\n    isSatellite: authenticateContext.isSatellite || false,\n    domain: authenticateContext.domain || '',\n    signInUrl: authenticateContext.signInUrl || '',\n    signUpUrl: authenticateContext.signUpUrl || '',\n    afterSignInUrl: authenticateContext.afterSignInUrl || '',\n    afterSignUpUrl: authenticateContext.afterSignUpUrl || '',\n    isSignedIn: false,\n    isAuthenticated: false,\n    tokenType,\n    toAuth,\n    headers,\n    token: null,\n  });\n}\n\nexport function handshake(\n  authenticateContext: AuthenticateContext,\n  reason: AuthReason,\n  message = '',\n  headers: Headers,\n): HandshakeState {\n  return withDebugHeaders({\n    status: AuthStatus.Handshake,\n    reason,\n    message,\n    publishableKey: authenticateContext.publishableKey || '',\n    isSatellite: authenticateContext.isSatellite || false,\n    domain: authenticateContext.domain || '',\n    proxyUrl: authenticateContext.proxyUrl || '',\n    signInUrl: authenticateContext.signInUrl || '',\n    signUpUrl: authenticateContext.signUpUrl || '',\n    afterSignInUrl: authenticateContext.afterSignInUrl || '',\n    afterSignUpUrl: authenticateContext.afterSignUpUrl || '',\n    isSignedIn: false,\n    isAuthenticated: false,\n    tokenType: TokenType.SessionToken,\n    toAuth: () => null,\n    headers,\n    token: null,\n  });\n}\n\nconst withDebugHeaders = <T extends { headers: Headers; message?: string; reason?: AuthReason; status?: AuthStatus }>(\n  requestState: T,\n): T => {\n  const headers = new Headers(requestState.headers || {});\n\n  if (requestState.message) {\n    try {\n      headers.set(constants.Headers.AuthMessage, requestState.message);\n    } catch {\n      // headers.set can throw if unicode strings are passed to it. In this case, simply do nothing\n    }\n  }\n\n  if (requestState.reason) {\n    try {\n      headers.set(constants.Headers.AuthReason, requestState.reason);\n    } catch {\n      /* empty */\n    }\n  }\n\n  if (requestState.status) {\n    try {\n      headers.set(constants.Headers.AuthStatus, requestState.status);\n    } catch {\n      /* empty */\n    }\n  }\n\n  requestState.headers = headers;\n\n  return requestState;\n};\n", "import { parse } from 'cookie';\n\nimport { constants } from '../constants';\nimport type { ClerkUrl } from './clerkUrl';\nimport { createClerkUrl } from './clerkUrl';\n\n/**\n * A class that extends the native Request class,\n * adds cookies helpers and a normalised clerkUrl that is constructed by using the values found\n * in req.headers so it is able to work reliably when the app is running behind a proxy server.\n */\nclass ClerkRequest extends Request {\n  readonly clerkUrl: ClerkUrl;\n  readonly cookies: Map<string, string | undefined>;\n\n  public constructor(input: ClerkRequest | Request | RequestInfo, init?: RequestInit) {\n    // The usual way to duplicate a request object is to\n    // pass the original request object to the Request constructor\n    // both as the `input` and `init` parameters, eg: super(req, req)\n    // However, this fails in certain environments like Vercel Edge Runtime\n    // when a framework like Remix polyfills the global Request object.\n    // This happens because `undici` performs the following instanceof check\n    // which, instead of testing against the global Request object, tests against\n    // the Request class defined in the same file (local Request class).\n    // For more details, please refer to:\n    // https://github.com/nodejs/undici/issues/2155\n    // https://github.com/nodejs/undici/blob/7153a1c78d51840bbe16576ce353e481c3934701/lib/fetch/request.js#L854\n    const url = typeof input !== 'string' && 'url' in input ? input.url : String(input);\n    super(url, init || typeof input === 'string' ? undefined : input);\n    this.clerkUrl = this.deriveUrlFromHeaders(this);\n    this.cookies = this.parseCookies(this);\n  }\n\n  public toJSON() {\n    return {\n      url: this.clerkUrl.href,\n      method: this.method,\n      headers: JSON.stringify(Object.fromEntries(this.headers)),\n      clerkUrl: this.clerkUrl.toString(),\n      cookies: JSON.stringify(Object.fromEntries(this.cookies)),\n    };\n  }\n\n  /**\n   * Used to fix request.url using the x-forwarded-* headers\n   * TODO add detailed description of the issues this solves\n   */\n  private deriveUrlFromHeaders(req: Request) {\n    const initialUrl = new URL(req.url);\n    const forwardedProto = req.headers.get(constants.Headers.ForwardedProto);\n    const forwardedHost = req.headers.get(constants.Headers.ForwardedHost);\n    const host = req.headers.get(constants.Headers.Host);\n    const protocol = initialUrl.protocol;\n\n    const resolvedHost = this.getFirstValueFromHeader(forwardedHost) ?? host;\n    const resolvedProtocol = this.getFirstValueFromHeader(forwardedProto) ?? protocol?.replace(/[:/]/, '');\n    const origin = resolvedHost && resolvedProtocol ? `${resolvedProtocol}://${resolvedHost}` : initialUrl.origin;\n\n    if (origin === initialUrl.origin) {\n      return createClerkUrl(initialUrl);\n    }\n    return createClerkUrl(initialUrl.pathname + initialUrl.search, origin);\n  }\n\n  private getFirstValueFromHeader(value?: string | null) {\n    return value?.split(',')[0];\n  }\n\n  private parseCookies(req: Request) {\n    const cookiesRecord = parse(this.decodeCookieValue(req.headers.get('cookie') || ''));\n    return new Map(Object.entries(cookiesRecord));\n  }\n\n  private decodeCookieValue(str: string) {\n    return str ? str.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent) : str;\n  }\n}\n\nexport const createClerkRequest = (...args: ConstructorParameters<typeof ClerkRequest>): ClerkRequest => {\n  return args[0] instanceof ClerkRequest ? args[0] : new ClerkRequest(...args);\n};\n\nexport type { ClerkRequest };\n", "class ClerkUrl extends URL {\n  public isCrossOrigin(other: URL | string) {\n    return this.origin !== new URL(other.toString()).origin;\n  }\n}\n\nexport type WithClerkUrl<T> = T & {\n  /**\n   * When a NextJs app is hosted on a platform different from Vercel\n   * or inside a container (Netlify, Fly.io, AWS Amplify, docker etc),\n   * req.url is always set to `localhost:3000` instead of the actual host of the app.\n   *\n   * The `authMiddleware` uses the value of the available req.headers in order to construct\n   * and use the correct url internally. This url is then exposed as `experimental_clerkUrl`,\n   * intended to be used within `beforeAuth` and `afterAuth` if needed.\n   */\n  clerkUrl: ClerkUrl;\n};\n\nexport const createClerkUrl = (...args: ConstructorParameters<typeof ClerkUrl>): ClerkUrl => {\n  return new ClerkUrl(...args);\n};\n\nexport type { ClerkUrl };\n", "export const getCookieName = (cookieDirective: string): string => {\n  return cookieDirective.split(';')[0]?.split('=')[0];\n};\n\nexport const getCookieValue = (cookieDirective: string): string => {\n  return cookieDirective.split(';')[0]?.split('=')[1];\n};\n", "import {\n  API_URL,\n  API_VERSION,\n  MAX_CACHE_LAST_UPDATED_AT_SECONDS,\n  SUPPORTED_BAPI_VERSION,\n  USER_AGENT,\n} from '../constants';\nimport {\n  TokenVerificationError,\n  TokenVerificationErrorAction,\n  TokenVerificationErrorCode,\n  TokenVerificationErrorReason,\n} from '../errors';\nimport { runtime } from '../runtime';\nimport { joinPaths } from '../util/path';\nimport { retry } from '../util/shared';\n\ntype JsonWebKeyWithKid = JsonWebKey & { kid: string };\n\ntype JsonWebKeyCache = Record<string, JsonWebKeyWithKid>;\n\nlet cache: JsonWebKeyCache = {};\nlet lastUpdatedAt = 0;\n\nfunction getFromCache(kid: string) {\n  return cache[kid];\n}\n\nfunction getCacheValues() {\n  return Object.values(cache);\n}\n\nfunction setInCache(jwk: JsonWebKeyWithKid, shouldExpire = true) {\n  cache[jwk.kid] = jwk;\n  lastUpdatedAt = shouldExpire ? Date.now() : -1;\n}\n\nconst LocalJwkKid = 'local';\nconst PEM_HEADER = '-----BEGIN PUBLIC KEY-----';\nconst PEM_TRAILER = '-----END PUBLIC KEY-----';\nconst RSA_PREFIX = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA';\nconst RSA_SUFFIX = 'IDAQAB';\n\n/**\n *\n * Loads a local PEM key usually from process.env and transform it to JsonWebKey format.\n * The result is also cached on the module level to avoid unnecessary computations in subsequent invocations.\n *\n * @param {string} localKey\n * @returns {JsonWebKey} key\n */\nexport function loadClerkJWKFromLocal(localKey?: string): JsonWebKey {\n  if (!getFromCache(LocalJwkKid)) {\n    if (!localKey) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.SetClerkJWTKey,\n        message: 'Missing local JWK.',\n        reason: TokenVerificationErrorReason.LocalJWKMissing,\n      });\n    }\n\n    const modulus = localKey\n      .replace(/\\r\\n|\\n|\\r/g, '')\n      .replace(PEM_HEADER, '')\n      .replace(PEM_TRAILER, '')\n      .replace(RSA_PREFIX, '')\n      .replace(RSA_SUFFIX, '')\n      .replace(/\\+/g, '-')\n      .replace(/\\//g, '_');\n\n    // JWK https://datatracker.ietf.org/doc/html/rfc7517\n    setInCache(\n      {\n        kid: 'local',\n        kty: 'RSA',\n        alg: 'RS256',\n        n: modulus,\n        e: 'AQAB',\n      },\n      false, // local key never expires in cache\n    );\n  }\n\n  return getFromCache(LocalJwkKid);\n}\n\n/**\n * @internal\n */\nexport type LoadClerkJWKFromRemoteOptions = {\n  /**\n   * @internal\n   */\n  kid: string;\n  /**\n   * @deprecated This cache TTL will be removed in the next major version. Specifying a cache TTL is a no-op.\n   */\n  jwksCacheTtlInMs?: number;\n  /**\n   * A flag to ignore the JWKS cache and always fetch JWKS before each JWT verification.\n   */\n  skipJwksCache?: boolean;\n  /**\n   * The Clerk Secret Key from the [**API keys**](https://dashboard.clerk.com/last-active?path=api-keys) page in the Clerk Dashboard.\n   */\n  secretKey?: string;\n  /**\n   * The [Clerk Backend API](https://clerk.com/docs/reference/backend-api){{ target: '_blank' }} endpoint.\n   * @default 'https://api.clerk.com'\n   */\n  apiUrl?: string;\n  /**\n   * The version passed to the Clerk API.\n   * @default 'v1'\n   */\n  apiVersion?: string;\n};\n\n/**\n *\n * Loads a key from JWKS retrieved from the well-known Frontend API endpoint of the issuer.\n * The result is also cached on the module level to avoid network requests in subsequent invocations.\n * The cache lasts up to 5 minutes.\n *\n * @param {Object} options\n * @param {string} options.kid - The id of the key that the JWT was signed with\n * @param {string} options.alg - The algorithm of the JWT\n * @returns {JsonWebKey} key\n */\nexport async function loadClerkJWKFromRemote({\n  secretKey,\n  apiUrl = API_URL,\n  apiVersion = API_VERSION,\n  kid,\n  skipJwksCache,\n}: LoadClerkJWKFromRemoteOptions): Promise<JsonWebKey> {\n  if (skipJwksCache || cacheHasExpired() || !getFromCache(kid)) {\n    if (!secretKey) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: 'Failed to load JWKS from Clerk Backend or Frontend API.',\n        reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n      });\n    }\n    const fetcher = () => fetchJWKSFromBAPI(apiUrl, secretKey, apiVersion) as Promise<{ keys: JsonWebKeyWithKid[] }>;\n    const { keys } = await retry(fetcher);\n\n    if (!keys || !keys.length) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: 'The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.',\n        reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n      });\n    }\n\n    keys.forEach(key => setInCache(key));\n  }\n\n  const jwk = getFromCache(kid);\n\n  if (!jwk) {\n    const cacheValues = getCacheValues();\n    const jwkKeys = cacheValues\n      .map(jwk => jwk.kid)\n      .sort()\n      .join(', ');\n\n    throw new TokenVerificationError({\n      action: `Go to your Dashboard and validate your secret and public keys are correct. ${TokenVerificationErrorAction.ContactSupport} if the issue persists.`,\n      message: `Unable to find a signing key in JWKS that matches the kid='${kid}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${jwkKeys}`,\n      reason: TokenVerificationErrorReason.JWKKidMismatch,\n    });\n  }\n\n  return jwk;\n}\n\nasync function fetchJWKSFromBAPI(apiUrl: string, key: string, apiVersion: string) {\n  if (!key) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.SetClerkSecretKey,\n      message:\n        'Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.',\n      reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n    });\n  }\n\n  const url = new URL(apiUrl);\n  url.pathname = joinPaths(url.pathname, apiVersion, '/jwks');\n\n  const response = await runtime.fetch(url.href, {\n    headers: {\n      Authorization: `Bearer ${key}`,\n      'Clerk-API-Version': SUPPORTED_BAPI_VERSION,\n      'Content-Type': 'application/json',\n      'User-Agent': USER_AGENT,\n    },\n  });\n\n  if (!response.ok) {\n    const json = await response.json();\n    const invalidSecretKeyError = getErrorObjectByCode(json?.errors, TokenVerificationErrorCode.InvalidSecretKey);\n\n    if (invalidSecretKeyError) {\n      const reason = TokenVerificationErrorReason.InvalidSecretKey;\n\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: invalidSecretKeyError.message,\n        reason,\n      });\n    }\n\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.ContactSupport,\n      message: `Error loading Clerk JWKS from ${url.href} with code=${response.status}`,\n      reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n    });\n  }\n\n  return response.json();\n}\n\nfunction cacheHasExpired() {\n  // If lastUpdatedAt is -1, it means that we're using a local JWKS and it never expires\n  if (lastUpdatedAt === -1) {\n    return false;\n  }\n\n  // If the cache has expired, clear the value so we don't attempt to make decisions based on stale data\n  const isExpired = Date.now() - lastUpdatedAt >= MAX_CACHE_LAST_UPDATED_AT_SECONDS * 1000;\n\n  if (isExpired) {\n    cache = {};\n  }\n\n  return isExpired;\n}\n\ntype ErrorFields = {\n  message: string;\n  long_message: string;\n  code: string;\n};\n\nconst getErrorObjectByCode = (errors: ErrorFields[], code: string) => {\n  if (!errors) {\n    return null;\n  }\n\n  return errors.find((err: ErrorFields) => err.code === code);\n};\n", "import { isClerkAPIResponseError } from '@clerk/shared/error';\nimport type { JwtPayload } from '@clerk/types';\n\nimport type { <PERSON>Key, IdPOAuthAccessToken, MachineToken } from '../api';\nimport { createBackendApiClient } from '../api/factory';\nimport {\n  MachineTokenVerificationError,\n  MachineTokenVerificationErrorCode,\n  TokenVerificationError,\n  TokenVerificationErrorAction,\n  TokenVerificationErrorReason,\n} from '../errors';\nimport type { VerifyJwtOptions } from '../jwt';\nimport type { JwtReturnType, MachineTokenReturnType } from '../jwt/types';\nimport { decodeJwt, verifyJwt } from '../jwt/verifyJwt';\nimport type { LoadClerkJWKFromRemoteOptions } from './keys';\nimport { loadClerkJWKFromLocal, loadClerkJWKFromRemote } from './keys';\nimport { API_KEY_PREFIX, M2M_TOKEN_PREFIX, OAUTH_TOKEN_PREFIX } from './machine';\nimport type { MachineTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\n\n/**\n * @interface\n */\nexport type VerifyTokenOptions = Omit<VerifyJwtOptions, 'key'> &\n  Omit<LoadClerkJWKFromRemoteOptions, 'kid'> & {\n    /**\n     * Used to verify the session token in a networkless manner. Supply the PEM public key from the **[**API keys**](https://dashboard.clerk.com/last-active?path=api-keys) page -> Show JWT public key -> PEM Public Key** section in the Clerk Dashboard. **It's recommended to use [the environment variable](https://clerk.com/docs/deployments/clerk-environment-variables) instead.** For more information, refer to [Manual JWT verification](https://clerk.com/docs/backend-requests/manual-jwt).\n     */\n    jwtKey?: string;\n  };\n\n/**\n * > [!WARNING]\n * > This is a lower-level method intended for more advanced use-cases. It's recommended to use [`authenticateRequest()`](https://clerk.com/docs/references/backend/authenticate-request), which fully authenticates a token passed from the `request` object.\n *\n * Verifies a Clerk-generated token signature. Networkless if the `jwtKey` is provided. Otherwise, performs a network call to retrieve the JWKS from the [Backend API](https://clerk.com/docs/reference/backend-api/tag/JWKS#operation/GetJWKS){{ target: '_blank' }}.\n *\n * @param token - The token to verify.\n * @param options - Options for verifying the token.\n *\n * @displayFunctionSignature\n *\n * @paramExtension\n *\n * ### `VerifyTokenOptions`\n *\n * It is recommended to set these options as [environment variables](/docs/deployments/clerk-environment-variables#api-and-sdk-configuration) where possible, and then pass them to the function. For example, you can set the `secretKey` option using the `CLERK_SECRET_KEY` environment variable, and then pass it to the function like this: `createClerkClient({ secretKey: process.env.CLERK_SECRET_KEY })`.\n *\n * > [!WARNING]\n * You must provide either `jwtKey` or `secretKey`.\n *\n * <Typedoc src=\"backend/verify-token-options\" />\n *\n * @example\n *\n * The following example demonstrates how to use the [JavaScript Backend SDK](https://clerk.com/docs/references/backend/overview) to verify the token signature.\n *\n * In the following example:\n *\n * 1. The **JWKS Public Key** from the Clerk Dashboard is set in the environment variable `CLERK_JWT_KEY`.\n * 1. The session token is retrieved from the `__session` cookie or the Authorization header.\n * 1. The token is verified in a networkless manner by passing the `jwtKey` prop.\n * 1. The `authorizedParties` prop is passed to verify that the session token is generated from the expected frontend application.\n * 1. If the token is valid, the response contains the verified token.\n *\n * ```ts\n * import { verifyToken } from '@clerk/backend'\n * import { cookies } from 'next/headers'\n *\n * export async function GET(request: Request) {\n *   const cookieStore = cookies()\n *   const sessToken = cookieStore.get('__session')?.value\n *   const bearerToken = request.headers.get('Authorization')?.replace('Bearer ', '')\n *   const token = sessToken || bearerToken\n *\n *   if (!token) {\n *     return Response.json({ error: 'Token not found. User must sign in.' }, { status: 401 })\n *   }\n *\n *   try {\n *     const verifiedToken = await verifyToken(token, {\n *       jwtKey: process.env.CLERK_JWT_KEY,\n *       authorizedParties: ['http://localhost:3001', 'api.example.com'], // Replace with your authorized parties\n *     })\n *\n *     return Response.json({ verifiedToken })\n *   } catch (error) {\n *     return Response.json({ error: 'Token not verified.' }, { status: 401 })\n *   }\n * }\n * ```\n *\n * If the token is valid, the response will contain a JSON object that looks something like this:\n *\n * ```json\n * {\n *   \"verifiedToken\": {\n *     \"azp\": \"http://localhost:3000\",\n *     \"exp\": **********,\n *     \"iat\": **********,\n *     \"iss\": \"https://magical-marmoset-51.clerk.accounts.dev\",\n *     \"nbf\": **********,\n *     \"sid\": \"sess_2Ro7e2IxrffdqBboq8KfB6eGbIy\",\n *     \"sub\": \"user_2RfWKJREkjKbHZy0Wqa5qrHeAnb\"\n *   }\n * }\n * ```\n */\nexport async function verifyToken(\n  token: string,\n  options: VerifyTokenOptions,\n): Promise<JwtReturnType<JwtPayload, TokenVerificationError>> {\n  const { data: decodedResult, errors } = decodeJwt(token);\n  if (errors) {\n    return { errors };\n  }\n\n  const { header } = decodedResult;\n  const { kid } = header;\n\n  try {\n    let key;\n\n    if (options.jwtKey) {\n      key = loadClerkJWKFromLocal(options.jwtKey);\n    } else if (options.secretKey) {\n      // Fetch JWKS from Backend API using the key\n      key = await loadClerkJWKFromRemote({ ...options, kid });\n    } else {\n      return {\n        errors: [\n          new TokenVerificationError({\n            action: TokenVerificationErrorAction.SetClerkJWTKey,\n            message: 'Failed to resolve JWK during verification.',\n            reason: TokenVerificationErrorReason.JWKFailedToResolve,\n          }),\n        ],\n      };\n    }\n\n    return await verifyJwt(token, { ...options, key });\n  } catch (error) {\n    return { errors: [error as TokenVerificationError] };\n  }\n}\n\n/**\n * Handles errors from Clerk API responses for machine tokens\n * @param tokenType - The type of machine token\n * @param err - The error from the Clerk API\n * @param notFoundMessage - Custom message for 404 errors\n */\nfunction handleClerkAPIError(\n  tokenType: MachineTokenType,\n  err: any,\n  notFoundMessage: string,\n): MachineTokenReturnType<any, MachineTokenVerificationError> {\n  if (isClerkAPIResponseError(err)) {\n    let code: MachineTokenVerificationErrorCode;\n    let message: string;\n\n    switch (err.status) {\n      case 401:\n        code = MachineTokenVerificationErrorCode.InvalidSecretKey;\n        message = err.errors[0]?.message || 'Invalid secret key';\n        break;\n      case 404:\n        code = MachineTokenVerificationErrorCode.TokenInvalid;\n        message = notFoundMessage;\n        break;\n      default:\n        code = MachineTokenVerificationErrorCode.UnexpectedError;\n        message = 'Unexpected error';\n    }\n\n    return {\n      data: undefined,\n      tokenType,\n      errors: [\n        new MachineTokenVerificationError({\n          message,\n          code,\n          status: err.status,\n        }),\n      ],\n    };\n  }\n\n  return {\n    data: undefined,\n    tokenType,\n    errors: [\n      new MachineTokenVerificationError({\n        message: 'Unexpected error',\n        code: MachineTokenVerificationErrorCode.UnexpectedError,\n        status: err.status,\n      }),\n    ],\n  };\n}\n\nasync function verifyMachineToken(\n  secret: string,\n  options: VerifyTokenOptions,\n): Promise<MachineTokenReturnType<MachineToken, MachineTokenVerificationError>> {\n  try {\n    const client = createBackendApiClient(options);\n    const verifiedToken = await client.machineTokens.verifySecret(secret);\n    return { data: verifiedToken, tokenType: TokenType.MachineToken, errors: undefined };\n  } catch (err: any) {\n    return handleClerkAPIError(TokenType.MachineToken, err, 'Machine token not found');\n  }\n}\n\nasync function verifyOAuthToken(\n  accessToken: string,\n  options: VerifyTokenOptions,\n): Promise<MachineTokenReturnType<IdPOAuthAccessToken, MachineTokenVerificationError>> {\n  try {\n    const client = createBackendApiClient(options);\n    const verifiedToken = await client.idPOAuthAccessToken.verifyAccessToken(accessToken);\n    return { data: verifiedToken, tokenType: TokenType.OAuthToken, errors: undefined };\n  } catch (err: any) {\n    return handleClerkAPIError(TokenType.OAuthToken, err, 'OAuth token not found');\n  }\n}\n\nasync function verifyAPIKey(\n  secret: string,\n  options: VerifyTokenOptions,\n): Promise<MachineTokenReturnType<APIKey, MachineTokenVerificationError>> {\n  try {\n    const client = createBackendApiClient(options);\n    const verifiedToken = await client.apiKeys.verifySecret(secret);\n    return { data: verifiedToken, tokenType: TokenType.ApiKey, errors: undefined };\n  } catch (err: any) {\n    return handleClerkAPIError(TokenType.ApiKey, err, 'API key not found');\n  }\n}\n\n/**\n * Verifies any type of machine token by detecting its type from the prefix.\n *\n * @param token - The token to verify (e.g. starts with \"m2m_\", \"oauth_\", \"api_key_\", etc.)\n * @param options - Options including secretKey for BAPI authorization\n */\nexport async function verifyMachineAuthToken(token: string, options: VerifyTokenOptions) {\n  if (token.startsWith(M2M_TOKEN_PREFIX)) {\n    return verifyMachineToken(token, options);\n  }\n  if (token.startsWith(OAUTH_TOKEN_PREFIX)) {\n    return verifyOAuthToken(token, options);\n  }\n  if (token.startsWith(API_KEY_PREFIX)) {\n    return verifyAPIKey(token, options);\n  }\n\n  throw new Error('Unknown machine token type');\n}\n", "import { constants, SUPPORTED_BAPI_VERSION } from '../constants';\nimport { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from '../errors';\nimport type { VerifyJwtOptions } from '../jwt';\nimport { assertHeaderAlgorithm, assertHeaderType } from '../jwt/assertions';\nimport { decodeJwt, hasValidSignature } from '../jwt/verifyJwt';\nimport type { AuthenticateContext } from './authenticateContext';\nimport type { SignedInState, SignedOutState } from './authStatus';\nimport { AuthErrorReason, signedIn, signedOut } from './authStatus';\nimport { getCookieName, getCookieValue } from './cookie';\nimport { loadClerkJWKFromLocal, loadClerkJWKFromRemote } from './keys';\nimport type { OrganizationMatcher } from './organizationMatcher';\nimport { TokenType } from './tokenTypes';\nimport type { OrganizationSyncOptions, OrganizationSyncTarget } from './types';\nimport type { VerifyTokenOptions } from './verify';\nimport { verifyToken } from './verify';\n\nasync function verifyHandshakeJwt(token: string, { key }: VerifyJwtOptions): Promise<{ handshake: string[] }> {\n  const { data: decoded, errors } = decodeJwt(token);\n  if (errors) {\n    throw errors[0];\n  }\n\n  const { header, payload } = decoded;\n\n  // Header verifications\n  const { typ, alg } = header;\n\n  assertHeaderType(typ);\n  assertHeaderAlgorithm(alg);\n\n  const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);\n  if (signatureErrors) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Error verifying handshake token. ${signatureErrors[0]}`,\n    });\n  }\n\n  if (!signatureValid) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidSignature,\n      message: 'Handshake signature is invalid.',\n    });\n  }\n\n  return payload as unknown as { handshake: string[] };\n}\n\n/**\n * Similar to our verifyToken flow for Clerk-issued JWTs, but this verification flow is for our signed handshake payload.\n * The handshake payload requires fewer verification steps.\n */\nexport async function verifyHandshakeToken(\n  token: string,\n  options: VerifyTokenOptions,\n): Promise<{ handshake: string[] }> {\n  const { secretKey, apiUrl, apiVersion, jwksCacheTtlInMs, jwtKey, skipJwksCache } = options;\n\n  const { data, errors } = decodeJwt(token);\n  if (errors) {\n    throw errors[0];\n  }\n\n  const { kid } = data.header;\n\n  let key;\n\n  if (jwtKey) {\n    key = loadClerkJWKFromLocal(jwtKey);\n  } else if (secretKey) {\n    // Fetch JWKS from Backend API using the key\n    key = await loadClerkJWKFromRemote({ secretKey, apiUrl, apiVersion, kid, jwksCacheTtlInMs, skipJwksCache });\n  } else {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.SetClerkJWTKey,\n      message: 'Failed to resolve JWK during handshake verification.',\n      reason: TokenVerificationErrorReason.JWKFailedToResolve,\n    });\n  }\n\n  return await verifyHandshakeJwt(token, {\n    key,\n  });\n}\n\nexport class HandshakeService {\n  private readonly authenticateContext: AuthenticateContext;\n  private readonly organizationMatcher: OrganizationMatcher;\n  private readonly options: { organizationSyncOptions?: OrganizationSyncOptions };\n\n  constructor(\n    authenticateContext: AuthenticateContext,\n    options: { organizationSyncOptions?: OrganizationSyncOptions },\n    organizationMatcher: OrganizationMatcher,\n  ) {\n    this.authenticateContext = authenticateContext;\n    this.options = options;\n    this.organizationMatcher = organizationMatcher;\n  }\n\n  /**\n   * Determines if a request is eligible for handshake based on its headers\n   *\n   * Currently, a request is only eligible for a handshake if we can say it's *probably* a request for a document, not a fetch or some other exotic request.\n   * This heuristic should give us a reliable enough signal for browsers that support `Sec-Fetch-Dest` and for those that don't.\n   *\n   * @returns boolean indicating if the request is eligible for handshake\n   */\n  isRequestEligibleForHandshake(): boolean {\n    const { accept, secFetchDest } = this.authenticateContext;\n\n    // NOTE: we could also check sec-fetch-mode === navigate here, but according to the spec, sec-fetch-dest: document should indicate that the request is the data of a user navigation.\n    // Also, we check for 'iframe' because it's the value set when a doc request is made by an iframe.\n    if (secFetchDest === 'document' || secFetchDest === 'iframe') {\n      return true;\n    }\n\n    if (!secFetchDest && accept?.startsWith('text/html')) {\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Builds the redirect headers for a handshake request\n   * @param reason - The reason for the handshake (e.g. 'session-token-expired')\n   * @returns Headers object containing the Location header for redirect\n   * @throws Error if clerkUrl is missing in authenticateContext\n   */\n  buildRedirectToHandshake(reason: string): Headers {\n    if (!this.authenticateContext?.clerkUrl) {\n      throw new Error('Missing clerkUrl in authenticateContext');\n    }\n\n    const redirectUrl = this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl);\n    const frontendApiNoProtocol = this.authenticateContext.frontendApi.replace(/http(s)?:\\/\\//, '');\n\n    const baseUrl = this.authenticateContext.proxyUrl\n      ? this.authenticateContext.proxyUrl.replace(/\\/$/, '')\n      : `https://${frontendApiNoProtocol}`;\n\n    const url = new URL(`${baseUrl}/v1/client/handshake`);\n    url.searchParams.append('redirect_url', redirectUrl?.href || '');\n    url.searchParams.append('__clerk_api_version', SUPPORTED_BAPI_VERSION);\n    url.searchParams.append(\n      constants.QueryParameters.SuffixedCookies,\n      this.authenticateContext.usesSuffixedCookies().toString(),\n    );\n    url.searchParams.append(constants.QueryParameters.HandshakeReason, reason);\n\n    if (this.authenticateContext.instanceType === 'development' && this.authenticateContext.devBrowserToken) {\n      url.searchParams.append(constants.QueryParameters.DevBrowser, this.authenticateContext.devBrowserToken);\n    }\n\n    const toActivate = this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl, this.organizationMatcher);\n    if (toActivate) {\n      const params = this.getOrganizationSyncQueryParams(toActivate);\n      params.forEach((value, key) => {\n        url.searchParams.append(key, value);\n      });\n    }\n\n    return new Headers({ [constants.Headers.Location]: url.href });\n  }\n\n  /**\n   * Gets cookies from either a handshake nonce or a handshake token\n   * @returns Promise resolving to string array of cookie directives\n   */\n  public async getCookiesFromHandshake(): Promise<string[]> {\n    const cookiesToSet: string[] = [];\n\n    if (this.authenticateContext.handshakeNonce) {\n      try {\n        const handshakePayload = await this.authenticateContext.apiClient?.clients.getHandshakePayload({\n          nonce: this.authenticateContext.handshakeNonce,\n        });\n        if (handshakePayload) {\n          cookiesToSet.push(...handshakePayload.directives);\n        }\n      } catch (error) {\n        console.error('Clerk: HandshakeService: error getting handshake payload:', error);\n      }\n    } else if (this.authenticateContext.handshakeToken) {\n      const handshakePayload = await verifyHandshakeToken(\n        this.authenticateContext.handshakeToken,\n        this.authenticateContext,\n      );\n      if (handshakePayload && Array.isArray(handshakePayload.handshake)) {\n        cookiesToSet.push(...handshakePayload.handshake);\n      }\n    }\n\n    return cookiesToSet;\n  }\n\n  /**\n   * Resolves a handshake request by verifying the handshake token and setting appropriate cookies\n   * @returns Promise resolving to either a SignedInState or SignedOutState\n   * @throws Error if handshake verification fails or if there are issues with the session token\n   */\n  async resolveHandshake(): Promise<SignedInState | SignedOutState> {\n    const headers = new Headers({\n      'Access-Control-Allow-Origin': 'null',\n      'Access-Control-Allow-Credentials': 'true',\n    });\n\n    const cookiesToSet = await this.getCookiesFromHandshake();\n\n    let sessionToken = '';\n    cookiesToSet.forEach((x: string) => {\n      headers.append('Set-Cookie', x);\n      if (getCookieName(x).startsWith(constants.Cookies.Session)) {\n        sessionToken = getCookieValue(x);\n      }\n    });\n\n    if (this.authenticateContext.instanceType === 'development') {\n      const newUrl = new URL(this.authenticateContext.clerkUrl);\n      newUrl.searchParams.delete(constants.QueryParameters.Handshake);\n      newUrl.searchParams.delete(constants.QueryParameters.HandshakeHelp);\n      headers.append(constants.Headers.Location, newUrl.toString());\n      headers.set(constants.Headers.CacheControl, 'no-store');\n    }\n\n    if (sessionToken === '') {\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext: this.authenticateContext,\n        reason: AuthErrorReason.SessionTokenMissing,\n        message: '',\n        headers,\n      });\n    }\n\n    const { data, errors: [error] = [] } = await verifyToken(sessionToken, this.authenticateContext);\n    if (data) {\n      return signedIn({\n        tokenType: TokenType.SessionToken,\n        authenticateContext: this.authenticateContext,\n        sessionClaims: data,\n        headers,\n        token: sessionToken,\n      });\n    }\n\n    if (\n      this.authenticateContext.instanceType === 'development' &&\n      (error?.reason === TokenVerificationErrorReason.TokenExpired ||\n        error?.reason === TokenVerificationErrorReason.TokenNotActiveYet ||\n        error?.reason === TokenVerificationErrorReason.TokenIatInTheFuture)\n    ) {\n      // Create a new error object with the same properties\n      const developmentError = new TokenVerificationError({\n        action: error.action,\n        message: error.message,\n        reason: error.reason,\n      });\n      // Set the tokenCarrier after construction\n      developmentError.tokenCarrier = 'cookie';\n\n      console.error(\n        `Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.\n\nTo resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).\n\n---\n\n${developmentError.getFullMessage()}`,\n      );\n\n      const { data: retryResult, errors: [retryError] = [] } = await verifyToken(sessionToken, {\n        ...this.authenticateContext,\n        clockSkewInMs: 86_400_000,\n      });\n      if (retryResult) {\n        return signedIn({\n          tokenType: TokenType.SessionToken,\n          authenticateContext: this.authenticateContext,\n          sessionClaims: retryResult,\n          headers,\n          token: sessionToken,\n        });\n      }\n\n      throw new Error(retryError?.message || 'Clerk: Handshake retry failed.');\n    }\n\n    throw new Error(error?.message || 'Clerk: Handshake failed.');\n  }\n\n  /**\n   * Handles handshake token verification errors in development mode\n   * @param error - The TokenVerificationError that occurred\n   * @throws Error with a descriptive message about the verification failure\n   */\n  handleTokenVerificationErrorInDevelopment(error: TokenVerificationError): void {\n    // In development, the handshake token is being transferred in the URL as a query parameter, so there is no\n    // possibility of collision with a handshake token of another app running on the same local domain\n    // (etc one app on localhost:3000 and one on localhost:3001).\n    // Therefore, if the handshake token is invalid, it is likely that the user has switched Clerk keys locally.\n    // We make sure to throw a descriptive error message and then stop the handshake flow in every case,\n    // to avoid the possibility of an infinite loop.\n    if (error.reason === TokenVerificationErrorReason.TokenInvalidSignature) {\n      const msg = `Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.`;\n      throw new Error(msg);\n    }\n    throw new Error(`Clerk: Handshake token verification failed: ${error.getFullMessage()}.`);\n  }\n\n  /**\n   * Checks if a redirect loop is detected and sets headers to track redirect count\n   * @param headers - The Headers object to modify\n   * @returns boolean indicating if a redirect loop was detected (true) or if the request can proceed (false)\n   */\n  checkAndTrackRedirectLoop(headers: Headers): boolean {\n    if (this.authenticateContext.handshakeRedirectLoopCounter === 3) {\n      return true;\n    }\n\n    const newCounterValue = this.authenticateContext.handshakeRedirectLoopCounter + 1;\n    const cookieName = constants.Cookies.RedirectCount;\n    headers.append('Set-Cookie', `${cookieName}=${newCounterValue}; SameSite=Lax; HttpOnly; Max-Age=3`);\n    return false;\n  }\n\n  private removeDevBrowserFromURL(url: URL): URL {\n    const updatedURL = new URL(url);\n    updatedURL.searchParams.delete(constants.QueryParameters.DevBrowser);\n    updatedURL.searchParams.delete(constants.QueryParameters.LegacyDevBrowser);\n    return updatedURL;\n  }\n\n  private getOrganizationSyncTarget(url: URL, matchers: OrganizationMatcher): OrganizationSyncTarget | null {\n    return matchers.findTarget(url);\n  }\n\n  private getOrganizationSyncQueryParams(toActivate: OrganizationSyncTarget): Map<string, string> {\n    const ret = new Map();\n    if (toActivate.type === 'personalAccount') {\n      ret.set('organization_id', '');\n    }\n    if (toActivate.type === 'organization') {\n      if (toActivate.organizationId) {\n        ret.set('organization_id', toActivate.organizationId);\n      }\n      if (toActivate.organizationSlug) {\n        ret.set('organization_id', toActivate.organizationSlug);\n      }\n    }\n    return ret;\n  }\n}\n", "import type { MatchFunction } from '@clerk/shared/pathToRegexp';\nimport { match } from '@clerk/shared/pathToRegexp';\n\nimport type { OrganizationSyncOptions, OrganizationSyncTarget } from './types';\n\nexport class OrganizationMatcher {\n  private readonly organizationPattern: MatchFunction | null;\n  private readonly personalAccountPattern: MatchFunction | null;\n\n  constructor(options?: OrganizationSyncOptions) {\n    this.organizationPattern = this.createMatcher(options?.organizationPatterns);\n    this.personalAccountPattern = this.createMatcher(options?.personalAccountPatterns);\n  }\n\n  private createMatcher(pattern?: string[]): MatchFunction | null {\n    if (!pattern) return null;\n    try {\n      return match(pattern);\n    } catch (e) {\n      throw new Error(`Invalid pattern \"${pattern}\": ${e}`);\n    }\n  }\n\n  findTarget(url: URL): OrganizationSyncTarget | null {\n    const orgTarget = this.findOrganizationTarget(url);\n    if (orgTarget) return orgTarget;\n\n    return this.findPersonalAccountTarget(url);\n  }\n\n  private findOrganizationTarget(url: URL): OrganizationSyncTarget | null {\n    if (!this.organizationPattern) return null;\n\n    try {\n      const result = this.organizationPattern(url.pathname);\n      if (!result || !('params' in result)) return null;\n\n      const params = result.params as { id?: string; slug?: string };\n      if (params.id) return { type: 'organization', organizationId: params.id };\n      if (params.slug) return { type: 'organization', organizationSlug: params.slug };\n\n      return null;\n    } catch (e) {\n      console.error('Failed to match organization pattern:', e);\n      return null;\n    }\n  }\n\n  private findPersonalAccountTarget(url: URL): OrganizationSyncTarget | null {\n    if (!this.personalAccountPattern) return null;\n\n    try {\n      const result = this.personalAccountPattern(url.pathname);\n      return result ? { type: 'personalAccount' } : null;\n    } catch (e) {\n      console.error('Failed to match personal account pattern:', e);\n      return null;\n    }\n  }\n}\n", "import type { JwtPayload } from '@clerk/types';\n\nimport { constants } from '../constants';\nimport type { TokenCarrier } from '../errors';\nimport { MachineTokenVerificationError, TokenVerificationError, TokenVerificationErrorReason } from '../errors';\nimport { decodeJwt } from '../jwt/verifyJwt';\nimport { assertValidSecretKey } from '../util/optionsAssertions';\nimport { isDevelopmentFromSecretKey } from '../util/shared';\nimport type { AuthenticateContext } from './authenticateContext';\nimport { createAuthenticateContext } from './authenticateContext';\nimport type { SignedInAuthObject } from './authObjects';\nimport type { HandshakeState, RequestState, SignedInState, SignedOutState, UnauthenticatedState } from './authStatus';\nimport { AuthErrorReason, handshake, signedIn, signedOut } from './authStatus';\nimport { createClerkRequest } from './clerkRequest';\nimport { getCookieName, getCookieValue } from './cookie';\nimport { HandshakeService } from './handshake';\nimport { getMachineTokenType, isMachineTokenByPrefix, isTokenTypeAccepted } from './machine';\nimport { OrganizationMatcher } from './organizationMatcher';\nimport type { MachineTokenType, SessionTokenType } from './tokenTypes';\nimport { TokenType } from './tokenTypes';\nimport type { AuthenticateRequestOptions } from './types';\nimport { verifyMachineAuthToken, verifyToken } from './verify';\n\nexport const RefreshTokenErrorReason = {\n  NonEligibleNoCookie: 'non-eligible-no-refresh-cookie',\n  NonEligibleNonGet: 'non-eligible-non-get',\n  InvalidSessionToken: 'invalid-session-token',\n  MissingApiClient: 'missing-api-client',\n  MissingSessionToken: 'missing-session-token',\n  MissingRefreshToken: 'missing-refresh-token',\n  ExpiredSessionTokenDecodeFailed: 'expired-session-token-decode-failed',\n  ExpiredSessionTokenMissingSidClaim: 'expired-session-token-missing-sid-claim',\n  FetchError: 'fetch-error',\n  UnexpectedSDKError: 'unexpected-sdk-error',\n  UnexpectedBAPIError: 'unexpected-bapi-error',\n} as const;\n\nfunction assertSignInUrlExists(signInUrl: string | undefined, key: string): asserts signInUrl is string {\n  if (!signInUrl && isDevelopmentFromSecretKey(key)) {\n    throw new Error(`Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite`);\n  }\n}\n\nfunction assertProxyUrlOrDomain(proxyUrlOrDomain: string | undefined) {\n  if (!proxyUrlOrDomain) {\n    throw new Error(`Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl`);\n  }\n}\n\nfunction assertSignInUrlFormatAndOrigin(_signInUrl: string, origin: string) {\n  let signInUrl: URL;\n  try {\n    signInUrl = new URL(_signInUrl);\n  } catch {\n    throw new Error(`The signInUrl needs to have a absolute url format.`);\n  }\n\n  if (signInUrl.origin === origin) {\n    throw new Error(`The signInUrl needs to be on a different origin than your satellite application.`);\n  }\n}\n\nfunction isRequestEligibleForRefresh(\n  err: TokenVerificationError,\n  authenticateContext: { refreshTokenInCookie?: string },\n  request: Request,\n) {\n  return (\n    err.reason === TokenVerificationErrorReason.TokenExpired &&\n    !!authenticateContext.refreshTokenInCookie &&\n    request.method === 'GET'\n  );\n}\n\nfunction checkTokenTypeMismatch(\n  parsedTokenType: MachineTokenType,\n  acceptsToken: NonNullable<AuthenticateRequestOptions['acceptsToken']>,\n  authenticateContext: AuthenticateContext,\n): UnauthenticatedState<MachineTokenType> | null {\n  const mismatch = !isTokenTypeAccepted(parsedTokenType, acceptsToken);\n  if (mismatch) {\n    return signedOut({\n      tokenType: parsedTokenType,\n      authenticateContext,\n      reason: AuthErrorReason.TokenTypeMismatch,\n    });\n  }\n  return null;\n}\n\nexport interface AuthenticateRequest {\n  /**\n   * @example\n   * clerkClient.authenticateRequest(request, { acceptsToken: ['session_token', 'api_key'] });\n   */\n  <T extends readonly TokenType[]>(\n    request: Request,\n    options: AuthenticateRequestOptions & { acceptsToken: T },\n  ): Promise<RequestState<T[number]>>;\n\n  /**\n   * @example\n   * clerkClient.authenticateRequest(request, { acceptsToken: 'session_token' });\n   */\n  <T extends TokenType>(\n    request: Request,\n    options: AuthenticateRequestOptions & { acceptsToken: T },\n  ): Promise<RequestState<T>>;\n\n  /**\n   * @example\n   * clerkClient.authenticateRequest(request, { acceptsToken: 'any' });\n   */\n  (request: Request, options: AuthenticateRequestOptions & { acceptsToken: 'any' }): Promise<RequestState<TokenType>>;\n\n  /**\n   * @example\n   * clerkClient.authenticateRequest(request);\n   */\n  (request: Request, options?: AuthenticateRequestOptions): Promise<RequestState<SessionTokenType>>;\n}\n\nexport const authenticateRequest: AuthenticateRequest = (async (\n  request: Request,\n  options: AuthenticateRequestOptions,\n): Promise<RequestState<TokenType>> => {\n  const authenticateContext = await createAuthenticateContext(createClerkRequest(request), options);\n  assertValidSecretKey(authenticateContext.secretKey);\n\n  // Default tokenType is session_token for backwards compatibility.\n  const acceptsToken = options.acceptsToken ?? TokenType.SessionToken;\n\n  if (authenticateContext.isSatellite) {\n    assertSignInUrlExists(authenticateContext.signInUrl, authenticateContext.secretKey);\n    if (authenticateContext.signInUrl && authenticateContext.origin) {\n      assertSignInUrlFormatAndOrigin(authenticateContext.signInUrl, authenticateContext.origin);\n    }\n    assertProxyUrlOrDomain(authenticateContext.proxyUrl || authenticateContext.domain);\n  }\n\n  const organizationMatcher = new OrganizationMatcher(options.organizationSyncOptions);\n  const handshakeService = new HandshakeService(\n    authenticateContext,\n    { organizationSyncOptions: options.organizationSyncOptions },\n    organizationMatcher,\n  );\n\n  async function refreshToken(\n    authenticateContext: AuthenticateContext,\n  ): Promise<{ data: string[]; error: null } | { data: null; error: any }> {\n    // To perform a token refresh, apiClient must be defined.\n    if (!options.apiClient) {\n      return {\n        data: null,\n        error: {\n          message: 'An apiClient is needed to perform token refresh.',\n          cause: { reason: RefreshTokenErrorReason.MissingApiClient },\n        },\n      };\n    }\n    const { sessionToken: expiredSessionToken, refreshTokenInCookie: refreshToken } = authenticateContext;\n    if (!expiredSessionToken) {\n      return {\n        data: null,\n        error: {\n          message: 'Session token must be provided.',\n          cause: { reason: RefreshTokenErrorReason.MissingSessionToken },\n        },\n      };\n    }\n    if (!refreshToken) {\n      return {\n        data: null,\n        error: {\n          message: 'Refresh token must be provided.',\n          cause: { reason: RefreshTokenErrorReason.MissingRefreshToken },\n        },\n      };\n    }\n    // The token refresh endpoint requires a sessionId, so we decode that from the expired token.\n    const { data: decodeResult, errors: decodedErrors } = decodeJwt(expiredSessionToken);\n    if (!decodeResult || decodedErrors) {\n      return {\n        data: null,\n        error: {\n          message: 'Unable to decode the expired session token.',\n          cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenDecodeFailed, errors: decodedErrors },\n        },\n      };\n    }\n\n    if (!decodeResult?.payload?.sid) {\n      return {\n        data: null,\n        error: {\n          message: 'Expired session token is missing the `sid` claim.',\n          cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenMissingSidClaim },\n        },\n      };\n    }\n\n    try {\n      // Perform the actual token refresh.\n      const response = await options.apiClient.sessions.refreshSession(decodeResult.payload.sid, {\n        format: 'cookie',\n        suffixed_cookies: authenticateContext.usesSuffixedCookies(),\n        expired_token: expiredSessionToken || '',\n        refresh_token: refreshToken || '',\n        request_origin: authenticateContext.clerkUrl.origin,\n        // The refresh endpoint expects headers as Record<string, string[]>, so we need to transform it.\n        request_headers: Object.fromEntries(Array.from(request.headers.entries()).map(([k, v]) => [k, [v]])),\n      });\n      return { data: response.cookies, error: null };\n    } catch (err: any) {\n      if (err?.errors?.length) {\n        if (err.errors[0].code === 'unexpected_error') {\n          return {\n            data: null,\n            error: {\n              message: `Fetch unexpected error`,\n              cause: { reason: RefreshTokenErrorReason.FetchError, errors: err.errors },\n            },\n          };\n        }\n        return {\n          data: null,\n          error: {\n            message: err.errors[0].code,\n            cause: { reason: err.errors[0].code, errors: err.errors },\n          },\n        };\n      } else {\n        return {\n          data: null,\n          error: {\n            message: `Unexpected Server/BAPI error`,\n            cause: { reason: RefreshTokenErrorReason.UnexpectedBAPIError, errors: [err] },\n          },\n        };\n      }\n    }\n  }\n\n  async function attemptRefresh(\n    authenticateContext: AuthenticateContext,\n  ): Promise<\n    | { data: { jwtPayload: JwtPayload; sessionToken: string; headers: Headers }; error: null }\n    | { data: null; error: any }\n  > {\n    const { data: cookiesToSet, error } = await refreshToken(authenticateContext);\n    if (!cookiesToSet || cookiesToSet.length === 0) {\n      return { data: null, error };\n    }\n\n    const headers = new Headers();\n    let sessionToken = '';\n    cookiesToSet.forEach((x: string) => {\n      headers.append('Set-Cookie', x);\n      if (getCookieName(x).startsWith(constants.Cookies.Session)) {\n        sessionToken = getCookieValue(x);\n      }\n    });\n\n    // Since we're going to return a signedIn response, we need to decode the data from the new sessionToken.\n    const { data: jwtPayload, errors } = await verifyToken(sessionToken, authenticateContext);\n    if (errors) {\n      return {\n        data: null,\n        error: {\n          message: `Clerk: unable to verify refreshed session token.`,\n          cause: { reason: RefreshTokenErrorReason.InvalidSessionToken, errors },\n        },\n      };\n    }\n    return { data: { jwtPayload, sessionToken, headers }, error: null };\n  }\n\n  function handleMaybeHandshakeStatus(\n    authenticateContext: AuthenticateContext,\n    reason: string,\n    message: string,\n    headers?: Headers,\n  ): SignedInState | SignedOutState | HandshakeState {\n    if (!handshakeService.isRequestEligibleForHandshake()) {\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        reason,\n        message,\n      });\n    }\n\n    // Right now the only usage of passing in different headers is for multi-domain sync, which redirects somewhere else.\n    // In the future if we want to decorate the handshake redirect with additional headers per call we need to tweak this logic.\n    const handshakeHeaders = headers ?? handshakeService.buildRedirectToHandshake(reason);\n\n    // Chrome aggressively caches inactive tabs. If we don't set the header here,\n    // all 307 redirects will be cached and the handshake will end up in an infinite loop.\n    if (handshakeHeaders.get(constants.Headers.Location)) {\n      handshakeHeaders.set(constants.Headers.CacheControl, 'no-store');\n    }\n\n    // Introduce the mechanism to protect for infinite handshake redirect loops\n    // using a cookie and returning true if it's infinite redirect loop or false if we can\n    // proceed with triggering handshake.\n    const isRedirectLoop = handshakeService.checkAndTrackRedirectLoop(handshakeHeaders);\n    if (isRedirectLoop) {\n      const msg = `Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard.`;\n      console.log(msg);\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        reason,\n        message,\n      });\n    }\n\n    return handshake(authenticateContext, reason, message, handshakeHeaders);\n  }\n\n  /**\n   * Determines if a handshake must occur to resolve a mismatch between the organization as specified\n   * by the URL (according to the options) and the actual active organization on the session.\n   *\n   * @returns {HandshakeState | SignedOutState | null} - The function can return the following:\n   *   - {HandshakeState}: If a handshake is needed to resolve the mismatched organization.\n   *   - {SignedOutState}: If a handshake is required but cannot be performed.\n   *   - {null}:           If no action is required.\n   */\n  function handleMaybeOrganizationSyncHandshake(\n    authenticateContext: AuthenticateContext,\n    auth: SignedInAuthObject,\n  ): HandshakeState | SignedOutState | null {\n    const organizationSyncTarget = organizationMatcher.findTarget(authenticateContext.clerkUrl);\n    if (!organizationSyncTarget) {\n      return null;\n    }\n    let mustActivate = false;\n    if (organizationSyncTarget.type === 'organization') {\n      // Activate an org by slug?\n      if (organizationSyncTarget.organizationSlug && organizationSyncTarget.organizationSlug !== auth.orgSlug) {\n        mustActivate = true;\n      }\n      // Activate an org by ID?\n      if (organizationSyncTarget.organizationId && organizationSyncTarget.organizationId !== auth.orgId) {\n        mustActivate = true;\n      }\n    }\n    // Activate the personal account?\n    if (organizationSyncTarget.type === 'personalAccount' && auth.orgId) {\n      mustActivate = true;\n    }\n    if (!mustActivate) {\n      return null;\n    }\n    if (authenticateContext.handshakeRedirectLoopCounter > 0) {\n      // We have an organization that needs to be activated, but this isn't our first time redirecting.\n      // This is because we attempted to activate the organization previously, but the organization\n      // must not have been valid (either not found, or not valid for this user), and gave us back\n      // a null organization. We won't re-try the handshake, and leave it to the server component to handle.\n      console.warn(\n        'Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation.',\n      );\n      return null;\n    }\n    const handshakeState = handleMaybeHandshakeStatus(\n      authenticateContext,\n      AuthErrorReason.ActiveOrganizationMismatch,\n      '',\n    );\n    if (handshakeState.status !== 'handshake') {\n      // Currently, this is only possible if we're in a redirect loop, but the above check should guard against that.\n      return null;\n    }\n    return handshakeState;\n  }\n\n  async function authenticateRequestWithTokenInHeader() {\n    const { tokenInHeader } = authenticateContext;\n\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const { data, errors } = await verifyToken(tokenInHeader!, authenticateContext);\n      if (errors) {\n        throw errors[0];\n      }\n      // use `await` to force this try/catch handle the signedIn invocation\n      return signedIn({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        sessionClaims: data,\n        headers: new Headers(),\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        token: tokenInHeader!,\n      });\n    } catch (err) {\n      return handleSessionTokenError(err, 'header');\n    }\n  }\n\n  async function authenticateRequestWithTokenInCookie() {\n    const hasActiveClient = authenticateContext.clientUat;\n    const hasSessionToken = !!authenticateContext.sessionTokenInCookie;\n    const hasDevBrowserToken = !!authenticateContext.devBrowserToken;\n\n    /**\n     * If we have a handshakeToken, resolve the handshake and attempt to return a definitive signed in or signed out state.\n     */\n    if (authenticateContext.handshakeNonce || authenticateContext.handshakeToken) {\n      try {\n        return await handshakeService.resolveHandshake();\n      } catch (error) {\n        // In production, the handshake token is being transferred as a cookie, so there is a possibility of collision\n        // with a handshake token of another app running on the same etld+1 domain.\n        // For example, if one app is running on sub1.clerk.com and another on sub2.clerk.com, the handshake token\n        // cookie for both apps will be set on etld+1 (clerk.com) so there's a possibility that one app will accidentally\n        // use the handshake token of a different app during the handshake flow.\n        // In this scenario, verification will fail with TokenInvalidSignature. In contrast to the development case,\n        // we need to allow the flow to continue so the app eventually retries another handshake with the correct token.\n        // We need to make sure, however, that we don't allow the flow to continue indefinitely, so we throw an error after X\n        // retries to avoid an infinite loop. An infinite loop can happen if the customer switched Clerk keys for their prod app.\n\n        // Check the handleTokenVerificationErrorInDevelopment method for the development case.\n        if (error instanceof TokenVerificationError && authenticateContext.instanceType === 'development') {\n          handshakeService.handleTokenVerificationErrorInDevelopment(error);\n        } else {\n          console.error('Clerk: unable to resolve handshake:', error);\n        }\n      }\n    }\n    /**\n     * Otherwise, check for \"known unknown\" auth states that we can resolve with a handshake.\n     */\n    if (\n      authenticateContext.instanceType === 'development' &&\n      authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.DevBrowser)\n    ) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserSync, '');\n    }\n\n    const isRequestEligibleForMultiDomainSync =\n      authenticateContext.isSatellite && authenticateContext.secFetchDest === 'document';\n\n    /**\n     * Begin multi-domain sync flows\n     */\n    if (authenticateContext.instanceType === 'production' && isRequestEligibleForMultiDomainSync) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, '');\n    }\n\n    // Multi-domain development sync flow\n    if (\n      authenticateContext.instanceType === 'development' &&\n      isRequestEligibleForMultiDomainSync &&\n      !authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.ClerkSynced)\n    ) {\n      // initiate MD sync\n\n      // signInUrl exists, checked at the top of `authenticateRequest`\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const redirectURL = new URL(authenticateContext.signInUrl!);\n      redirectURL.searchParams.append(\n        constants.QueryParameters.ClerkRedirectUrl,\n        authenticateContext.clerkUrl.toString(),\n      );\n      const headers = new Headers({ [constants.Headers.Location]: redirectURL.toString() });\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, '', headers);\n    }\n\n    // Multi-domain development sync flow\n    const redirectUrl = new URL(authenticateContext.clerkUrl).searchParams.get(\n      constants.QueryParameters.ClerkRedirectUrl,\n    );\n\n    if (authenticateContext.instanceType === 'development' && !authenticateContext.isSatellite && redirectUrl) {\n      // Dev MD sync from primary, redirect back to satellite w/ dev browser query param\n      const redirectBackToSatelliteUrl = new URL(redirectUrl);\n\n      if (authenticateContext.devBrowserToken) {\n        redirectBackToSatelliteUrl.searchParams.append(\n          constants.QueryParameters.DevBrowser,\n          authenticateContext.devBrowserToken,\n        );\n      }\n      redirectBackToSatelliteUrl.searchParams.append(constants.QueryParameters.ClerkSynced, 'true');\n\n      const headers = new Headers({ [constants.Headers.Location]: redirectBackToSatelliteUrl.toString() });\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.PrimaryRespondsToSyncing, '', headers);\n    }\n    /**\n     * End multi-domain sync flows\n     */\n\n    if (authenticateContext.instanceType === 'development' && !hasDevBrowserToken) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserMissing, '');\n    }\n\n    if (!hasActiveClient && !hasSessionToken) {\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        reason: AuthErrorReason.SessionTokenAndUATMissing,\n      });\n    }\n\n    // This can eagerly run handshake since client_uat is SameSite=Strict in dev\n    if (!hasActiveClient && hasSessionToken) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenWithoutClientUAT, '');\n    }\n\n    if (hasActiveClient && !hasSessionToken) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.ClientUATWithoutSessionToken, '');\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const { data: decodeResult, errors: decodedErrors } = decodeJwt(authenticateContext.sessionTokenInCookie!);\n\n    if (decodedErrors) {\n      return handleSessionTokenError(decodedErrors[0], 'cookie');\n    }\n\n    if (decodeResult.payload.iat < authenticateContext.clientUat) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenIATBeforeClientUAT, '');\n    }\n\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const { data, errors } = await verifyToken(authenticateContext.sessionTokenInCookie!, authenticateContext);\n      if (errors) {\n        throw errors[0];\n      }\n\n      const signedInRequestState = signedIn({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        sessionClaims: data,\n        headers: new Headers(),\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        token: authenticateContext.sessionTokenInCookie!,\n      });\n\n      const authObject = signedInRequestState.toAuth();\n      // Org sync if necessary\n      if (authObject.userId) {\n        const handshakeRequestState = handleMaybeOrganizationSyncHandshake(authenticateContext, authObject);\n        if (handshakeRequestState) {\n          return handshakeRequestState;\n        }\n      }\n\n      return signedInRequestState;\n    } catch (err) {\n      return handleSessionTokenError(err, 'cookie');\n    }\n\n    // Unreachable\n    return signedOut({\n      tokenType: TokenType.SessionToken,\n      authenticateContext,\n      reason: AuthErrorReason.UnexpectedError,\n    });\n  }\n\n  async function handleSessionTokenError(\n    err: unknown,\n    tokenCarrier: TokenCarrier,\n  ): Promise<SignedInState | SignedOutState | HandshakeState> {\n    if (!(err instanceof TokenVerificationError)) {\n      return signedOut({\n        tokenType: TokenType.SessionToken,\n        authenticateContext,\n        reason: AuthErrorReason.UnexpectedError,\n      });\n    }\n\n    let refreshError: string | null;\n\n    if (isRequestEligibleForRefresh(err, authenticateContext, request)) {\n      const { data, error } = await attemptRefresh(authenticateContext);\n      if (data) {\n        return signedIn({\n          tokenType: TokenType.SessionToken,\n          authenticateContext,\n          sessionClaims: data.jwtPayload,\n          headers: data.headers,\n          token: data.sessionToken,\n        });\n      }\n\n      // If there's any error, simply fallback to the handshake flow including the reason as a query parameter.\n      if (error?.cause?.reason) {\n        refreshError = error.cause.reason;\n      } else {\n        refreshError = RefreshTokenErrorReason.UnexpectedSDKError;\n      }\n    } else {\n      if (request.method !== 'GET') {\n        refreshError = RefreshTokenErrorReason.NonEligibleNonGet;\n      } else if (!authenticateContext.refreshTokenInCookie) {\n        refreshError = RefreshTokenErrorReason.NonEligibleNoCookie;\n      } else {\n        //refresh error is not applicable if token verification error is not 'session-token-expired'\n        refreshError = null;\n      }\n    }\n\n    err.tokenCarrier = tokenCarrier;\n\n    const reasonToHandshake = [\n      TokenVerificationErrorReason.TokenExpired,\n      TokenVerificationErrorReason.TokenNotActiveYet,\n      TokenVerificationErrorReason.TokenIatInTheFuture,\n    ].includes(err.reason);\n\n    if (reasonToHandshake) {\n      return handleMaybeHandshakeStatus(\n        authenticateContext,\n        convertTokenVerificationErrorReasonToAuthErrorReason({ tokenError: err.reason, refreshError }),\n        err.getFullMessage(),\n      );\n    }\n\n    return signedOut({\n      tokenType: TokenType.SessionToken,\n      authenticateContext,\n      reason: err.reason,\n      message: err.getFullMessage(),\n    });\n  }\n\n  function handleMachineError(tokenType: MachineTokenType, err: unknown): UnauthenticatedState<MachineTokenType> {\n    if (!(err instanceof MachineTokenVerificationError)) {\n      return signedOut({\n        tokenType,\n        authenticateContext,\n        reason: AuthErrorReason.UnexpectedError,\n      });\n    }\n\n    return signedOut({\n      tokenType,\n      authenticateContext,\n      reason: err.code,\n      message: err.getFullMessage(),\n    });\n  }\n\n  async function authenticateMachineRequestWithTokenInHeader() {\n    const { tokenInHeader } = authenticateContext;\n    // Use session token error handling if no token in header (default behavior)\n    if (!tokenInHeader) {\n      return handleSessionTokenError(new Error('Missing token in header'), 'header');\n    }\n\n    // Handle case where tokenType is any and the token is not a machine token\n    if (!isMachineTokenByPrefix(tokenInHeader)) {\n      return signedOut({\n        tokenType: acceptsToken as MachineTokenType,\n        authenticateContext,\n        reason: AuthErrorReason.TokenTypeMismatch,\n        message: '',\n      });\n    }\n\n    const parsedTokenType = getMachineTokenType(tokenInHeader);\n    const mismatchState = checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext);\n    if (mismatchState) {\n      return mismatchState;\n    }\n\n    const { data, tokenType, errors } = await verifyMachineAuthToken(tokenInHeader, authenticateContext);\n    if (errors) {\n      return handleMachineError(tokenType, errors[0]);\n    }\n    return signedIn({\n      tokenType,\n      authenticateContext,\n      machineData: data,\n      token: tokenInHeader,\n    });\n  }\n\n  async function authenticateAnyRequestWithTokenInHeader() {\n    const { tokenInHeader } = authenticateContext;\n    // Use session token error handling if no token in header (default behavior)\n    if (!tokenInHeader) {\n      return handleSessionTokenError(new Error('Missing token in header'), 'header');\n    }\n\n    // Handle as a machine token\n    if (isMachineTokenByPrefix(tokenInHeader)) {\n      const parsedTokenType = getMachineTokenType(tokenInHeader);\n      const mismatchState = checkTokenTypeMismatch(parsedTokenType, acceptsToken, authenticateContext);\n      if (mismatchState) {\n        return mismatchState;\n      }\n\n      const { data, tokenType, errors } = await verifyMachineAuthToken(tokenInHeader, authenticateContext);\n      if (errors) {\n        return handleMachineError(tokenType, errors[0]);\n      }\n\n      return signedIn({\n        tokenType,\n        authenticateContext,\n        machineData: data,\n        token: tokenInHeader,\n      });\n    }\n\n    // Handle as a regular session token\n    const { data, errors } = await verifyToken(tokenInHeader, authenticateContext);\n    if (errors) {\n      return handleSessionTokenError(errors[0], 'header');\n    }\n\n    return signedIn({\n      tokenType: TokenType.SessionToken,\n      authenticateContext,\n      sessionClaims: data,\n      token: tokenInHeader,\n    });\n  }\n\n  if (authenticateContext.tokenInHeader) {\n    if (acceptsToken === 'any') {\n      return authenticateAnyRequestWithTokenInHeader();\n    }\n\n    if (acceptsToken === TokenType.SessionToken) {\n      return authenticateRequestWithTokenInHeader();\n    }\n\n    return authenticateMachineRequestWithTokenInHeader();\n  }\n\n  // Machine requests cannot have the token in the cookie, it must be in header.\n  if (\n    acceptsToken === TokenType.OAuthToken ||\n    acceptsToken === TokenType.ApiKey ||\n    acceptsToken === TokenType.MachineToken\n  ) {\n    return signedOut({\n      tokenType: acceptsToken,\n      authenticateContext,\n      reason: 'No token in header',\n    });\n  }\n\n  return authenticateRequestWithTokenInCookie();\n}) as AuthenticateRequest;\n\n/**\n * @internal\n */\nexport const debugRequestState = (params: RequestState) => {\n  const { isSignedIn, isAuthenticated, proxyUrl, reason, message, publishableKey, isSatellite, domain } = params;\n  return { isSignedIn, isAuthenticated, proxyUrl, reason, message, publishableKey, isSatellite, domain };\n};\n\nconst convertTokenVerificationErrorReasonToAuthErrorReason = ({\n  tokenError,\n  refreshError,\n}: {\n  tokenError: TokenVerificationErrorReason;\n  refreshError: string | null;\n}): string => {\n  switch (tokenError) {\n    case TokenVerificationErrorReason.TokenExpired:\n      return `${AuthErrorReason.SessionTokenExpired}-refresh-${refreshError}`;\n    case TokenVerificationErrorReason.TokenNotActiveYet:\n      return AuthErrorReason.SessionTokenNBF;\n    case TokenVerificationErrorReason.TokenIatInTheFuture:\n      return AuthErrorReason.SessionTokenIatInTheFuture;\n    default:\n      return AuthErrorReason.UnexpectedError;\n  }\n};\n", "import type { ApiClient } from '../api';\nimport { mergePreDefinedOptions } from '../util/mergePreDefinedOptions';\nimport type { AuthenticateRequest } from './request';\nimport { authenticateRequest as authenticateRequestOriginal, debugRequestState } from './request';\nimport type { AuthenticateRequestOptions } from './types';\n\ntype RunTimeOptions = Omit<AuthenticateRequestOptions, 'apiUrl' | 'apiVersion'>;\ntype BuildTimeOptions = Partial<\n  Pick<\n    AuthenticateRequestOptions,\n    | 'apiUrl'\n    | 'apiVersion'\n    | 'audience'\n    | 'domain'\n    | 'isSatellite'\n    | 'jwtKey'\n    | 'proxyUrl'\n    | 'publishableKey'\n    | 'secretKey'\n  >\n>;\n\nconst defaultOptions = {\n  secretKey: '',\n  jwtKey: '',\n  apiUrl: undefined,\n  apiVersion: undefined,\n  proxyUrl: '',\n  publishableKey: '',\n  isSatellite: false,\n  domain: '',\n  audience: '',\n} satisfies BuildTimeOptions;\n\n/**\n * @internal\n */\nexport type CreateAuthenticateRequestOptions = {\n  options: BuildTimeOptions;\n  apiClient: ApiClient;\n};\n\n/**\n * @internal\n */\nexport function createAuthenticateRequest(params: CreateAuthenticateRequestOptions) {\n  const buildTimeOptions = mergePreDefinedOptions(defaultOptions, params.options);\n  const apiClient = params.apiClient;\n\n  const authenticateRequest: AuthenticateRequest = (request: Request, options: RunTimeOptions = {}) => {\n    const { apiUrl, apiVersion } = buildTimeOptions;\n    const runTimeOptions = mergePreDefinedOptions(buildTimeOptions, options);\n    return authenticateRequestOriginal(request, {\n      ...options,\n      ...runTimeOptions,\n      // We should add all the omitted props from options here (eg apiUrl / apiVersion)\n      // to avoid runtime options override them.\n      apiUrl,\n      apiVersion,\n      apiClient,\n    });\n  };\n\n  return {\n    authenticateRequest,\n    debugRequestState,\n  };\n}\n", "import type { CreateBackendApiOptions, Organization, Session, User } from '../api';\nimport { createBackendApiClient } from '../api';\nimport type { AuthObject, SignedInAuthObject, SignedOutAuthObject } from '../tokens/authObjects';\n\ntype DecorateAuthWithResourcesOptions = {\n  loadSession?: boolean;\n  loadUser?: boolean;\n  loadOrganization?: boolean;\n};\n\ntype WithResources<T> = T & {\n  session?: Session | null;\n  user?: User | null;\n  organization?: Organization | null;\n};\n\n/**\n * @internal\n */\nexport const decorateObjectWithResources = async <T extends object>(\n  obj: T,\n  authObj: AuthObject,\n  opts: CreateBackendApiOptions & DecorateAuthWithResourcesOptions,\n): Promise<WithResources<T>> => {\n  const { loadSession, loadUser, loadOrganization } = opts || {};\n  const { userId, sessionId, orgId } = authObj as SignedInAuthObject | SignedOutAuthObject;\n\n  const { sessions, users, organizations } = createBackendApiClient({ ...opts });\n\n  const [sessionResp, userResp, organizationResp] = await Promise.all([\n    loadSession && sessionId ? sessions.getSession(sessionId) : Promise.resolve(undefined),\n    loadUser && userId ? users.getUser(userId) : Promise.resolve(undefined),\n    loadOrganization && orgId ? organizations.getOrganization({ organizationId: orgId }) : Promise.resolve(undefined),\n  ]);\n\n  const resources = stripPrivateDataFromObject({\n    session: sessionResp,\n    user: userResp,\n    organization: organizationResp,\n  });\n  return Object.assign(obj, resources);\n};\n\n/**\n * @internal\n */\nexport function stripPrivateDataFromObject<T extends WithResources<object>>(authObject: T): T {\n  const user = authObject.user ? { ...authObject.user } : authObject.user;\n  const organization = authObject.organization ? { ...authObject.organization } : authObject.organization;\n  prunePrivateMetadata(user);\n  prunePrivateMetadata(organization);\n  return { ...authObject, user, organization };\n}\n\nfunction prunePrivateMetadata(resource?: { private_metadata: any } | { privateMetadata: any } | null) {\n  // Delete sensitive private metadata from resource before rendering in SSR\n  if (resource) {\n    if ('privateMetadata' in resource) {\n      delete resource['privateMetadata'];\n    }\n    if ('private_metadata' in resource) {\n      delete resource['private_metadata'];\n    }\n  }\n\n  return resource;\n}\n", "export { constants } from './constants';\nexport { createRedirect } from './createRedirect';\nexport type { RedirectFun } from './createRedirect';\n\nexport type { CreateAuthenticateRequestOptions } from './tokens/factory';\nexport { createAuthenticateRequest } from './tokens/factory';\n\nexport { debugRequestState } from './tokens/request';\n\nexport type {\n  AuthenticateRequestOptions,\n  OrganizationSyncOptions,\n  InferAuthObjectFromToken,\n  InferAuthObjectFromTokenArray,\n  GetAuthFn,\n} from './tokens/types';\n\nexport { TokenType } from './tokens/tokenTypes';\nexport type { SessionTokenType, MachineTokenType } from './tokens/tokenTypes';\n\nexport type {\n  SignedInAuthObjectOptions,\n  SignedInAuthObject,\n  SignedOutAuthObject,\n  AuthenticatedMachineObject,\n  UnauthenticatedMachineObject,\n} from './tokens/authObjects';\nexport {\n  makeAuthObjectSerializable,\n  signedOutAuthObject,\n  signedInAuthObject,\n  authenticatedMachineObject,\n  unauthenticatedMachineObject,\n  invalidTokenAuthObject,\n  getAuthObjectFromJwt,\n  getAuthObjectForAcceptedToken,\n} from './tokens/authObjects';\n\nexport { AuthStatus } from './tokens/authStatus';\nexport type {\n  RequestState,\n  SignedInState,\n  SignedOutState,\n  AuthenticatedState,\n  UnauthenticatedState,\n} from './tokens/authStatus';\n\nexport { decorateObjectWithResources, stripPrivateDataFromObject } from './util/decorateObjectWithResources';\n\nexport { createClerkRequest } from './tokens/clerkRequest';\nexport type { ClerkRequest } from './tokens/clerkRequest';\n\nexport { reverificationError, reverificationErrorResponse } from '@clerk/shared/authorization-errors';\n\nexport { verifyMachineAuthToken } from './tokens/verify';\n\nexport { isMachineTokenByPrefix, isMachineTokenType, getMachineTokenType, isTokenTypeAccepted } from './tokens/machine';\n"], "names": ["Headers", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "basePath", "Cookies", "data", "Cookies", "jwk", "authenticateContext", "refreshToken", "data", "errors", "authenticateRequest"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA,SAAS,4BAA4B;;;AIArC,SAAS,gCAAgC;AACzC,SAAS,uDAAuD;;A+BDhE,SAAS,uBAAuB,kBAAkB;;AAElD,OAAO,mBAAmB;;AgDF1B,SAAS,aAAa;;;AMCtB,SAAS,aAAa;;AImDtB,SAAS,qBAAqB,mCAAmC;;;;;A9FpD1D,IAAM,UAAU;AAChB,IAAM,cAAc;AAEpB,IAAM,aAAa,GAAG,gBAAY,CAAA,CAAA,EAAI,OAAe,EAAA;AACrD,IAAM,oCAAoC,IAAI;AAC9C,IAAM,yBAAyB;AAEtC,IAAM,aAAa;IACjB,WAAW;IACX,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,UAAU;AACZ;AAEA,IAAM,UAAU;IACd,SAAS;IACT,SAAS;IACT,WAAW;IACX,WAAW;IACX,YAAY;IACZ,eAAe;IACf,gBAAgB;AAClB;AAEA,IAAM,kBAAkB;IACtB,aAAa;IACb,iBAAiB;IACjB,kBAAkB;IAAA,oEAAA;IAElB,YAAY,QAAQ,UAAA;IACpB,WAAW,QAAQ,SAAA;IACnB,eAAe;IACf,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB,QAAQ,cAAA;AAC1B;AAEA,IAAMA,WAAU;IACd,QAAQ;IACR,aAAa;IACb,eAAe;IACf,YAAY;IACZ,eAAe;IACf,YAAY;IACZ,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,0BAA0B;IAC1B,aAAa;IACb,uBAAuB;IACvB,iCAAiC;IACjC,aAAa;IACb,eAAe;IACf,eAAe;IACf,gBAAgB;IAChB,MAAM;IACN,UAAU;IACV,OAAO;IACP,QAAQ;IACR,UAAU;IACV,cAAc;IACd,WAAW;IACX,oBAAoB;AACtB;AAEA,IAAM,eAAe;IACnB,MAAM;AACR;AAKO,IAAM,YAAY;IACvB;IACA;IACA,SAAAA;IACA;IACA;AACF;;AC5EA,IAAM,WAAW,CACf,UACA,YACA,gBACA,qBACG;IACH,IAAI,aAAa,IAAI;QACnB,OAAO,eAAe,WAAW,QAAA,CAAS,GAAG,gBAAgB,SAAS,CAAC;IACzE;IAEA,MAAM,UAAU,IAAI,IAAI,QAAQ;IAChC,MAAM,gBAAgB,iBAAiB,IAAI,IAAI,gBAAgB,OAAO,IAAI,KAAA;IAC1E,MAAM,MAAM,IAAI,IAAI,YAAY,OAAO;IAEvC,IAAI,eAAe;QACjB,IAAI,YAAA,CAAa,GAAA,CAAI,gBAAgB,cAAc,QAAA,CAAS,CAAC;IAC/D;IAEA,IAAI,oBAAoB,QAAQ,QAAA,KAAa,IAAI,QAAA,EAAU;QACzD,IAAI,YAAA,CAAa,GAAA,CAAI,UAAU,eAAA,CAAgB,UAAA,EAAY,gBAAgB;IAC7E;IACA,OAAO,IAAI,QAAA,CAAS;AACtB;AAWA,IAAM,iBAAiB,CAAC,WAAmB,gBAAyB;IAClE,IAAI;IACJ,IAAI,CAAC,UAAU,UAAA,CAAW,MAAM,GAAG;QACjC,IAAI,CAAC,eAAe,CAAC,YAAY,UAAA,CAAW,MAAM,GAAG;YACnD,MAAM,IAAI,MAAM,oEAAoE;QACtF;QAEA,MAAM,UAAU,IAAI,IAAI,WAAW;QACnC,MAAM,IAAI,IAAI,WAAW,QAAQ,MAAM;IACzC,OAAO;QACL,MAAM,IAAI,IAAI,SAAS;IACzB;IAEA,IAAI,aAAa;QACf,IAAI,YAAA,CAAa,GAAA,CAAI,gBAAgB,WAAW;IAClD;IAEA,OAAO,IAAI,QAAA,CAAS;AACtB;AAsBO,IAAM,iBAAiC,CAAA,WAAU;IACtD,MAAM,EAAE,cAAA,EAAgB,eAAA,EAAiB,SAAA,EAAW,SAAA,EAAW,OAAA,EAAS,aAAA,CAAc,CAAA,GAAI;IAC1F,MAAM,8UAAuB,sBAAA,EAAoB,cAAc;IAC/D,MAAM,cAAc,sBAAsB;IAC1C,MAAM,gBAAgB,sBAAsB,iBAAiB;IAC7D,MAAM,sBAAkB,6UAAA,EAAqB,WAAW;IACxD,MAAM,mBAAmB,kBAAkB;IAE3C,MAAM,kBAAkB,CAAC,KAAmB,EAAE,aAAA,CAAc,CAAA,KAAwB;QAClF,OAAO,gBACL,SAAS,SAAS,GAAG,GAAG,CAAA,MAAA,CAAA,EAAU,eAAe,gBAAgB,OAAO,eAAA,GAAkB,IAAI;IAElG;IAEA,MAAM,mBAAmB,CAAC,EAAE,aAAA,CAAc,CAAA,GAAsB,CAAC,CAAA,KAAM;QACrE,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,oUAAA,CAAA,eAAA,CAAa,+BAAA,CAAgC;QAC/C;QAEA,MAAM,oBAAoB,GAAG,eAAe,CAAA,QAAA,CAAA;QAG5C,SAAS,eAAe,MAAA,EAAkC;YACxD,IAAI,CAAC,QAAQ;gBACX;YACF;YACA,MAAM,MAAM,IAAI,IAAI,QAAQ,OAAO;YACnC,IAAI,QAAA,GAAW,GAAG,IAAI,QAAQ,CAAA,OAAA,CAAA;YAC9B,OAAO,IAAI,QAAA,CAAS;QACtB;QAEA,MAAM,YAAY,aAAa,eAAe,SAAS,KAAK;QAE5D,IAAI,kBAAkB;YACpB,OAAO,gBAAgB,WAAW;gBAAE;YAAc,CAAC;QACrD;QAEA,OAAO,gBAAgB,SAAS,SAAS,WAAW,eAAe,gBAAgB,OAAO,eAAA,GAAkB,IAAI,CAAC;IACnH;IAEA,MAAM,mBAAmB,CAAC,EAAE,aAAA,CAAc,CAAA,GAAsB,CAAC,CAAA,KAAM;QACrE,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,oUAAA,CAAA,eAAA,CAAa,+BAAA,CAAgC;QAC/C;QAEA,MAAM,oBAAoB,GAAG,eAAe,CAAA,QAAA,CAAA;QAC5C,MAAM,YAAY,aAAa;QAE/B,IAAI,kBAAkB;YACpB,OAAO,gBAAgB,WAAW;gBAAE;YAAc,CAAC;QACrD;QAEA,OAAO,gBAAgB,SAAS,SAAS,WAAW,eAAe,gBAAgB,OAAO,eAAA,GAAkB,IAAI,CAAC;IACnH;IAEA,OAAO;QAAE;QAAkB;IAAiB;AAC9C;;ACvIO,SAAS,uBAAsD,iBAAA,EAAsB,OAAA,EAAwB;IAClH,OAAO,OAAO,IAAA,CAAK,iBAAiB,EAAE,MAAA,CACpC,CAAC,KAAQ,QAAgB;QACvB,OAAO;YAAE,GAAG,GAAA;YAAK,CAAC,GAAG,CAAA,EAAG,OAAA,CAAQ,GAAG,CAAA,IAAK,GAAA,CAAI,GAAG,CAAA;QAAE;IACnD,GACA;QAAE,GAAG,iBAAA;IAAkB;AAE3B;;ACLO,SAAS,qBAAqB,GAAA,EAAqC;IACxE,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACnC,MAAM,MAAM,iGAAiG;IAC/G;AAGF;AAEO,SAAS,0BAA0B,GAAA,EAAqC;IAC7E,CAAA,GAAA,kTAAA,CAAA,sBAAA,EAAoB,KAA2B;QAAE,OAAO;IAAK,CAAC;AAChE;;ACiCA,IAAM,sBAAN,MAAyD;IAUhD,YACG,YAAA,EACA,YAAA,EACR,OAAA,CACA;QAHQ,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;QAMR,IAAA,CAAK,wBAAA,CAAyB,OAAO;QACrC,IAAA,CAAK,gBAAA,CAAiB;QAEtB,IAAA,CAAK,gBAAA,CAAiB;QACtB,IAAA,CAAK,mBAAA,CAAoB;QACzB,OAAO,MAAA,CAAO,IAAA,EAAM,OAAO;QAC3B,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,YAAA,CAAa,QAAA;IACpC;IAAA;;;;GAAA,GAnBA,IAAW,eAAmC;QAC5C,OAAO,IAAA,CAAK,oBAAA,IAAwB,IAAA,CAAK,aAAA;IAC3C;IAmBO,sBAA+B;QACpC,MAAM,oBAAoB,IAAA,CAAK,iBAAA,CAAkB,UAAU,OAAA,CAAQ,SAAS;QAC5E,MAAM,YAAY,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,SAAS;QAC5D,MAAM,kBAAkB,IAAA,CAAK,iBAAA,CAAkB,UAAU,OAAA,CAAQ,OAAO,KAAK;QAC7E,MAAM,UAAU,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,OAAO,KAAK;QAK7D,IAAI,WAAW,CAAC,IAAA,CAAK,cAAA,CAAe,OAAO,GAAG;YAC5C,OAAO;QACT;QAIA,IAAI,WAAW,CAAC,IAAA,CAAK,sBAAA,CAAuB,OAAO,GAAG;YACpD,OAAO;QACT;QAGA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB;YAC1C,OAAO;QACT;QAEA,MAAM,EAAE,MAAM,WAAA,CAAY,CAAA,2TAAI,aAAA,EAAU,OAAO;QAC/C,MAAM,aAAa,aAAa,QAAQ,OAAO;QAC/C,MAAM,EAAE,MAAM,mBAAA,CAAoB,CAAA,4TAAI,YAAA,EAAU,eAAe;QAC/D,MAAM,qBAAqB,qBAAqB,QAAQ,OAAO;QAI/D,IAAI,sBAAsB,OAAO,cAAc,OAAO,aAAa,oBAAoB;YACrF,OAAO;QACT;QAKA,IAAI,sBAAsB,OAAO,cAAc,KAAK;YAClD,OAAO;QACT;QA+BA,IAAI,IAAA,CAAK,YAAA,KAAiB,cAAc;YACtC,MAAM,2BAA2B,IAAA,CAAK,cAAA,CAAe,mBAAmB;YACxE,IAAI,sBAAsB,OAAO,cAAc,OAAO,0BAA0B;gBAC9E,OAAO;YACT;QACF;QAMA,IAAI,CAAC,qBAAqB,iBAAiB;YACzC,OAAO;QACT;QAEA,OAAO;IACT;IAEQ,yBAAyB,OAAA,EAAqC;QACpE,0BAA0B,QAAQ,cAAc;QAChD,IAAA,CAAK,cAAA,GAAiB,QAAQ,cAAA;QAE9B,MAAM,KAAK,6UAAA,EAAoB,IAAA,CAAK,cAAA,EAAgB;YAClD,OAAO;YACP,UAAU,QAAQ,QAAA;YAClB,QAAQ,QAAQ,MAAA;YAChB,aAAa,QAAQ,WAAA;QACvB,CAAC;QACD,IAAA,CAAK,YAAA,GAAe,GAAG,YAAA;QACvB,IAAA,CAAK,WAAA,GAAc,GAAG,WAAA;IACxB;IAEQ,mBAAmB;QACzB,IAAA,CAAK,aAAA,GAAgB,IAAA,CAAK,wBAAA,CAAyB,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,aAAa,CAAC;QAClG,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,MAAM;QACrD,IAAA,CAAK,IAAA,GAAO,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,IAAI;QACjD,IAAA,CAAK,aAAA,GAAgB,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,aAAa;QACnE,IAAA,CAAK,cAAA,GACH,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,wBAAwB,KAAK,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,cAAc;QAC/G,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,QAAQ;QACzD,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,SAAS;QAC3D,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,YAAY;QACjE,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,MAAM;IACvD;IAEQ,mBAAmB;QAEzB,IAAA,CAAK,oBAAA,GAAuB,IAAA,CAAK,6BAAA,CAA8B,UAAU,OAAA,CAAQ,OAAO;QACxF,IAAA,CAAK,oBAAA,GAAuB,IAAA,CAAK,iBAAA,CAAkB,UAAU,OAAA,CAAQ,OAAO;QAC5E,IAAA,CAAK,SAAA,GAAY,OAAO,QAAA,CAAS,IAAA,CAAK,6BAAA,CAA8B,UAAU,OAAA,CAAQ,SAAS,KAAK,EAAE,KAAK;IAC7G;IAEQ,sBAAsB;QAC5B,IAAA,CAAK,eAAA,GACH,IAAA,CAAK,aAAA,CAAc,UAAU,eAAA,CAAgB,UAAU,KACvD,IAAA,CAAK,6BAAA,CAA8B,UAAU,OAAA,CAAQ,UAAU;QAEjE,IAAA,CAAK,cAAA,GACH,IAAA,CAAK,aAAA,CAAc,UAAU,eAAA,CAAgB,SAAS,KAAK,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,SAAS;QACvG,IAAA,CAAK,4BAAA,GAA+B,OAAO,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,aAAa,CAAC,KAAK;QAC/F,IAAA,CAAK,cAAA,GACH,IAAA,CAAK,aAAA,CAAc,UAAU,eAAA,CAAgB,cAAc,KAAK,IAAA,CAAK,SAAA,CAAU,UAAU,OAAA,CAAQ,cAAc;IACnH;IAEQ,cAAc,IAAA,EAAc;QAClC,OAAO,IAAA,CAAK,YAAA,CAAa,QAAA,CAAS,YAAA,CAAa,GAAA,CAAI,IAAI;IACzD;IAEQ,UAAU,IAAA,EAAc;QAC9B,OAAO,IAAA,CAAK,YAAA,CAAa,OAAA,CAAQ,GAAA,CAAI,IAAI,KAAK,KAAA;IAChD;IAEQ,UAAU,IAAA,EAAc;QAC9B,OAAO,IAAA,CAAK,YAAA,CAAa,OAAA,CAAQ,GAAA,CAAI,IAAI,KAAK,KAAA;IAChD;IAEQ,kBAAkB,IAAA,EAAc;QACtC,OAAO,IAAA,CAAK,SAAA,wTAAU,wBAAA,EAAsB,MAAM,IAAA,CAAK,YAAY,CAAC,KAAK,KAAA;IAC3E;IAEQ,8BAA8B,UAAA,EAAoB;QACxD,IAAI,IAAA,CAAK,mBAAA,CAAoB,GAAG;YAC9B,OAAO,IAAA,CAAK,iBAAA,CAAkB,UAAU;QAC1C;QACA,OAAO,IAAA,CAAK,SAAA,CAAU,UAAU;IAClC;IAEQ,yBAAyB,mBAAA,EAAoE;QACnG,IAAI,CAAC,qBAAqB;YACxB,OAAO,KAAA;QACT;QAEA,MAAM,CAAC,QAAQ,KAAK,CAAA,GAAI,oBAAoB,KAAA,CAAM,KAAK,CAAC;QAExD,IAAI,CAAC,OAAO;YAEV,OAAO;QACT;QAEA,IAAI,WAAW,UAAU;YACvB,OAAO;QACT;QAGA,OAAO,KAAA;IACT;IAEQ,eAAe,KAAA,EAAwB;QAC7C,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,4TAAI,YAAA,EAAU,KAAK;QACxC,IAAI,QAAQ;YACV,OAAO;QACT;QACA,OAAO,CAAC,CAAC,KAAK,OAAA,CAAQ,GAAA;IACxB;IAEQ,uBAAuB,KAAA,EAAwB;QACrD,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,4TAAI,YAAA,EAAU,KAAK;QACxC,IAAI,QAAQ;YACV,OAAO;QACT;QACA,MAAM,cAAc,KAAK,OAAA,CAAQ,GAAA,CAAI,OAAA,CAAQ,iBAAiB,EAAE;QAChE,OAAO,IAAA,CAAK,WAAA,KAAgB;IAC9B;IAEQ,eAAe,GAAA,EAA+B;QACpD,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,OAAQ,KAAK,GAAA,CAAI,IAAI,OAAS;IAC7D;AACF;AAIO,IAAM,4BAA4B,OACvC,cACA,YACiC;IACjC,MAAM,eAAe,QAAQ,cAAA,GACzB,6TAAM,kBAAA,EAAgB,QAAQ,cAAA,uTAAgB,UAAA,CAAQ,MAAA,CAAO,MAAM,IACnE;IACJ,OAAO,IAAI,oBAAoB,cAAc,cAAc,OAAO;AACpE;;;;AE9RA,IAAM,YAAY;AAClB,IAAM,2BAA2B,IAAI,OAAO,WAAW,YAAY,QAAQ,GAAG;AAIvE,SAAS,UAAA,GAAa,IAAA,EAA4B;IACvD,OAAO,KACJ,MAAA,CAAO,CAAA,IAAK,CAAC,EACb,IAAA,CAAK,SAAS,EACd,OAAA,CAAQ,0BAA0B,SAAS;AAChD;;ACRO,IAAe,cAAf,MAA2B;IAChC,YAAsB,OAAA,CAA0B;QAA1B,IAAA,CAAA,OAAA,GAAA;IAA2B;IAEvC,UAAU,EAAA,EAAY;QAC9B,IAAI,CAAC,IAAI;YACP,MAAM,IAAI,MAAM,kCAAkC;QACpD;IACF;AACF;;ACNA,IAAM,WAAW;AAyCV,IAAM,gBAAN,cAA4B,YAAY;IAC7C,MAAa,OAAO,MAAA,EAAgC;QAClD,OAAO,IAAA,CAAK,OAAA,CAAoB;YAC9B,QAAQ;YACR,MAAM;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,OAAO,YAAA,EAAsB;QACxC,IAAA,CAAK,SAAA,CAAU,YAAY;QAC3B,OAAO,IAAA,CAAK,OAAA,CAAoB;YAC9B,QAAQ;YACR,MAAM,UAAU,UAAU,cAAc,QAAQ;QAClD,CAAC;IACH;AACF;;ACzDA,IAAMC,YAAW;AAEV,IAAM,4BAAN,cAAwC,YAAY;IACzD,MAAa,+BAA+B;QAC1C,OAAO,IAAA,CAAK,OAAA,CAAgC;YAC1C,QAAQ;YACR,MAAMA;QACR,CAAC;IACH;IAEA,MAAa,2CAA2C;QACtD,OAAO,IAAA,CAAK,OAAA,CAAgC;YAC1C,QAAQ;YACR,MAAM,UAAUA,WAAU,UAAU;QACtC,CAAC;IACH;AACF;;ACZA,IAAMC,YAAW;AAOV,IAAM,yBAAN,cAAqC,YAAY;IACtD,MAAa,2BAA2B,SAAiC,CAAC,CAAA,EAAG;QAC3E,OAAO,IAAA,CAAK,OAAA,CAA0D;YACpE,QAAQ;YACR,MAAMA;YACN,aAAa;gBAAE,GAAG,MAAA;gBAAQ,WAAW;YAAK;QAC5C,CAAC;IACH;IAEA,MAAa,0BAA0B,MAAA,EAAyC;QAC9E,OAAO,IAAA,CAAK,OAAA,CAA6B;YACvC,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,0BAA0B,qBAAA,EAA+B;QACpE,IAAA,CAAK,SAAA,CAAU,qBAAqB;QACpC,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,WAAU,qBAAqB;QACjD,CAAC;IACH;AACF;;ACnCA,IAAMC,YAAW;AAEV,IAAM,aAAN,cAAyB,YAAY;IAC1C,MAAM,aAAa,MAAA,EAAgB;QACjC,OAAO,IAAA,CAAK,OAAA,CAAgB;YAC1B,QAAQ;YACR,MAAM,UAAUA,WAAU,QAAQ;YAClC,YAAY;gBAAE;YAAO;QACvB,CAAC;IACH;AACF;;ACXA,IAAMC,YAAW;AAgBV,IAAM,kBAAN,cAA8B,YAAY;IAAA;;;;;;;;;GAAA,GAW/C,MAAa,aAAa,MAAA,EAA4B;QACpD,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,WAAU,eAAe;YACzC,YAAY;QACd,CAAC;IACH;AACF;;AC7BA,IAAMC,YAAW;AAMV,IAAM,yBAAN,cAAqC,YAAY;IACtD,MAAa,2BAA2B,SAAiC,CAAC,CAAA,EAAG;QAC3E,OAAO,IAAA,CAAK,OAAA,CAA0D;YACpE,QAAQ;YACR,MAAMA;YACN,aAAa;QACf,CAAC;IACH;IAEA,MAAa,0BAA0B,MAAA,EAAyC;QAC9E,OAAO,IAAA,CAAK,OAAA,CAA6B;YACvC,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,0BAA0B,qBAAA,EAA+B;QACpE,IAAA,CAAK,SAAA,CAAU,qBAAqB;QACpC,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,WAAU,qBAAqB;QACjD,CAAC;IACH;AACF;;AC9BA,IAAMC,YAAW;AAMV,IAAM,YAAN,cAAwB,YAAY;IACzC,MAAa,cAAc,SAAiC,CAAC,CAAA,EAAG;QAC9D,OAAO,IAAA,CAAK,OAAA,CAA6C;YACvD,QAAQ;YACR,MAAMA;YACN,aAAa;gBAAE,GAAG,MAAA;gBAAQ,WAAW;YAAK;QAC5C,CAAC;IACH;IAEA,MAAa,UAAU,QAAA,EAAkB;QACvC,IAAA,CAAK,SAAA,CAAU,QAAQ;QACvB,OAAO,IAAA,CAAK,OAAA,CAAgB;YAC1B,QAAQ;YACR,MAAM,UAAUA,WAAU,QAAQ;QACpC,CAAC;IACH;IAEO,aAAa,KAAA,EAAe;QACjC,OAAO,IAAA,CAAK,OAAA,CAAgB;YAC1B,QAAQ;YACR,MAAM,UAAUA,WAAU,QAAQ;YAClC,YAAY;gBAAE;YAAM;QACtB,CAAC;IACH;IAEA,MAAa,oBAAoB,WAAA,EAAwC;QACvE,OAAO,IAAA,CAAK,OAAA,CAA0B;YACpC,QAAQ;YACR,MAAM,UAAUA,WAAU,mBAAmB;YAC7C;QACF,CAAC;IACH;AACF;;ACxCA,IAAMC,YAAW;AA8BV,IAAM,YAAN,cAAwB,YAAY;IACzC,MAAa,OAAO;QAClB,OAAO,IAAA,CAAK,OAAA,CAA6C;YACvD,QAAQ;YACR,MAAMA;QACR,CAAC;IACH;IAEA,MAAa,IAAI,MAAA,EAAyB;QACxC,OAAO,IAAA,CAAK,OAAA,CAAgB;YAC1B,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,OAAO,MAAA,EAA4B;QAC9C,MAAM,EAAE,QAAA,EAAU,GAAG,WAAW,CAAA,GAAI;QAEpC,IAAA,CAAK,SAAA,CAAU,QAAQ;QAEvB,OAAO,IAAA,CAAK,OAAA,CAAgB;YAC1B,QAAQ;YACR,MAAM,UAAUA,WAAU,QAAQ;YAClC;QACF,CAAC;IACH;IAAA;;;GAAA,GAMA,MAAa,OAAO,iBAAA,EAA2B;QAC7C,OAAO,IAAA,CAAK,YAAA,CAAa,iBAAiB;IAC5C;IAAA;;GAAA,GAKA,MAAa,aAAa,iBAAA,EAA2B;QACnD,IAAA,CAAK,SAAA,CAAU,iBAAiB;QAChC,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,WAAU,iBAAiB;QAC7C,CAAC;IACH;AACF;;AC9EA,IAAMC,YAAW;AAcV,IAAM,kBAAN,cAA8B,YAAY;IAC/C,MAAa,gBAAgB,cAAA,EAAwB;QACnD,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAM,UAAUA,WAAU,cAAc;QAC1C,CAAC;IACH;IAEA,MAAa,mBAAmB,MAAA,EAAkC;QAChE,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,mBAAmB,cAAA,EAAwB,SAAmC,CAAC,CAAA,EAAG;QAC7F,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAM,UAAUA,WAAU,cAAc;YACxC,YAAY;QACd,CAAC;IACH;IAEA,MAAa,mBAAmB,cAAA,EAAwB;QACtD,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,WAAU,cAAc;QAC1C,CAAC;IACH;AACF;;AClDA,IAAMC,aAAW;AAEV,IAAM,yBAAN,cAAqC,YAAY;IACtD,MAAM,kBAAkB,WAAA,EAAqB;QAC3C,OAAO,IAAA,CAAK,OAAA,CAA6B;YACvC,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ;YAClC,YAAY;gBAAE,cAAc;YAAY;QAC1C,CAAC;IACH;AACF;;ACRA,IAAMC,aAAW;AA+DV,IAAM,cAAN,cAA0B,YAAY;IAC3C,MAAa,MAAM;QACjB,OAAO,IAAA,CAAK,OAAA,CAAkB;YAC5B,QAAQ;YACR,MAAMA;QACR,CAAC;IACH;IAEA,MAAa,OAAO,MAAA,EAAsB;QACxC,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,mBAAmB,MAAA,EAAkC;QAChE,OAAO,IAAA,CAAK,OAAA,CAA8B;YACxC,QAAQ;YACR,MAAM,UAAUA,YAAU,cAAc;YACxC,YAAY;QACd,CAAC;IACH;IAEA,MAAa,2BAA2B,MAAA,EAA0C;QAChF,OAAO,IAAA,CAAK,OAAA,CAA8B;YACxC,QAAQ;YACR,MAAM,UAAUA,YAAU,uBAAuB;YACjD,YAAY;QACd,CAAC;IACH;AACF;;AC5FA,IAAMC,aAAW;AAqCV,IAAM,gBAAN,cAA4B,YAAY;IAC7C,MAAa,kBAAkB,SAAkC,CAAC,CAAA,EAAG;QACnE,OAAO,IAAA,CAAK,OAAA,CAAiD;YAC3D,QAAQ;YACR,MAAMA;YACN,aAAa;gBAAE,GAAG,MAAA;gBAAQ,WAAW;YAAK;QAC5C,CAAC;IACH;IAEA,MAAa,iBAAiB,MAAA,EAAsB;QAClD,OAAO,IAAA,CAAK,OAAA,CAAoB;YAC9B,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,iBAAiB,YAAA,EAAsB;QAClD,IAAA,CAAK,SAAA,CAAU,YAAY;QAC3B,OAAO,IAAA,CAAK,OAAA,CAAoB;YAC9B,QAAQ;YACR,MAAM,UAAUA,YAAU,cAAc,QAAQ;QAClD,CAAC;IACH;AACF;;ACjEA,IAAMC,aAAW;AAEV,IAAM,mBAAN,cAA+B,YAAY;IAChD,MAAM,aAAa,MAAA,EAAgB;QACjC,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ;YAClC,YAAY;gBAAE;YAAO;QACvB,CAAC;IACH;AACF;;ACXA,IAAMC,aAAW;AAEV,IAAM,UAAN,cAAsB,YAAY;IACvC,MAAa,UAAU;QACrB,OAAO,IAAA,CAAK,OAAA,CAAkB;YAC5B,QAAQ;YACR,MAAMA;QACR,CAAC;IACH;AACF;;ACNA,IAAMC,aAAW;AA0CV,IAAM,kBAAN,cAA8B,YAAY;IAC/C,MAAa,KAAK,SAAiC,CAAC,CAAA,EAAG;QACrD,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAMA;YACN,aAAa;gBAAE,GAAG,MAAA;gBAAQ,WAAW;YAAK;QAC5C,CAAC;IACH;IAEA,MAAa,IAAI,UAAA,EAAoB;QACnC,IAAA,CAAK,SAAA,CAAU,UAAU;QAEzB,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAM,UAAUA,YAAU,UAAU;QACtC,CAAC;IACH;IAEA,MAAa,OAAO,MAAA,EAAiC;QACnD,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,OAAO,MAAA,EAAiC;QACnD,MAAM,EAAE,UAAA,EAAY,GAAG,WAAW,CAAA,GAAI;QAEtC,IAAA,CAAK,SAAA,CAAU,UAAU;QACzB,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAM,UAAUA,YAAU,UAAU;YACpC;QACF,CAAC;IACH;IAEA,MAAa,OAAO,UAAA,EAAoB;QACtC,IAAA,CAAK,SAAA,CAAU,UAAU;QAEzB,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,YAAU,UAAU;QACtC,CAAC;IACH;AACF;;AC7EA,IAAMC,aAAW;AA4MV,IAAM,kBAAN,cAA8B,YAAY;IAC/C,MAAa,oBAAoB,MAAA,EAAoC;QACnE,OAAO,IAAA,CAAK,OAAA,CAAmD;YAC7D,QAAQ;YACR,MAAMA;YACN,aAAa;QACf,CAAC;IACH;IAEA,MAAa,mBAAmB,MAAA,EAAsB;QACpD,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,gBAAgB,MAAA,EAA+B;QAC1D,MAAM,EAAE,mBAAA,CAAoB,CAAA,GAAI;QAChC,MAAM,uBAAuB,oBAAoB,SAAS,OAAO,cAAA,GAAiB,OAAO,IAAA;QACzF,IAAA,CAAK,SAAA,CAAU,oBAAoB;QAEnC,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAM,UAAUA,YAAU,oBAAoB;YAC9C,aAAa;gBACX;YACF;QACF,CAAC;IACH;IAEA,MAAa,mBAAmB,cAAA,EAAwB,MAAA,EAAsB;QAC5E,IAAA,CAAK,SAAA,CAAU,cAAc;QAC7B,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAM,UAAUA,YAAU,cAAc;YACxC,YAAY;QACd,CAAC;IACH;IAEA,MAAa,uBAAuB,cAAA,EAAwB,MAAA,EAA0B;QACpF,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,MAAM,WAAW,yTAAI,UAAA,CAAQ,QAAA,CAAS;QACtC,SAAS,MAAA,CAAO,QAAQ,QAAQ,IAAI;QACpC,IAAI,QAAQ,gBAAgB;YAC1B,SAAS,MAAA,CAAO,oBAAoB,QAAQ,cAAc;QAC5D;QAEA,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,MAAM;YAChD;QACF,CAAC;IACH;IAEA,MAAa,uBAAuB,cAAA,EAAwB;QAC1D,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,MAAM;QAClD,CAAC;IACH;IAEA,MAAa,2BAA2B,cAAA,EAAwB,MAAA,EAA8B;QAC5F,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,UAAU;YACpD,YAAY;QACd,CAAC;IACH;IAEA,MAAa,mBAAmB,cAAA,EAAwB;QACtD,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAM,UAAUA,YAAU,cAAc;QAC1C,CAAC;IACH;IAEA,MAAa,8BAA8B,MAAA,EAA6C;QACtF,MAAM,EAAE,cAAA,EAAgB,GAAG,YAAY,CAAA,GAAI;QAC3C,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAA6D;YACvE,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,aAAa;YACvD;QACF,CAAC;IACH;IAEA,MAAa,sCAAsC,MAAA,EAAqD;QACtG,OAAO,IAAA,CAAK,OAAA,CAA6D;YACvE,QAAQ;YACR,MAAM;YACN,aAAa;QACf,CAAC;IACH;IAEA,MAAa,6BAA6B,MAAA,EAA4C;QACpF,MAAM,EAAE,cAAA,EAAgB,GAAG,WAAW,CAAA,GAAI;QAC1C,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAgC;YAC1C,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,aAAa;YACvD;QACF,CAAC;IACH;IAEA,MAAa,6BAA6B,MAAA,EAA4C;QACpF,MAAM,EAAE,cAAA,EAAgB,MAAA,EAAQ,GAAG,WAAW,CAAA,GAAI;QAClD,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAgC;YAC1C,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,MAAM;YAC/D;QACF,CAAC;IACH;IAEA,MAAa,qCAAqC,MAAA,EAAoD;QACpG,MAAM,EAAE,cAAA,EAAgB,MAAA,EAAQ,GAAG,WAAW,CAAA,GAAI;QAElD,OAAO,IAAA,CAAK,OAAA,CAAgC;YAC1C,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,QAAQ,UAAU;YAC3E;QACF,CAAC;IACH;IAEA,MAAa,6BAA6B,MAAA,EAA4C;QACpF,MAAM,EAAE,cAAA,EAAgB,MAAA,CAAO,CAAA,GAAI;QACnC,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAgC;YAC1C,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,MAAM;QACjE,CAAC;IACH;IAEA,MAAa,8BAA8B,MAAA,EAA6C;QACtF,MAAM,EAAE,cAAA,EAAgB,GAAG,YAAY,CAAA,GAAI;QAC3C,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAA6D;YACvE,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,aAAa;YACvD;QACF,CAAC;IACH;IAEA,MAAa,6BAA6B,MAAA,EAA4C;QACpF,MAAM,EAAE,cAAA,EAAgB,GAAG,WAAW,CAAA,GAAI;QAC1C,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAgC;YAC1C,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,aAAa;YACvD;QACF,CAAC;IACH;IAEA,MAAa,iCACX,cAAA,EACA,MAAA,EACA;QACA,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAkC;YAC5C,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,MAAM;YAC/D,YAAY;QACd,CAAC;IACH;IAEA,MAAa,0BAA0B,MAAA,EAAyC;QAC9E,MAAM,EAAE,cAAA,EAAgB,YAAA,CAAa,CAAA,GAAI;QACzC,IAAA,CAAK,SAAA,CAAU,cAAc;QAC7B,IAAA,CAAK,SAAA,CAAU,YAAY;QAE3B,OAAO,IAAA,CAAK,OAAA,CAAgC;YAC1C,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,YAAY;QACvE,CAAC;IACH;IAEA,MAAa,6BAA6B,MAAA,EAA4C;QACpF,MAAM,EAAE,cAAA,EAAgB,YAAA,EAAc,GAAG,WAAW,CAAA,GAAI;QACxD,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAgC;YAC1C,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,eAAe,cAAc,QAAQ;YAC/E;QACF,CAAC;IACH;IAEA,MAAa,0BAA0B,MAAA,EAAyC;QAC9E,MAAM,EAAE,cAAA,EAAgB,GAAG,YAAY,CAAA,GAAI;QAC3C,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAAyD;YACnE,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,SAAS;YACnD;QACF,CAAC;IACH;IAEA,MAAa,yBAAyB,MAAA,EAAwC;QAC5E,MAAM,EAAE,cAAA,EAAgB,GAAG,WAAW,CAAA,GAAI;QAC1C,IAAA,CAAK,SAAA,CAAU,cAAc;QAE7B,OAAO,IAAA,CAAK,OAAA,CAA4B;YACtC,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,SAAS;YACnD,YAAY;gBACV,GAAG,UAAA;gBACH,UAAU,WAAW,QAAA,IAAY;YACnC;QACF,CAAC;IACH;IAEA,MAAa,yBAAyB,MAAA,EAAwC;QAC5E,MAAM,EAAE,cAAA,EAAgB,QAAA,EAAU,GAAG,WAAW,CAAA,GAAI;QACpD,IAAA,CAAK,SAAA,CAAU,cAAc;QAC7B,IAAA,CAAK,SAAA,CAAU,QAAQ;QAEvB,OAAO,IAAA,CAAK,OAAA,CAA4B;YACtC,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,WAAW,QAAQ;YAC7D;QACF,CAAC;IACH;IAEA,MAAa,yBAAyB,MAAA,EAAwC;QAC5E,MAAM,EAAE,cAAA,EAAgB,QAAA,CAAS,CAAA,GAAI;QACrC,IAAA,CAAK,SAAA,CAAU,cAAc;QAC7B,IAAA,CAAK,SAAA,CAAU,QAAQ;QAEvB,OAAO,IAAA,CAAK,OAAA,CAA4B;YACtC,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB,WAAW,QAAQ;QAC/D,CAAC;IACH;AACF;;AC3cA,IAAMC,aAAW;AA8BV,IAAM,uBAAN,cAAmC,YAAY;IACpD,MAAa,KAAK,SAAiC,CAAC,CAAA,EAAG;QACrD,OAAO,IAAA,CAAK,OAAA,CAAuD;YACjE,QAAQ;YACR,MAAMA;YACN,aAAa;QACf,CAAC;IACH;IAEA,MAAa,IAAI,kBAAA,EAA4B;QAC3C,IAAA,CAAK,SAAA,CAAU,kBAAkB;QAEjC,OAAO,IAAA,CAAK,OAAA,CAA0B;YACpC,QAAQ;YACR,MAAM,UAAUA,YAAU,kBAAkB;QAC9C,CAAC;IACH;IAEA,MAAa,OAAO,MAAA,EAAsC;QACxD,OAAO,IAAA,CAAK,OAAA,CAA0B;YACpC,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,OAAO,MAAA,EAAsC;QACxD,MAAM,EAAE,kBAAA,EAAoB,GAAG,WAAW,CAAA,GAAI;QAE9C,IAAA,CAAK,SAAA,CAAU,kBAAkB;QAEjC,OAAO,IAAA,CAAK,OAAA,CAA0B;YACpC,QAAQ;YACR,MAAM,UAAUA,YAAU,kBAAkB;YAC5C;QACF,CAAC;IACH;IAEA,MAAa,OAAO,kBAAA,EAA4B;QAC9C,IAAA,CAAK,SAAA,CAAU,kBAAkB;QAEjC,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,YAAU,kBAAkB;QAC9C,CAAC;IACH;IAEA,MAAa,aAAa,kBAAA,EAA4B;QACpD,IAAA,CAAK,SAAA,CAAU,kBAAkB;QAEjC,OAAO,IAAA,CAAK,OAAA,CAA0B;YACpC,QAAQ;YACR,MAAM,UAAUA,YAAU,oBAAoB,eAAe;QAC/D,CAAC;IACH;AACF;;ACzFA,IAAMC,aAAW;AAgBV,IAAM,iBAAN,cAA6B,YAAY;IAC9C,MAAa,eAAe,aAAA,EAAuB;QACjD,IAAA,CAAK,SAAA,CAAU,aAAa;QAE5B,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAM,UAAUA,YAAU,aAAa;QACzC,CAAC;IACH;IAEA,MAAa,kBAAkB,MAAA,EAAiC;QAC9D,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,kBAAkB,aAAA,EAAuB,SAAkC,CAAC,CAAA,EAAG;QAC1F,IAAA,CAAK,SAAA,CAAU,aAAa;QAE5B,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAM,UAAUA,YAAU,aAAa;YACvC,YAAY;QACd,CAAC;IACH;IAEA,MAAa,kBAAkB,aAAA,EAAuB;QACpD,IAAA,CAAK,SAAA,CAAU,aAAa;QAE5B,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,YAAU,aAAa;QACzC,CAAC;IACH;AACF;;ACrDA,IAAMC,aAAW;AAOV,IAAM,gBAAN,cAA4B,YAAY;IAC7C,MAAa,OAAO,MAAA,EAAsB;QACxC,OAAO,IAAA,CAAK,OAAA,CAAoB;YAC9B,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;AACF;;ACbA,IAAMC,aAAW;AAMV,IAAM,iBAAN,cAA6B,YAAY;IAC9C,MAAa,qBAAqB;QAChC,OAAO,IAAA,CAAK,OAAA,CAAkD;YAC5D,QAAQ;YACR,MAAMA;YACN,aAAa;gBAAE,WAAW;YAAK;QACjC,CAAC;IACH;IAEA,MAAa,eAAe,aAAA,EAAuB;QACjD,IAAA,CAAK,SAAA,CAAU,aAAa;QAC5B,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAM,UAAUA,YAAU,aAAa;QACzC,CAAC;IACH;IAEA,MAAa,kBAAkB,MAAA,EAAiC;QAC9D,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,kBAAkB,aAAA,EAAuB;QACpD,IAAA,CAAK,SAAA,CAAU,aAAa;QAC5B,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAM,UAAUA,YAAU,aAAa;QACzC,CAAC;IACH;AACF;;ACrCA,IAAMC,aAAW;AA8CV,IAAM,oBAAN,cAAgC,YAAY;IACjD,MAAa,sBAAsB,SAAmC,CAAC,CAAA,EAAG;QACxE,OAAO,IAAA,CAAK,OAAA,CAA0B;YACpC,QAAQ;YACR,MAAMA;YACN,aAAa;QACf,CAAC;IACH;IAEA,MAAa,qBAAqB,MAAA,EAAoC;QACpE,OAAO,IAAA,CAAK,OAAA,CAAwB;YAClC,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,kBAAkB,gBAAA,EAA0B;QACvD,IAAA,CAAK,SAAA,CAAU,gBAAgB;QAC/B,OAAO,IAAA,CAAK,OAAA,CAAwB;YAClC,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB;QAC5C,CAAC;IACH;IAEA,MAAa,qBAAqB,gBAAA,EAA0B,SAAqC,CAAC,CAAA,EAAG;QACnG,IAAA,CAAK,SAAA,CAAU,gBAAgB;QAE/B,OAAO,IAAA,CAAK,OAAA,CAAwB;YAClC,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB;YAC1C,YAAY;QACd,CAAC;IACH;IACA,MAAa,qBAAqB,gBAAA,EAA0B;QAC1D,IAAA,CAAK,SAAA,CAAU,gBAAgB;QAC/B,OAAO,IAAA,CAAK,OAAA,CAAwB;YAClC,QAAQ;YACR,MAAM,UAAUA,YAAU,gBAAgB;QAC5C,CAAC;IACH;AACF;;ACpFA,IAAMC,aAAW;AAsBV,IAAM,aAAN,cAAyB,YAAY;IAC1C,MAAa,eAAe,SAA4B,CAAC,CAAA,EAAG;QAC1D,OAAO,IAAA,CAAK,OAAA,CAA8C;YACxD,QAAQ;YACR,MAAMA;YACN,aAAa;gBAAE,GAAG,MAAA;gBAAQ,WAAW;YAAK;QAC5C,CAAC;IACH;IAEA,MAAa,WAAW,SAAA,EAAmB;QACzC,IAAA,CAAK,SAAA,CAAU,SAAS;QACxB,OAAO,IAAA,CAAK,OAAA,CAAiB;YAC3B,QAAQ;YACR,MAAM,UAAUA,YAAU,SAAS;QACrC,CAAC;IACH;IAEA,MAAa,cAAc,MAAA,EAA6B;QACtD,OAAO,IAAA,CAAK,OAAA,CAAiB;YAC3B,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,cAAc,SAAA,EAAmB;QAC5C,IAAA,CAAK,SAAA,CAAU,SAAS;QACxB,OAAO,IAAA,CAAK,OAAA,CAAiB;YAC3B,QAAQ;YACR,MAAM,UAAUA,YAAU,WAAW,QAAQ;QAC/C,CAAC;IACH;IAEA,MAAa,cAAc,SAAA,EAAmB,KAAA,EAAe;QAC3D,IAAA,CAAK,SAAA,CAAU,SAAS;QACxB,OAAO,IAAA,CAAK,OAAA,CAAiB;YAC3B,QAAQ;YACR,MAAM,UAAUA,YAAU,WAAW,QAAQ;YAC7C,YAAY;gBAAE;YAAM;QACtB,CAAC;IACH;IAEA,MAAa,SAAS,SAAA,EAAmB,QAAA,EAAkB;QACzD,IAAA,CAAK,SAAA,CAAU,SAAS;QACxB,OAAO,IAAA,CAAK,OAAA,CAAe;YACzB,QAAQ;YACR,MAAM,UAAUA,YAAU,WAAW,UAAU,YAAY,EAAE;QAC/D,CAAC;IACH;IAKA,MAAa,eAAe,SAAA,EAAmB,MAAA,EAAsD;QACnG,IAAA,CAAK,SAAA,CAAU,SAAS;QACxB,MAAM,EAAE,gBAAA,EAAkB,GAAG,WAAW,CAAA,GAAI;QAC5C,OAAO,IAAA,CAAK,OAAA,CAAQ;YAClB,QAAQ;YACR,MAAM,UAAUA,YAAU,WAAW,SAAS;YAC9C,YAAY;YACZ,aAAa;gBAAE;YAAiB;QAClC,CAAC;IACH;AACF;;ACrFA,IAAMC,aAAW;AAEV,IAAM,iBAAN,cAA6B,YAAY;IAC9C,MAAa,kBAAkB,MAAA,EAAkC;QAC/D,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,kBAAkB,aAAA,EAAuB;QACpD,IAAA,CAAK,SAAA,CAAU,aAAa;QAC5B,OAAO,IAAA,CAAK,OAAA,CAAqB;YAC/B,QAAQ;YACR,MAAM,UAAUA,YAAU,eAAe,QAAQ;QACnD,CAAC;IACH;AACF;;ACjBA,IAAMC,aAAW;AAEV,IAAM,YAAN,cAAwB,YAAY;IACzC,MAAa,IAAI,eAAA,EAAyB;QACxC,IAAA,CAAK,SAAA,CAAU,eAAe;QAE9B,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,YAAU,eAAe;QAC3C,CAAC;IACH;IAEA,MAAa,OAAO,MAAA,EAA4B;QAC9C,MAAM,EAAE,eAAA,EAAiB,GAAG,WAAW,CAAA,GAAI;QAE3C,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,YAAU,eAAe;YACzC;QACF,CAAC;IACH;AACF;;AC5BA,IAAMC,aAAW;AAEV,IAAM,kBAAN,cAA8B,YAAY;IAC/C,MAAa,qBAAqB;QAChC,OAAO,IAAA,CAAK,OAAA,CAAsB;YAChC,QAAQ;YACR,MAAMA;QACR,CAAC;IACH;AACF;;ACIA,IAAMC,aAAW;AAwLV,IAAM,UAAN,cAAsB,YAAY;IACvC,MAAa,YAAY,SAAyB,CAAC,CAAA,EAAG;QACpD,MAAM,EAAE,KAAA,EAAO,MAAA,EAAQ,OAAA,EAAS,GAAG,gBAAgB,CAAA,GAAI;QAIvD,MAAM,CAAC,MAAM,UAAU,CAAA,GAAI,MAAM,QAAQ,GAAA,CAAI;YAC3C,IAAA,CAAK,OAAA,CAAgB;gBACnB,QAAQ;gBACR,MAAMA;gBACN,aAAa;YACf,CAAC;YACD,IAAA,CAAK,QAAA,CAAS,eAAe;SAC9B;QACD,OAAO;YAAE;YAAM;QAAW;IAC5B;IAEA,MAAa,QAAQ,MAAA,EAAgB;QACnC,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,MAAM;QAClC,CAAC;IACH;IAEA,MAAa,WAAW,MAAA,EAA0B;QAChD,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;IAEA,MAAa,WAAW,MAAA,EAAgB,SAA2B,CAAC,CAAA,EAAG;QACrE,IAAA,CAAK,SAAA,CAAU,MAAM;QAErB,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,MAAM;YAChC,YAAY;QACd,CAAC;IACH;IAEA,MAAa,uBAAuB,MAAA,EAAgB,MAAA,EAA+B;QACjF,IAAA,CAAK,SAAA,CAAU,MAAM;QAErB,MAAM,WAAW,yTAAI,UAAA,CAAQ,QAAA,CAAS;QACtC,SAAS,MAAA,CAAO,QAAQ,QAAQ,IAAI;QAEpC,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,eAAe;YACjD;QACF,CAAC;IACH;IAEA,MAAa,mBAAmB,MAAA,EAAgB,MAAA,EAA4B;QAC1E,IAAA,CAAK,SAAA,CAAU,MAAM;QAErB,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,UAAU;YAC5C,YAAY;QACd,CAAC;IACH;IAEA,MAAa,WAAW,MAAA,EAAgB;QACtC,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,MAAM;QAClC,CAAC;IACH;IAEA,MAAa,SAAS,SAA0B,CAAC,CAAA,EAAG;QAClD,OAAO,IAAA,CAAK,OAAA,CAAgB;YAC1B,QAAQ;YACR,MAAM,UAAUA,YAAU,OAAO;YACjC,aAAa;QACf,CAAC;IACH;IAWA,MAAa,wBAAwB,MAAA,EAAgB,QAAA,EAAoD;QACvG,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,MAAM,YAAY,SAAS,UAAA,CAAW,QAAQ;QAC9C,MAAM,YAAY,YAAY,WAAW,CAAA,MAAA,EAAS,QAAQ,EAAA;QAE1D,IAAI,WAAW;YACb,CAAA,GAAA,kTAAA,CAAA,aAAA,EACE,6CACA;QAEJ;QAEA,OAAO,IAAA,CAAK,OAAA,CAAuD;YACjE,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,uBAAuB,SAAS;YAClE,aAAa;gBAAE,WAAW;YAAK;QACjC,CAAC;IACH;IAEA,MAAa,eAAe,MAAA,EAAgB;QAC1C,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAgB;YAC1B,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,KAAK;QACzC,CAAC;IACH;IAEA,MAAa,8BAA8B,MAAA,EAA6C;QACtF,MAAM,EAAE,MAAA,EAAQ,KAAA,EAAO,MAAA,CAAO,CAAA,GAAI;QAClC,IAAA,CAAK,SAAA,CAAU,MAAM;QAErB,OAAO,IAAA,CAAK,OAAA,CAA6D;YACvE,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,0BAA0B;YAC5D,aAAa;gBAAE;gBAAO;YAAO;QAC/B,CAAC;IACH;IAEA,MAAa,8BAA8B,MAAA,EAA6C;QACtF,MAAM,EAAE,MAAA,EAAQ,GAAG,YAAY,CAAA,GAAI;QACnC,IAAA,CAAK,SAAA,CAAU,MAAM;QAErB,OAAO,IAAA,CAAK,OAAA,CAA6D;YACvE,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,0BAA0B;YAC5D;QACF,CAAC;IACH;IAEA,MAAa,eAAe,MAAA,EAA8B;QACxD,MAAM,EAAE,MAAA,EAAQ,QAAA,CAAS,CAAA,GAAI;QAC7B,IAAA,CAAK,SAAA,CAAU,MAAM;QAErB,OAAO,IAAA,CAAK,OAAA,CAA4B;YACtC,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,iBAAiB;YACnD,YAAY;gBAAE;YAAS;QACzB,CAAC;IACH;IAEA,MAAa,WAAW,MAAA,EAA0B;QAChD,MAAM,EAAE,MAAA,EAAQ,IAAA,CAAK,CAAA,GAAI;QACzB,IAAA,CAAK,SAAA,CAAU,MAAM;QAErB,OAAO,IAAA,CAAK,OAAA,CAA+C;YACzD,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,aAAa;YAC/C,YAAY;gBAAE;YAAK;QACrB,CAAC;IACH;IAEA,MAAa,QAAQ,MAAA,EAAgB;QACnC,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,KAAK;QACzC,CAAC;IACH;IAEA,MAAa,UAAU,MAAA,EAAgB;QACrC,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,OAAO;QAC3C,CAAC;IACH;IAEA,MAAa,SAAS,MAAA,EAAgB;QACpC,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,MAAM;QAC1C,CAAC;IACH;IAEA,MAAa,WAAW,MAAA,EAAgB;QACtC,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,QAAQ;QAC5C,CAAC;IACH;IAEA,MAAa,uBAAuB,MAAA,EAAgB;QAClD,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,eAAe;QACnD,CAAC;IACH;IAEA,MAAa,kBAAkB,MAAA,EAAiC;QAC9D,IAAA,CAAK,SAAA,CAAU,OAAO,MAAM;QAC5B,IAAA,CAAK,SAAA,CAAU,OAAO,uBAAuB;QAC7C,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,YAAU,OAAO,MAAA,EAAQ,YAAY,OAAO,uBAAuB;QACrF,CAAC;IACH;IAEA,MAAa,qBAAqB,MAAA,EAAgC;QAChE,IAAA,CAAK,SAAA,CAAU,OAAO,MAAM;QAC5B,IAAA,CAAK,SAAA,CAAU,OAAO,0BAA0B;QAChD,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,YAAU,OAAO,MAAA,EAAQ,gBAAgB,OAAO,0BAA0B;QAC5F,CAAC;IACH;IAEA,MAAa,0BAA0B,MAAA,EAAyC;QAC9E,IAAA,CAAK,SAAA,CAAU,OAAO,MAAM;QAC5B,IAAA,CAAK,SAAA,CAAU,OAAO,iBAAiB;QACvC,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAM,UAAUA,YAAU,OAAO,MAAA,EAAQ,qBAAqB,OAAO,iBAAiB;QACxF,CAAC;IACH;IAEA,MAAa,sBAAsB,MAAA,EAAgB;QACjD,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAgB;YAC1B,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,aAAa;QACjD,CAAC;IACH;IAEA,MAAa,eAAe,MAAA,EAAgB;QAC1C,IAAA,CAAK,SAAA,CAAU,MAAM;QACrB,OAAO,IAAA,CAAK,OAAA,CAAgB;YAC1B,QAAQ;YACR,MAAM,UAAUA,YAAU,QAAQ,MAAM;QAC1C,CAAC;IACH;AACF;;ACpbA,IAAMC,aAAW;AAgBV,IAAM,mBAAN,cAA+B,YAAY;IAChD,MAAa,KAAK,SAAkC,CAAC,CAAA,EAAG;QACtD,OAAO,IAAA,CAAK,OAAA,CAAkD;YAC5D,QAAQ;YACR,MAAMA;YACN,aAAa;QACf,CAAC;IACH;IAEA,MAAa,OAAO,MAAA,EAAmC;QACrD,OAAO,IAAA,CAAK,OAAA,CAAuB;YACjC,QAAQ;YACR,MAAMA;YACN,YAAY;QACd,CAAC;IACH;AACF;;ACpCA,IAAMC,aAAW;AAEV,IAAM,aAAN,cAAyB,YAAY;IAC1C,MAAa,gBAAgB;QAC3B,OAAO,IAAA,CAAK,OAAA,CAA0B;YACpC,QAAQ;YACR,MAAM,UAAUA,YAAU,MAAM;QAClC,CAAC;IACH;IAEA,MAAa,sBAAsB;QACjC,OAAO,IAAA,CAAK,OAAA,CAA0B;YACpC,QAAQ;YACR,MAAM,UAAUA,YAAU,UAAU;QACtC,CAAC;IACH;IAEA,MAAa,gBAAgB;QAC3B,OAAO,IAAA,CAAK,OAAA,CAAc;YACxB,QAAQ;YACR,MAAM,UAAUA,YAAU,MAAM;QAClC,CAAC;IACH;AACF;;;;AEzBO,IAAM,yBAAN,MAAM,wBAAuB;IAClC,YACW,cAAA,EACA,SAAA,EACA,QAAA,EACA,UAAA,CACT;QAJS,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA0D;QACxE,OAAO,IAAI,wBAAuB,KAAK,eAAA,EAAiB,KAAK,UAAA,EAAY,KAAK,SAAA,EAAW,KAAK,YAAY;IAC5G;AACF;;ACVO,IAAM,aAAN,MAAM,YAAW;IACtB,YACW,EAAA,EACA,MAAA,EACA,MAAA,EACA,KAAA,EACA,KAAA,EACA,GAAA,EACA,SAAA,EACA,SAAA,CACT;QARS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,GAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAkC;QAChD,OAAO,IAAI,YACT,KAAK,EAAA,EACL,KAAK,MAAA,EACL,KAAK,OAAA,EACL,KAAK,KAAA,EACL,KAAK,KAAA,EACL,KAAK,GAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA;IAET;AACF;;ACrBO,IAAM,sBAAN,MAAM,qBAAoB;IAC/B,YAIW,EAAA,EAIA,UAAA,EAIA,cAAA,EAIA,SAAA,EAIA,SAAA,EAIA,UAAA,EAIA,YAAA,CACT;QAzBS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,UAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,UAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAoD;QAClE,OAAO,IAAI,qBACT,KAAK,EAAA,EACL,KAAK,UAAA,EACL,KAAK,eAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,WAAA,EACL,KAAK,aAAA;IAET;AACF;;AC/CO,IAAM,SAAN,MAAM,QAAO;IAClB,YACW,EAAA,EACA,IAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,MAAA,EACA,OAAA,EACA,gBAAA,EACA,OAAA,EACA,UAAA,EACA,SAAA,EACA,WAAA,EACA,UAAA,EACA,SAAA,EACA,SAAA,CACT;QAfS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAkB;QAChC,OAAO,IAAI,QACT,KAAK,EAAA,EACL,KAAK,IAAA,EACL,KAAK,IAAA,EACL,KAAK,OAAA,EACL,KAAK,MAAA,EACL,KAAK,MAAA,EACL,KAAK,OAAA,EACL,KAAK,iBAAA,EACL,KAAK,OAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,WAAA,EACL,KAAK,YAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA;IAET;AACF;;ACrCO,IAAM,sBAAN,MAAM,qBAAoB;IAC/B,YACW,EAAA,EACA,UAAA,EACA,cAAA,EACA,SAAA,EACA,SAAA,EACA,UAAA,CACT;QANS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAoD;QAClE,OAAO,IAAI,qBACT,KAAK,EAAA,EACL,KAAK,UAAA,EACL,KAAK,eAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,WAAA;IAET;AACF;;AClBO,IAAM,kBAAN,MAAM,iBAAgB;IAC3B,YAIW,EAAA,EAIA,QAAA,EAIA,SAAA,EAIA,IAAA,EAIA,OAAA,EAIA,cAAA,EAIA,WAAA,EAIA,UAAA,CACT;QA7BS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,IAAA,GAAA;QAIA,IAAA,CAAA,OAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,WAAA,GAAA;QAIA,IAAA,CAAA,UAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA4C;QAC1D,OAAO,IAAI,iBACT,KAAK,EAAA,EACL,KAAK,SAAA,EACL,KAAK,UAAA,EACL,KAAK,IAAA,EACL,KAAK,OAAA,EACL,KAAK,eAAA,EACL,KAAK,YAAA,EACL,KAAK,WAAA;IAET;AACF;AAKO,IAAM,UAAN,MAAM,SAAQ;IACnB,YAIW,EAAA,EAIA,QAAA,EAIA,MAAA,EAIA,MAAA,EAIA,YAAA,EAIA,QAAA,EAIA,SAAA,EAIA,SAAA,EAIA,SAAA,EAIA,wBAAA,EAIA,cAAA,EAIA,QAAwC,IAAA,CACjD;QA7CS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,wBAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,KAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA4B;QAC1C,OAAO,IAAI,SACT,KAAK,EAAA,EACL,KAAK,SAAA,EACL,KAAK,OAAA,EACL,KAAK,MAAA,EACL,KAAK,cAAA,EACL,KAAK,SAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,2BAAA,EACL,KAAK,eAAA,IAAmB,gBAAgB,QAAA,CAAS,KAAK,eAAe,GACrE,KAAK,KAAA;IAET;AACF;;ACxHO,IAAM,SAAN,MAAM,QAAO;IAClB,YAIW,EAAA,EAIA,UAAA,EAIA,QAAA,EAIA,QAAA,EAIA,QAAA,EAIA,mBAAA,EAIA,SAAA,EAIA,SAAA,CACT;QA7BS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,UAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,mBAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA0B;QACxC,OAAO,IAAI,QACT,KAAK,EAAA,EACL,KAAK,WAAA,EACL,KAAK,QAAA,CAAS,GAAA,CAAI,CAAA,IAAK,QAAQ,QAAA,CAAS,CAAC,CAAC,GAC1C,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,sBAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA;IAET;AACF;;ACpDO,IAAM,cAAN,MAAM,aAAY;IACvB,YACW,IAAA,EACA,KAAA,EACA,QAAA,CACT;QAHS,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAoC;QAClD,OAAO,IAAI,aAAY,KAAK,IAAA,EAAM,KAAK,KAAA,EAAO,KAAK,QAAQ;IAC7D;AACF;;ACVO,IAAMC,WAAN,MAAM,SAAQ;IACnB,YAAqB,OAAA,CAAmB;QAAnB,IAAA,CAAA,OAAA,GAAA;IAAoB;IAEzC,OAAO,SAAS,IAAA,EAA4B;QAC1C,OAAO,IAAI,SAAQ,KAAK,OAAO;IACjC;AACF;;ACNO,IAAM,gBAAN,MAAM,eAAc;IACzB,YACW,MAAA,EACA,EAAA,EACA,IAAA,EACA,OAAA,CACT;QAJS,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAyB;QACvC,OAAO,IAAI,eAAc,KAAK,MAAA,EAAQ,KAAK,EAAA,IAAM,MAAM,KAAK,IAAA,IAAQ,MAAM,KAAK,OAAO;IACxF;AACF;;ACVO,IAAM,SAAN,MAAM,QAAO;IAClB,YACW,EAAA,EACA,IAAA,EACA,WAAA,EACA,cAAA,EACA,iBAAA,EACA,YAAA,EACA,iBAAA,EACA,QAAA,CACT;QARS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,iBAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,iBAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA0B;QACxC,OAAO,IAAI,QACT,KAAK,EAAA,EACL,KAAK,IAAA,EACL,KAAK,YAAA,EACL,KAAK,gBAAA,EACL,KAAK,kBAAA,EACL,KAAK,aAAA,IAAiB,KAAK,aAAA,CAAc,GAAA,CAAI,CAAA,IAAK,YAAY,QAAA,CAAS,CAAC,CAAC,GACzE,KAAK,mBAAA,EACL,KAAK,SAAA;IAET;AACF;;ACzBO,IAAM,QAAN,MAAM,OAAM;IACjB,YACW,EAAA,EACA,aAAA,EACA,cAAA,EACA,cAAA,EACA,OAAA,EACA,IAAA,EACA,SAAA,EACA,MAAA,EACA,IAAA,EACA,IAAA,EACA,gBAAA,CACT;QAXS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,aAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAwB;QACtC,OAAO,IAAI,OACT,KAAK,EAAA,EACL,KAAK,eAAA,EACL,KAAK,gBAAA,EACL,KAAK,gBAAA,EACL,KAAK,OAAA,EACL,KAAK,IAAA,EACL,KAAK,UAAA,EACL,KAAK,MAAA,EACL,KAAK,IAAA,EACL,KAAK,IAAA,EACL,KAAK,kBAAA;IAET;AACF;;AC3BO,IAAM,qBAAN,MAAM,oBAAmB;IAC9B,YAIW,EAAA,EAIA,IAAA,CACT;QALS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,IAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAkD;QAChE,OAAO,IAAI,oBAAmB,KAAK,EAAA,EAAI,KAAK,IAAI;IAClD;AACF;;ACbO,IAAM,eAAN,MAAM,cAAa;IACxB,YAYW,MAAA,EAIA,QAAA,EAIA,kCAA8C,IAAA,EAI9C,WAA0B,IAAA,EAI1B,WAA0B,IAAA,EAI1B,QAAuB,IAAA,EAIvB,UAAyB,IAAA,CAClC;QAzBS,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,+BAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,KAAA,GAAA;QAIA,IAAA,CAAA,OAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAsC;QACpD,OAAO,IAAI,cACT,KAAK,MAAA,EACL,KAAK,QAAA,EACL,KAAK,kCAAA,GAAqC,IAAI,IAAI,KAAK,kCAAkC,IAAI,MAC7F,KAAK,QAAA,EACL,KAAK,SAAA,EACL,KAAK,KAAA;IAET;AACF;;AC9CO,IAAM,eAAN,MAAM,cAAa;IACxB,YAIW,EAAA,EAIA,YAAA,EAIA,YAAA,EAIA,QAAA,CACT;QAbS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAsC;QACpD,OAAO,IAAI,cACT,KAAK,EAAA,EACL,KAAK,aAAA,EACL,KAAK,YAAA,IAAgB,aAAa,QAAA,CAAS,KAAK,YAAY,GAC5D,KAAK,SAAA,CAAU,GAAA,CAAI,CAAA,OAAQ,mBAAmB,QAAA,CAAS,IAAI,CAAC;IAEhE;AACF;;AC/BO,IAAM,kBAAN,MAAM,iBAAgB;IAC3B,YAIW,EAAA,EAIA,QAAA,EAIA,gBAAA,EAIA,UAAA,EAIA,cAAA,EAIA,YAAA,EAIA,SAAA,EAIA,QAAA,EAIA,QAAA,EAIA,QAAA,EAIA,WAAA,EAIA,iBAAiD,CAAC,CAAA,EAIlD,KAAA,EAIA,YAAA,CACT;QArDS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,gBAAA,GAAA;QAIA,IAAA,CAAA,UAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,WAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,KAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA4C;QAC1D,OAAO,IAAI,iBACT,KAAK,EAAA,EACL,KAAK,QAAA,EACL,KAAK,iBAAA,EACL,KAAK,gBAAA,EACL,KAAK,eAAA,EACL,KAAK,aAAA,EACL,KAAK,UAAA,EACL,KAAK,SAAA,EACL,KAAK,SAAA,IAAa,IAClB,KAAK,QAAA,EACL,KAAK,YAAA,EACL,KAAK,eAAA,EACL,KAAK,KAAA,EACL,KAAK,YAAA,IAAgB,aAAa,QAAA,CAAS,KAAK,YAAY;IAEhE;AACF;;ACpFO,IAAM,sBAAN,MAAM,qBAAoB;IAC/B,YACW,EAAA,EACA,QAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,OAAA,EACA,gBAAA,EACA,OAAA,EACA,UAAA,EACA,SAAA,EACA,SAAA,CACT;QAXS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA+B;QAC7C,OAAO,IAAI,qBACT,KAAK,EAAA,EACL,KAAK,SAAA,EACL,KAAK,IAAA,EACL,KAAK,OAAA,EACL,KAAK,MAAA,EACL,KAAK,OAAA,EACL,KAAK,iBAAA,EACL,KAAK,OAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA;IAET;AACF;;AC9BO,IAAM,WAAN,MAAM,UAAS;IACpB,YACW,EAAA,EACA,eAAA,EACA,cAAA,CACT;QAHS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,eAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA8B;QAC5C,OAAO,IAAI,UAAS,KAAK,EAAA,EAAI,KAAK,gBAAA,EAAkB,KAAK,eAAe;IAC1E;AACF;;ACVO,IAAM,uBAAN,MAAM,sBAAqB;IAChC,YACW,SAAA,EACA,SAAA,EACA,sBAAA,EACA,2BAAA,EACA,2BAAA,CACT;QALS,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,sBAAA,GAAA;QACA,IAAA,CAAA,2BAAA,GAAA;QACA,IAAA,CAAA,2BAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAsD;QACpE,OAAO,IAAI,sBACT,KAAK,SAAA,EACL,KAAK,SAAA,EACL,KAAK,wBAAA,EACL,KAAK,8BAAA,EACL,KAAK,+BAAA;IAET;AACF;;AClBO,IAAM,mBAAN,MAAM,kBAAiB;IAC5B,YACW,EAAA,EACA,qBAAA,EACA,gBAAA,EACA,iBAAA,EACA,2BAAA,CACT;QALS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,qBAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;QACA,IAAA,CAAA,iBAAA,GAAA;QACA,IAAA,CAAA,2BAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA8C;QAC5D,OAAO,IAAI,kBACT,KAAK,EAAA,EACL,KAAK,uBAAA,EACL,KAAK,kBAAA,EACL,KAAK,mBAAA,EACL,KAAK,6BAAA;IAET;AACF;;ACdO,IAAM,aAAN,MAAM,YAAW;IAOtB,YAIW,EAAA,EAIA,YAAA,EAIA,cAAA,EAIA,SAAA,EAIA,SAAA,EAIA,MAAA,EAIA,GAAA,EAIA,OAAA,CACT;QA7BS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,GAAA,GAAA;QAIA,IAAA,CAAA,OAAA,GAAA;QAtCX,IAAA,CAAQ,IAAA,GAA8B;IAuCnC;IArCH,IAAW,MAA6B;QACtC,OAAO,IAAA,CAAK,IAAA;IACd;IAqCA,OAAO,SAAS,IAAA,EAAkC;QAChD,MAAM,MAAM,IAAI,YACd,KAAK,EAAA,EACL,KAAK,aAAA,EACL,KAAK,eAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,MAAA,EACL,KAAK,GAAA,EACL,KAAK,OAAA;QAEP,IAAI,IAAA,GAAO;QACX,OAAO;IACT;AACF;;AC5CO,IAAM,aAAa;IACxB,wBAAwB;IACxB,YAAY;IACZ,qBAAqB;IACrB,QAAQ;IACR,qBAAqB;IACrB,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,OAAO;IACP,cAAc;IACd,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,UAAU;IACV,sBAAsB;IACtB,kBAAkB;IAClB,YAAY;IACZ,cAAc;IACd,aAAa;IACb,kBAAkB;IAClB,qBAAqB;IACrB,kBAAkB;IAClB,cAAc;IACd,oBAAoB;IACpB,wBAAwB;IACxB,wBAAwB;IACxB,sBAAsB;IACtB,aAAa;IACb,YAAY;IACZ,aAAa;IACb,aAAa;IACb,gBAAgB;IAChB,SAAS;IACT,eAAe;IACf,aAAa;IACb,eAAe;IACf,YAAY;IACZ,MAAM;IACN,eAAe;IACf,YAAY;IACZ,OAAO;IACP,YAAY;IACZ,cAAc;IACd,MAAM;IACN,YAAY;AACd;;AC9DO,IAAM,eAAN,MAAM,cAAa;IACxB,YACW,EAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,MAAA,EACA,OAAA,EACA,gBAAA,EACA,OAAA,EACA,UAAA,EACA,SAAA,EACA,cAAA,EACA,SAAA,EACA,SAAA,CACT;QAbS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAwB;QACtC,OAAO,IAAI,cACT,KAAK,EAAA,EACL,KAAK,IAAA,EACL,KAAK,OAAA,EACL,KAAK,MAAA,EACL,KAAK,MAAA,EACL,KAAK,OAAA,EACL,KAAK,iBAAA,EACL,KAAK,OAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,eAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA;IAET;AACF;;AClCO,IAAM,cAAN,MAAM,aAAY;IACvB,YACW,EAAA,EACA,IAAA,EACA,MAAA,EACA,QAAA,EACA,gBAAA,EACA,gBAAA,EACA,gBAAA,EACA,SAAA,EACA,SAAA,CACT;QATS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAoC;QAClD,OAAO,IAAI,aACT,KAAK,EAAA,EACL,KAAK,IAAA,EACL,KAAK,MAAA,EACL,KAAK,QAAA,EACL,KAAK,kBAAA,EACL,KAAK,kBAAA,EACL,KAAK,iBAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA;IAET;AACF;;AC1BO,IAAM,mBAAN,MAAM,kBAAiB;IAC5B,YACW,iBAAA,EACA,QAAA,EACA,KAAA,EACA,iBAA0C,CAAC,CAAA,EAC3C,KAAA,EACA,MAAA,EACA,WAAA,EACA,SAAA,CACT;QARS,IAAA,CAAA,iBAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA4B;QAC1C,OAAO,IAAI,kBACT,KAAK,mBAAA,EACL,KAAK,QAAA,EACL,KAAK,KAAA,EACL,KAAK,eAAA,EACL,KAAK,KAAA,IAAS,IACd,KAAK,MAAA,EACL,KAAK,YAAA,EACL,KAAK,UAAA;IAET;AACF;;ACxBO,IAAM,mBAAN,MAAM,kBAAiB;IAC5B,YACW,EAAA,EACA,UAAA,EACA,IAAA,EACA,QAAA,EACA,QAAA,EACA,MAAA,EACA,YAAA,EACA,YAAA,EACA,aAAA,EACA,WAAA,EACA,YAAA,EACA,qBAAA,EACA,SAAA,EACA,SAAA,EACA,YAAA,CACT;QAfS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,aAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,qBAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA4B;QAC1C,OAAO,IAAI,kBACT,KAAK,EAAA,EACL,KAAK,WAAA,EACL,KAAK,IAAA,EACL,KAAK,SAAA,EACL,KAAK,MAAA,EACL,KAAK,MAAA,EACL,KAAK,aAAA,EACL,KAAK,aAAA,EACL,KAAK,eAAA,EACL,KAAK,aAAA,EACL,KAAK,aAAA,EACL,KAAK,uBAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,aAAA;IAET;AACF;;ACnCO,IAAM,eAAN,MAAM,cAAa;IAOxB,YAIW,EAAA,EAIA,IAAA,EAIA,IAAA,EAIA,QAAA,EAIA,QAAA,EAIA,SAAA,EAIA,SAAA,EAIA,iBAAoD,CAAC,CAAA,EAIrD,kBAA+C,CAAC,CAAA,EAIhD,qBAAA,EAIA,kBAAA,EAIA,YAAA,EAIA,SAAA,CACT;QAjDS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,IAAA,GAAA;QAIA,IAAA,CAAA,IAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,eAAA,GAAA;QAIA,IAAA,CAAA,qBAAA,GAAA;QAIA,IAAA,CAAA,kBAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QA1DX,IAAA,CAAQ,IAAA,GAAgC;IA2DrC;IAzDH,IAAW,MAA+B;QACxC,OAAO,IAAA,CAAK,IAAA;IACd;IAyDA,OAAO,SAAS,IAAA,EAAsC;QACpD,MAAM,MAAM,IAAI,cACd,KAAK,EAAA,EACL,KAAK,IAAA,EACL,KAAK,IAAA,EACL,KAAK,SAAA,IAAa,IAClB,KAAK,SAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,eAAA,EACL,KAAK,gBAAA,EACL,KAAK,uBAAA,EACL,KAAK,oBAAA,EACL,KAAK,aAAA,EACL,KAAK,UAAA;QAEP,IAAI,IAAA,GAAO;QACX,OAAO;IACT;AACF;;AChFO,IAAM,yBAAN,MAAM,wBAAuB;IAOlC,YAIW,EAAA,EAIA,YAAA,EAIA,IAAA,EAIA,QAAA,EAIA,cAAA,EAIA,SAAA,EAIA,SAAA,EAIA,SAAA,EAIA,GAAA,EAIA,MAAA,EAIA,iBAAuD,CAAC,CAAA,EAIxD,kBAAyD,CAAC,CAAA,EAI1D,sBAAA,CACT;QAjDS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,IAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,GAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,eAAA,GAAA;QAIA,IAAA,CAAA,sBAAA,GAAA;QA1DX,IAAA,CAAQ,IAAA,GAA0C;IA2D/C;IAzDH,IAAW,MAAyC;QAClD,OAAO,IAAA,CAAK,IAAA;IACd;IAyDA,OAAO,SAAS,IAAA,EAAkC;QAChD,MAAM,MAAM,IAAI,wBACd,KAAK,EAAA,EACL,KAAK,aAAA,EACL,KAAK,IAAA,EACL,KAAK,SAAA,EACL,KAAK,eAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,GAAA,EACL,KAAK,MAAA,EACL,KAAK,eAAA,EACL,KAAK,gBAAA,EACL,KAAK,wBAAA;QAEP,IAAI,IAAA,GAAO;QACX,OAAO;IACT;AACF;;AChFO,IAAM,yBAAN,MAAM,wBAAuB;IAOlC,YAIW,EAAA,EAIA,IAAA,EAIA,WAAA,EAIA,iBAAuD,CAAC,CAAA,EAIxD,kBAAyD,CAAC,CAAA,EAI1D,SAAA,EAIA,SAAA,EAIA,YAAA,EAIA,cAAA,CACT;QAjCS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,IAAA,GAAA;QAIA,IAAA,CAAA,WAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,eAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QA1CX,IAAA,CAAQ,IAAA,GAA0C;IA2C/C;IAzCH,IAAW,MAAyC;QAClD,OAAO,IAAA,CAAK,IAAA;IACd;IAyCA,OAAO,SAAS,IAAA,EAAkC;QAChD,MAAM,MAAM,IAAI,wBACd,KAAK,EAAA,EACL,KAAK,IAAA,EACL,KAAK,WAAA,EACL,KAAK,eAAA,EACL,KAAK,gBAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,aAAa,QAAA,CAAS,KAAK,YAAY,GACvC,qCAAqC,QAAA,CAAS,KAAK,gBAAgB;QAErE,IAAI,IAAA,GAAO;QACX,OAAO;IACT;AACF;AAKO,IAAM,uCAAN,MAAM,sCAAqC;IAChD,YAIW,UAAA,EAIA,SAAA,EAIA,QAAA,EAIA,QAAA,EAIA,QAAA,EAIA,MAAA,CACT;QArBS,IAAA,CAAA,UAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAgD;QAC9D,OAAO,IAAI,sCACT,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,SAAA,EACL,KAAK,SAAA,EACL,KAAK,SAAA,EACL,KAAK,OAAA;IAET;AACF;;AC5GO,IAAM,uBAAN,MAAM,sBAAqB;IAChC,YACW,OAAA,EACA,qBAAA,EACA,eAAA,EACA,qBAAA,EACA,WAAA,EACA,kBAAA,EACA,cAAA,EACA,sBAAA,EACA,kBAAA,CACT;QATS,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,qBAAA,GAAA;QACA,IAAA,CAAA,eAAA,GAAA;QACA,IAAA,CAAA,qBAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,kBAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,sBAAA,GAAA;QACA,IAAA,CAAA,kBAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAsD;QACpE,OAAO,IAAI,sBACT,KAAK,OAAA,EACL,KAAK,uBAAA,EACL,KAAK,iBAAA,EACL,KAAK,uBAAA,EACL,KAAK,YAAA,EACL,KAAK,oBAAA,EACL,KAAK,eAAA,EACL,KAAK,wBAAA,EACL,KAAK,oBAAA;IAET;AACF;;AClBO,IAAM,cAAN,MAAM,aAAY;IACvB,YAIW,EAAA,EAIA,WAAA,EAIA,uBAAA,EAIA,mBAAA,EAIA,YAAA,EAIA,QAAA,CACT;QArBS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,WAAA,GAAA;QAIA,IAAA,CAAA,uBAAA,GAAA;QAIA,IAAA,CAAA,mBAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAoC;QAClD,OAAO,IAAI,aACT,KAAK,EAAA,EACL,KAAK,YAAA,EACL,KAAK,0BAAA,EACL,KAAK,qBAAA,EACL,KAAK,YAAA,IAAgB,aAAa,QAAA,CAAS,KAAK,YAAY,GAC5D,KAAK,SAAA,CAAU,GAAA,CAAI,CAAA,OAAQ,mBAAmB,QAAA,CAAS,IAAI,CAAC;IAEhE;AACF;;AC/CO,IAAM,aAAN,MAAM,YAAW;IACtB,YACW,EAAA,EACA,QAAA,EACA,SAAA,EACA,QAAA,EACA,UAAA,EACA,SAAA,EACA,SAAA,CACT;QAPS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAkC;QAChD,OAAO,IAAI,YACT,KAAK,EAAA,EACL,KAAK,SAAA,EACL,KAAK,WAAA,EACL,KAAK,SAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA;IAET;AACF;;ACjBO,IAAM,cAAN,MAAM,aAAY;IACvB,YAIW,EAAA,EAMA,GAAA,EAIA,SAAA,EAIA,SAAA,CACT;QAfS,IAAA,CAAA,EAAA,GAAA;QAMA,IAAA,CAAA,GAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAoC;QAClD,OAAO,IAAI,aAAY,KAAK,EAAA,EAAI,KAAK,GAAA,EAAK,KAAK,UAAA,EAAY,KAAK,UAAU;IAC5E;AACF;;AC3BO,IAAM,iBAAN,MAAM,gBAAe;IAC1B,YAIW,EAAA,EAIA,IAAA,EAIA,MAAA,EAIA,cAAA,EAIA,WAAA,EAIA,SAAA,EAIA,cAAA,EAIA,cAAA,EAIA,WAAA,EAIA,MAAA,EAIA,UAAA,EAIA,aAAA,EAIA,MAAA,EAIA,QAAA,EAIA,SAAA,EAIA,kBAAA,EAIA,eAAA,EAIA,iBAAA,EAIA,SAAA,EAIA,SAAA,EAIA,gBAAA,CACT;QAjFS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,IAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,WAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,WAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,UAAA,GAAA;QAIA,IAAA,CAAA,aAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,kBAAA,GAAA;QAIA,IAAA,CAAA,eAAA,GAAA;QAIA,IAAA,CAAA,iBAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,gBAAA,GAAA;IACR;IACH,OAAO,SAAS,IAAA,EAA0C;QACxD,OAAO,IAAI,gBACT,KAAK,EAAA,EACL,KAAK,IAAA,EACL,KAAK,MAAA,EACL,KAAK,eAAA,EACL,KAAK,aAAA,EACL,KAAK,WAAA,EACL,KAAK,eAAA,EACL,KAAK,gBAAA,EACL,KAAK,YAAA,EACL,KAAK,OAAA,EACL,KAAK,YAAA,EACL,KAAK,eAAA,EACL,KAAK,MAAA,EACL,KAAK,QAAA,EACL,KAAK,UAAA,EACL,KAAK,oBAAA,EACL,KAAK,gBAAA,EACL,KAAK,mBAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,iBAAA,IAAqB,iBAAiB,QAAA,CAAS,KAAK,iBAAiB;IAE9E;AACF;AAEO,IAAM,wBAAN,MAAM,uBAAsB;IACjC,YACW,EAAA,EACA,IAAA,EACA,MAAA,EACA,MAAA,EACA,QAAA,EACA,kBAAA,EACA,eAAA,EACA,iBAAA,EACA,SAAA,EACA,SAAA,CACT;QAVS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,kBAAA,GAAA;QACA,IAAA,CAAA,eAAA,GAAA;QACA,IAAA,CAAA,iBAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;IACR;IACH,OAAO,SAAS,IAAA,EAAwD;QACtE,OAAO,IAAI,uBACT,KAAK,EAAA,EACL,KAAK,IAAA,EACL,KAAK,MAAA,EACL,KAAK,MAAA,EACL,KAAK,QAAA,EACL,KAAK,oBAAA,EACL,KAAK,gBAAA,EACL,KAAK,mBAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA;IAET;AACF;AAEA,IAAM,mBAAN,MAAM,kBAAiB;IACrB,YAIW,MAAA,EAIA,YAAA,EAIA,SAAA,EAIA,QAAA,CACT;QAbS,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA8C;QAC5D,OAAO,IAAI,kBAAiB,KAAK,OAAA,EAAS,KAAK,aAAA,EAAe,KAAK,UAAA,EAAY,KAAK,SAAS;IAC/F;AACF;;ACpKO,IAAM,cAAN,MAAM,aAAY;IACvB,YAIW,EAAA,EAIA,QAAA,EAIA,cAAA,EAIA,MAAA,EAIA,YAAA,EAIA,SAAA,EAIA,QAAA,EAIA,YAAA,EAIA,cAAA,CACT;QAjCS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAoC;QAClD,OAAO,IAAI,aACT,KAAK,EAAA,EACL,KAAK,QAAA,EACL,KAAK,gBAAA,EACL,KAAK,MAAA,EACL,KAAK,aAAA,EACL,KAAK,UAAA,EACL,KAAK,SAAA,EACL,KAAK,YAAA,IAAgB,aAAa,QAAA,CAAS,KAAK,YAAY,GAC5D,KAAK,eAAA,IAAmB,sBAAsB,QAAA,CAAS,KAAK,eAAe;IAE/E;AACF;;AC1DO,IAAM,cAAN,MAAM,aAAY;IACvB,YACW,EAAA,EACA,MAAA,EACA,KAAA,EACA,MAAA,EACA,GAAA,EACA,SAAA,EACA,SAAA,CACT;QAPS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,GAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAoC;QAClD,OAAO,IAAI,aAAY,KAAK,EAAA,EAAI,KAAK,OAAA,EAAS,KAAK,KAAA,EAAO,KAAK,MAAA,EAAQ,KAAK,GAAA,EAAK,KAAK,UAAA,EAAY,KAAK,UAAU;IACnH;AACF;;ACXO,IAAM,4BAAN,MAAM,2BAA0B;IACrC,YACW,UAAA,EACA,mBAAA,CACT;QAFS,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,mBAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAyD;QACvE,OAAO,IAAI,2BAA0B,KAAK,WAAA,EAAa,KAAK,oBAAoB;IAClF;AACF;AAEO,IAAM,6BAAN,MAAM,4BAA2B;IACtC,YACW,YAAA,EACA,WAAA,EACA,UAAA,EACA,eAAA,CACT;QAJS,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,eAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAA2D;QACzE,OAAO,IAAI,4BACT,KAAK,aAAA,IAAiB,0BAA0B,QAAA,CAAS,KAAK,aAAa,GAC3E,KAAK,YAAA,IAAgB,0BAA0B,QAAA,CAAS,KAAK,YAAY,GACzE,KAAK,WAAA,IAAe,0BAA0B,QAAA,CAAS,KAAK,WAAW,GACvE,KAAK,gBAAA;IAET;AACF;AAEO,IAAM,gBAAN,MAAM,eAAc;IACzB,YACW,EAAA,EACA,MAAA,EACA,cAAA,EACA,cAAA,EACA,aAAA,EACA,gBAAA,EACA,aAAA,EACA,QAAA,EACA,YAAA,EACA,WAAA,EACA,UAAA,EACA,eAAA,EACA,SAAA,EACA,QAAA,EACA,YAAA,EACA,UAAA,EACA,gBAAA,EACA,aAAA,EACA,SAAA,EACA,eAAA,EACA,cAAA,EACA,cAAA,CACT;QAtBS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,aAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;QACA,IAAA,CAAA,aAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,eAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,gBAAA,GAAA;QACA,IAAA,CAAA,aAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,eAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;QACA,IAAA,CAAA,cAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAiC;QAC/C,OAAO,IAAI,eACT,KAAK,EAAA,EACL,KAAK,MAAA,EACL,KAAK,eAAA,EACL,KAAK,eAAA,EACL,KAAK,cAAA,EACL,KAAK,iBAAA,EACL,KAAK,aAAA,GAAgB,2BAA2B,QAAA,CAAS,KAAK,aAAa,IAAI,MAC/E,KAAK,QAAA,EACL,KAAK,aAAA,EACL,KAAK,YAAA,EACL,KAAK,WAAA,EACL,KAAK,gBAAA,EACL,KAAK,UAAA,EACL,KAAK,SAAA,EACL,KAAK,aAAA,EACL,KAAK,WAAA,EACL,KAAK,kBAAA,EACL,KAAK,eAAA,EACL,KAAK,UAAA,EACL,KAAK,iBAAA,EACL,KAAK,eAAA,EACL,KAAK,eAAA;IAET;AACF;;ACpFO,IAAM,aAAN,MAAM,YAAW;IACtB,YACW,EAAA,EACA,eAAA,EACA,aAAA,EACA,OAAA,EACA,MAAA,EACA,aAAA,EACA,IAAA,CACT;QAPS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,eAAA,GAAA;QACA,IAAA,CAAA,aAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,aAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAkC;QAChD,OAAO,IAAI,YACT,KAAK,EAAA,EACL,KAAK,iBAAA,EACL,KAAK,eAAA,EACL,KAAK,OAAA,EACL,KAAK,MAAA,EACL,KAAK,eAAA,EACL,KAAK,IAAA;IAET;AACF;;ACtBO,IAAM,QAAN,MAAM,OAAM;IACjB,YAAqB,GAAA,CAAa;QAAb,IAAA,CAAA,GAAA,GAAA;IAAc;IAEnC,OAAO,SAAS,IAAA,EAAwB;QACtC,OAAO,IAAI,OAAM,KAAK,GAAG;IAC3B;AACF;;ACAO,IAAM,aAAN,MAAM,YAAW;IACtB,YAIW,EAAA,EAIA,UAAA,EAIA,YAAA,CACT;QATS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,UAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAkC;QAChD,OAAO,IAAI,YAAW,KAAK,EAAA,EAAI,KAAK,WAAA,EAAa,KAAK,YAAA,IAAgB,aAAa,QAAA,CAAS,KAAK,YAAY,CAAC;IAChH;AACF;;ACjBO,IAAM,OAAN,MAAM,MAAK;IAOhB,YAIW,EAAA,EAIA,eAAA,EAIA,WAAA,EAIA,iBAAA,EAIA,gBAAA,EAIA,MAAA,EAIA,MAAA,EAIA,SAAA,EAIA,SAAA,EAIA,QAAA,EAIA,QAAA,EAIA,qBAAA,EAIA,oBAAA,EAIA,mBAAA,EAIA,YAAA,EAIA,UAAA,EAIA,QAAA,EAIA,SAAA,EAIA,QAAA,EAIA,iBAAqC,CAAC,CAAA,EAItC,kBAAuC,CAAC,CAAA,EAIxC,iBAAqC,CAAC,CAAA,EAItC,iBAAiC,CAAC,CAAA,EAIlC,eAA8B,CAAC,CAAA,EAI/B,cAA4B,CAAC,CAAA,EAI7B,mBAAsC,CAAC,CAAA,EAIvC,eAA8B,CAAC,CAAA,EAI/B,YAAA,EAIA,yBAAA,EAIA,2BAA0C,IAAA,EAI1C,iBAAA,EAIA,eAAA,CACT;QA7HS,IAAA,CAAA,EAAA,GAAA;QAIA,IAAA,CAAA,eAAA,GAAA;QAIA,IAAA,CAAA,WAAA,GAAA;QAIA,IAAA,CAAA,iBAAA,GAAA;QAIA,IAAA,CAAA,gBAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,MAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,qBAAA,GAAA;QAIA,IAAA,CAAA,oBAAA,GAAA;QAIA,IAAA,CAAA,mBAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,UAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,SAAA,GAAA;QAIA,IAAA,CAAA,QAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,eAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,cAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,WAAA,GAAA;QAIA,IAAA,CAAA,gBAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,YAAA,GAAA;QAIA,IAAA,CAAA,yBAAA,GAAA;QAIA,IAAA,CAAA,wBAAA,GAAA;QAIA,IAAA,CAAA,iBAAA,GAAA;QAIA,IAAA,CAAA,eAAA,GAAA;QAtIX,IAAA,CAAQ,IAAA,GAAwB;IAuI7B;IArIH,IAAW,MAAuB;QAChC,OAAO,IAAA,CAAK,IAAA;IACd;IAqIA,OAAO,SAAS,IAAA,EAAsB;QACpC,MAAM,MAAM,IAAI,MACd,KAAK,EAAA,EACL,KAAK,gBAAA,EACL,KAAK,YAAA,EACL,KAAK,mBAAA,EACL,KAAK,kBAAA,EACL,KAAK,MAAA,EACL,KAAK,MAAA,EACL,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,SAAA,EACL,KAAK,SAAA,EACL,KAAK,wBAAA,EACL,KAAK,uBAAA,EACL,KAAK,sBAAA,EACL,KAAK,eAAA,EACL,KAAK,WAAA,EACL,KAAK,QAAA,EACL,KAAK,UAAA,EACL,KAAK,SAAA,EACL,KAAK,eAAA,EACL,KAAK,gBAAA,EACL,KAAK,eAAA,EAAA,CACJ,KAAK,eAAA,IAAmB,CAAC,CAAA,EAAG,GAAA,CAAI,CAAA,IAAK,aAAa,QAAA,CAAS,CAAC,CAAC,GAAA,CAC7D,KAAK,aAAA,IAAiB,CAAC,CAAA,EAAG,GAAA,CAAI,CAAA,IAAK,YAAY,QAAA,CAAS,CAAC,CAAC,GAAA,CAC1D,KAAK,YAAA,IAAgB,CAAC,CAAA,EAAG,GAAA,CAAI,CAAA,IAAK,WAAW,QAAA,CAAS,CAAC,CAAC,GAAA,CACxD,KAAK,iBAAA,IAAqB,CAAC,CAAA,EAAG,GAAA,CAAI,CAAC,IAA2B,gBAAgB,QAAA,CAAS,CAAC,CAAC,GAAA,CACzF,KAAK,aAAA,IAAiB,CAAC,CAAA,EAAG,GAAA,CAAI,CAAC,IAAuB,YAAY,QAAA,CAAS,CAAC,CAAC,GAC9E,KAAK,cAAA,EACL,KAAK,2BAAA,EACL,KAAK,0BAAA,EACL,KAAK,mBAAA,EACL,KAAK,iBAAA;QAEP,IAAI,IAAA,GAAO;QACX,OAAO;IACT;IAAA;;GAAA,GAKA,IAAI,sBAAsB;QACxB,OAAO,IAAA,CAAK,cAAA,CAAe,IAAA,CAAK,CAAC,EAAE,EAAA,CAAG,CAAA,GAAM,OAAO,IAAA,CAAK,qBAAqB,KAAK;IACpF;IAAA;;GAAA,GAKA,IAAI,qBAAqB;QACvB,OAAO,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,CAAC,EAAE,EAAA,CAAG,CAAA,GAAM,OAAO,IAAA,CAAK,oBAAoB,KAAK;IACjF;IAAA;;GAAA,GAKA,IAAI,oBAAoB;QACtB,OAAO,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,CAAC,EAAE,EAAA,CAAG,CAAA,GAAM,OAAO,IAAA,CAAK,mBAAmB,KAAK;IAC/E;IAAA;;GAAA,GAKA,IAAI,WAAW;QACb,OAAO;YAAC,IAAA,CAAK,SAAA;YAAW,IAAA,CAAK,QAAQ;SAAA,CAAE,IAAA,CAAK,GAAG,EAAE,IAAA,CAAK,KAAK;IAC7D;AACF;;AClNO,IAAM,gBAAN,MAAM,eAAc;IACzB,YACW,EAAA,EACA,YAAA,EACA,MAAA,EACA,UAAA,EACA,SAAA,EACA,SAAA,EACA,QAAA,CACT;QAPS,IAAA,CAAA,EAAA,GAAA;QACA,IAAA,CAAA,YAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,SAAA,GAAA;QACA,IAAA,CAAA,QAAA,GAAA;IACR;IAEH,OAAO,SAAS,IAAA,EAAwC;QACtD,OAAO,IAAI,eACT,KAAK,EAAA,EACL,KAAK,aAAA,EACL,KAAK,MAAA,EACL,KAAK,UAAA,IAAc,WAAW,QAAA,CAAS,KAAK,UAAU,GACtD,KAAK,UAAA,EACL,KAAK,UAAA,EACL,KAAK,SAAA;IAET;AACF;;ACuCO,SAAS,YAAqB,OAAA,EAAsE;IACzG,IAAI,MAAM;IAEV,IAAI,MAAM,OAAA,CAAQ,OAAO,GAAG;QAC1B,MAAMC,QAAO,QAAQ,GAAA,CAAI,CAAA,OAAQ,aAAa,IAAI,CAAC;QACnD,OAAO;YAAE,MAAAA;QAAK;IAChB,OAAA,IAAW,YAAY,OAAO,GAAG;QAC/B,OAAO,QAAQ,IAAA,CAAK,GAAA,CAAI,CAAA,OAAQ,aAAa,IAAI,CAAC;QAClD,aAAa,QAAQ,WAAA;QAErB,OAAO;YAAE;YAAM;QAAW;IAC5B,OAAO;QACL,OAAO;YAAE,MAAM,aAAa,OAAO;QAAE;IACvC;AACF;AAEA,SAAS,YAAY,OAAA,EAAoD;IACvE,IAAI,CAAC,WAAW,OAAO,YAAY,YAAY,CAAA,CAAE,UAAU,OAAA,GAAU;QACnE,OAAO;IACT;IAEA,OAAO,MAAM,OAAA,CAAQ,QAAQ,IAAI,KAAK,QAAQ,IAAA,KAAS,KAAA;AACzD;AAEA,SAAS,SAAS,IAAA,EAA6B;IAC7C,OAAO,KAAK,WAAA;AACd;AAGA,SAAS,aAAa,IAAA,EAAgB;IAGpC,IAAI,OAAO,SAAS,YAAY,YAAY,QAAQ,aAAa,MAAM;QACrE,OAAO,cAAc,QAAA,CAAS,IAAI;IACpC;IAEA,OAAQ,KAAK,MAAA,EAAQ;QACnB,KAAK,WAAW,sBAAA;YACd,OAAO,uBAAuB,QAAA,CAAS,IAAI;QAC7C,KAAK,WAAW,UAAA;YACd,OAAO,WAAW,QAAA,CAAS,IAAI;QACjC,KAAK,WAAW,mBAAA;YACd,OAAO,oBAAoB,QAAA,CAAS,IAAI;QAC1C,KAAK,WAAW,MAAA;YACd,OAAO,OAAO,QAAA,CAAS,IAAI;QAC7B,KAAK,WAAW,mBAAA;YACd,OAAO,oBAAoB,QAAA,CAAS,IAAI;QAC1C,KAAK,WAAW,MAAA;YACd,OAAO,OAAO,QAAA,CAAS,IAAI;QAC7B,KAAK,WAAW,OAAA;YACd,OAAOC,SAAQ,QAAA,CAAS,IAAI;QAC9B,KAAK,WAAW,MAAA;YACd,OAAO,OAAO,QAAA,CAAS,IAAI;QAC7B,KAAK,WAAW,YAAA;YACd,OAAO,aAAa,QAAA,CAAS,IAAI;QACnC,KAAK,WAAW,KAAA;YACd,OAAO,MAAM,QAAA,CAAS,IAAI;QAC5B,KAAK,WAAW,mBAAA;YACd,OAAO,oBAAoB,QAAA,CAAS,IAAI;QAC1C,KAAK,WAAW,QAAA;YACd,OAAO,SAAS,QAAA,CAAS,IAAI;QAC/B,KAAK,WAAW,oBAAA;YACd,OAAO,qBAAqB,QAAA,CAAS,IAAI;QAC3C,KAAK,WAAW,gBAAA;YACd,OAAO,iBAAiB,QAAA,CAAS,IAAI;QACvC,KAAK,WAAW,UAAA;YACd,OAAO,WAAW,QAAA,CAAS,IAAI;QACjC,KAAK,WAAW,WAAA;YACd,OAAO,YAAY,QAAA,CAAS,IAAI;QAClC,KAAK,WAAW,YAAA;YACd,OAAO,aAAa,QAAA,CAAS,IAAI;QACnC,KAAK,WAAW,gBAAA;YACd,OAAO,iBAAiB,QAAA,CAAS,IAAI;QACvC,KAAK,WAAW,gBAAA;YACd,OAAO,iBAAiB,QAAA,CAAS,IAAI;QACvC,KAAK,WAAW,YAAA;YACd,OAAO,aAAa,QAAA,CAAS,IAAI;QACnC,KAAK,WAAW,sBAAA;YACd,OAAO,uBAAuB,QAAA,CAAS,IAAI;QAC7C,KAAK,WAAW,sBAAA;YACd,OAAO,uBAAuB,QAAA,CAAS,IAAI;QAC7C,KAAK,WAAW,oBAAA;YACd,OAAO,qBAAqB,QAAA,CAAS,IAAI;QAC3C,KAAK,WAAW,WAAA;YACd,OAAO,YAAY,QAAA,CAAS,IAAI;QAClC,KAAK,WAAW,UAAA;YACd,OAAO,WAAW,QAAA,CAAS,IAAI;QACjC,KAAK,WAAW,WAAA;YACd,OAAO,YAAY,QAAA,CAAS,IAAI;QAClC,KAAK,WAAW,cAAA;YACd,OAAO,eAAe,QAAA,CAAS,IAAI;QACrC,KAAK,WAAW,WAAA;YACd,OAAO,YAAY,QAAA,CAAS,IAAI;QAClC,KAAK,WAAW,aAAA;YACd,OAAO,cAAc,QAAA,CAAS,IAAI;QACpC,KAAK,WAAW,OAAA;YACd,OAAO,QAAQ,QAAA,CAAS,IAAI;QAC9B,KAAK,WAAW,UAAA;YACd,OAAO,WAAW,QAAA,CAAS,IAAI;QACjC,KAAK,WAAW,KAAA;YACd,OAAO,MAAM,QAAA,CAAS,IAAI;QAC5B,KAAK,WAAW,UAAA;YACd,OAAO,SAAS,IAAI;QACtB,KAAK,WAAW,IAAA;YACd,OAAO,KAAK,QAAA,CAAS,IAAI;QAC3B,KAAK,WAAW,aAAA;YACd,OAAO,cAAc,QAAA,CAAS,IAAI;QACpC;YACE,OAAO;IACX;AACF;;A3ClHO,SAAS,aAAa,OAAA,EAA8B;IACzD,MAAM,YAAY,OAAU,mBAAuF;QACjH,MAAM,EACJ,SAAA,EACA,mBAAmB,IAAA,EACnB,SAAS,OAAA,EACT,aAAa,WAAA,EACb,YAAY,UAAA,EACd,GAAI;QACJ,MAAM,EAAE,IAAA,EAAM,MAAA,EAAQ,WAAA,EAAa,YAAA,EAAc,UAAA,EAAY,QAAA,CAAS,CAAA,GAAI;QAE1E,IAAI,kBAAkB;YACpB,qBAAqB,SAAS;QAChC;QAEA,MAAM,MAAM,UAAU,QAAQ,YAAY,IAAI;QAG9C,MAAM,WAAW,IAAI,IAAI,GAAG;QAE5B,IAAI,aAAa;YAEf,MAAM,6OAAwB,UAAA,EAAc;gBAAE,GAAG,WAAA;YAAY,CAAC;YAG9D,KAAA,MAAW,CAAC,KAAK,GAAG,CAAA,IAAK,OAAO,OAAA,CAAQ,qBAAqB,EAAG;gBAC9D,IAAI,KAAK;oBACP;wBAAC,GAAG;qBAAA,CAAE,IAAA,CAAK,EAAE,OAAA,CAAQ,CAAA,IAAK,SAAS,YAAA,CAAa,MAAA,CAAO,KAAK,CAAW,CAAC;gBAC1E;YACF;QACF;QAGA,MAAM,UAA+B;YACnC,qBAAqB;YACrB,cAAc;YACd,GAAG,YAAA;QACL;QAEA,IAAI,WAAW;YACb,QAAQ,aAAA,GAAgB,CAAA,OAAA,EAAU,SAAS,EAAA;QAC7C;QAEA,IAAI;QACJ,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,2TAAM,UAAA,CAAQ,KAAA,CAAM,SAAS,IAAA,EAAM;oBACvC;oBACA;oBACA,MAAM;gBACR,CAAC;YACH,OAAO;gBAEL,OAAA,CAAQ,cAAc,CAAA,GAAI;gBAE1B,MAAM,YAAY,MAAM;oBACtB,MAAM,UAAU,WAAW,SAAS,cAAc,OAAO,IAAA,CAAK,UAAU,EAAE,MAAA,GAAS;oBACnF,IAAI,CAAC,SAAS;wBACZ,OAAO;oBACT;oBAEA,MAAM,aAAa,CAAC,8NAAgD,UAAA,EAAc,QAAQ;4BAAE,MAAM;wBAAM,CAAC;oBAEzG,OAAO;wBACL,MAAM,KAAK,SAAA,CAAU,MAAM,OAAA,CAAQ,UAAU,IAAI,WAAW,GAAA,CAAI,UAAU,IAAI,WAAW,UAAU,CAAC;oBACtG;gBACF;gBAEA,MAAM,2TAAM,UAAA,CAAQ,KAAA,CAAM,SAAS,IAAA,EAAM;oBACvC;oBACA;oBACA,GAAG,UAAU,CAAA;gBACf,CAAC;YACH;YAGA,MAAM,iBACJ,KAAK,WAAW,IAAI,OAAA,EAAS,IAAI,UAAU,OAAA,CAAQ,WAAW,MAAM,UAAU,YAAA,CAAa,IAAA;YAC7F,MAAM,eAAe,MAAA,CAAO,iBAAiB,IAAI,IAAA,CAAK,IAAI,IAAI,IAAA,CAAK,CAAA;YAEnE,IAAI,CAAC,IAAI,EAAA,EAAI;gBACX,OAAO;oBACL,MAAM;oBACN,QAAQ,YAAY,YAAY;oBAChC,QAAQ,KAAK;oBACb,YAAY,KAAK;oBACjB,cAAc,WAAW,cAAc,KAAK,OAAO;oBACnD,YAAY,cAAc,KAAK,OAAO;gBACxC;YACF;YAEA,OAAO;gBACL,GAAG,YAAe,YAAY,CAAA;gBAC9B,QAAQ;YACV;QACF,EAAA,OAAS,KAAK;YACZ,IAAI,eAAe,OAAO;gBACxB,OAAO;oBACL,MAAM;oBACN,QAAQ;wBACN;4BACE,MAAM;4BACN,SAAS,IAAI,OAAA,IAAW;wBAC1B;qBACF;oBACA,cAAc,WAAW,KAAK,KAAK,OAAO;gBAC5C;YACF;YAEA,OAAO;gBACL,MAAM;gBACN,QAAQ,YAAY,GAAG;gBACvB,QAAQ,KAAK;gBACb,YAAY,KAAK;gBACjB,cAAc,WAAW,KAAK,KAAK,OAAO;gBAC1C,YAAY,cAAc,KAAK,OAAO;YACxC;QACF;IACF;IAEA,OAAO,wBAAwB,SAAS;AAC1C;AAIA,SAAS,WAAW,IAAA,EAAe,OAAA,EAA2B;IAC5D,IAAI,QAAQ,OAAO,SAAS,YAAY,oBAAoB,QAAQ,OAAO,KAAK,cAAA,KAAmB,UAAU;QAC3G,OAAO,KAAK,cAAA;IACd;IAEA,MAAM,QAAQ,SAAS,IAAI,QAAQ;IACnC,OAAO,SAAS;AAClB;AAEA,SAAS,cAAc,OAAA,EAAuC;IAC5D,MAAM,aAAa,SAAS,IAAI,aAAa;IAC7C,IAAI,CAAC,WAAY,CAAA;IAEjB,MAAM,QAAQ,SAAS,YAAY,EAAE;IACrC,IAAI,MAAM,KAAK,EAAG,CAAA;IAElB,OAAO;AACT;AAEA,SAAS,YAAY,IAAA,EAAgC;IACnD,IAAI,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,YAAY,MAAM;QAC1D,MAAM,SAAS,KAAK,MAAA;QACpB,OAAO,OAAO,MAAA,GAAS,IAAI,OAAO,GAAA,oTAAI,aAAU,IAAI,CAAC,CAAA;IACvD;IACA,OAAO,CAAC,CAAA;AACV;AAKA,SAAS,wBAAwB,EAAA,EAAgC;IAC/D,OAAO,OAAA,GAAU,SAAS;QACxB,MAAM,EAAE,IAAA,EAAM,MAAA,EAAQ,UAAA,EAAY,MAAA,EAAQ,UAAA,EAAY,YAAA,EAAc,UAAA,CAAW,CAAA,GAAI,MAAM,GAAG,GAAG,IAAI;QACnG,IAAI,QAAQ;YAIV,MAAM,QAAQ,sTAAI,yBAAA,CAAsB,cAAc,IAAI;gBACxD,MAAM,CAAC,CAAA;gBACP;gBACA;gBACA;YACF,CAAC;YACD,MAAM,MAAA,GAAS;YACf,MAAM;QACR;QAEA,IAAI,OAAO,eAAe,aAAa;YACrC,OAAO;gBAAE;gBAAM;YAAW;QAC5B;QAEA,OAAO;IACT;AACF;;A4C3MO,SAAS,uBAAuB,OAAA,EAAkC;IACvE,MAAM,UAAU,aAAa,OAAO;IAEpC,OAAO;QACL,wCAAwC,IAAI,0BAC1C,aAAa;YAAE,GAAG,OAAA;YAAS,kBAAkB;QAAM,CAAC;QAEtD,aAAa,IAAI,cAAc,OAAO;QACtC,sBAAsB,IAAI,uBAAuB,OAAO;QACxD,cAAc,IAAI,gBAAgB,OAAO;QACzC,sBAAsB,IAAI,uBAAuB,OAAO;QACxD,SAAS,IAAI,UAAU,OAAO;QAC9B,SAAS,IAAI,UAAU,OAAO;QAC9B,gBAAgB,IAAI,gBAAgB,OAAO;QAC3C,UAAU,IAAI,YAAY,OAAO;QACjC,aAAa,IAAI,cAAc,OAAO;QAAA,6EAAA;QAAA,6DAAA;QAAA,wEAAA;QAItC,eAAe,IAAI,iBACjB,aAAa;YACX,GAAG,OAAA;YACH,YAAY;QACd,CAAC;QAEH,qBAAqB,IAAI,uBACvB,aAAa;YACX,GAAG,OAAA;YACH,YAAY;QACd,CAAC;QAEH,SAAS,IAAI,WACX,aAAa;YACX,GAAG,OAAA;YACH,YAAY;QACd,CAAC;QAEH,MAAM,IAAI,QAAQ,OAAO;QACzB,cAAc,IAAI,gBAAgB,OAAO;QACzC,mBAAmB,IAAI,qBAAqB,OAAO;QACnD,eAAe,IAAI,gBAAgB,OAAO;QAC1C,cAAc,IAAI,eAAe,OAAO;QACxC,aAAa,IAAI,cAAc,OAAO;QACtC,cAAc,IAAI,eAAe,OAAO;QACxC,iBAAiB,IAAI,kBAAkB,OAAO;QAC9C,UAAU,IAAI,WAAW,OAAO;QAChC,cAAc,IAAI,eAAe,OAAO;QACxC,SAAS,IAAI,UAAU,OAAO;QAC9B,eAAe,IAAI,gBAAgB,OAAO;QAC1C,OAAO,IAAI,QAAQ,OAAO;QAC1B,iBAAiB,IAAI,iBAAiB,OAAO;QAC7C,UAAU,IAAI,WAAW,OAAO;IAClC;AACF;;ACzFO,IAAM,YAAY;IACvB,cAAc;IACd,QAAQ;IACR,cAAc;IACd,YAAY;AACd;;ACDO,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB;AAE9B,IAAM,yBAAyB;IAAC;IAAkB;IAAoB,cAAc;CAAA;AAY7E,SAAS,uBAAuB,KAAA,EAAwB;IAC7D,OAAO,uBAAuB,IAAA,CAAK,CAAA,SAAU,MAAM,UAAA,CAAW,MAAM,CAAC;AACvE;AAaO,SAAS,oBAAoB,KAAA,EAAiC;IACnE,IAAI,MAAM,UAAA,CAAW,gBAAgB,GAAG;QACtC,OAAO,UAAU,YAAA;IACnB;IAEA,IAAI,MAAM,UAAA,CAAW,kBAAkB,GAAG;QACxC,OAAO,UAAU,UAAA;IACnB;IAEA,IAAI,MAAM,UAAA,CAAW,cAAc,GAAG;QACpC,OAAO,UAAU,MAAA;IACnB;IAEA,MAAM,IAAI,MAAM,4BAA4B;AAC9C;AASO,IAAM,sBAAsB,CACjC,WACA,iBACY;IACZ,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO;IACT;IAEA,MAAM,aAAa,MAAM,OAAA,CAAQ,YAAY,IAAI,eAAe;QAAC,YAAY;KAAA;IAC7E,OAAO,WAAW,QAAA,CAAS,SAAS;AACtC;AAQO,SAAS,mBAAmB,IAAA,EAAwC;IACzE,OAAO,SAAS,UAAU,MAAA,IAAU,SAAS,UAAU,YAAA,IAAgB,SAAS,UAAU,UAAA;AAC5F;;A7EsFA,IAAM,cAAc,CAAC,SAA0C;IAC7D,OAAO,MAAM;QACX,MAAM,MAAM;YAAE,GAAG,IAAA;QAAK;QACtB,IAAI,SAAA,GAAA,CAAa,IAAI,SAAA,IAAa,EAAA,EAAI,SAAA,CAAU,GAAG,CAAC;QACpD,IAAI,MAAA,GAAA,CAAU,IAAI,MAAA,IAAU,EAAA,EAAI,SAAA,CAAU,GAAG,CAAC;QAC9C,OAAO;YAAE,GAAG,GAAA;QAAI;IAClB;AACF;AAKO,SAAS,mBACd,mBAAA,EACA,YAAA,EACA,aAAA,EACoB;IACpB,MAAM,EAAE,KAAA,EAAO,SAAA,EAAW,aAAA,EAAe,MAAA,EAAQ,KAAA,EAAO,OAAA,EAAS,OAAA,EAAS,cAAA,EAAgB,qBAAA,CAAsB,CAAA,yTAC9G,kDAAA,EAAgD,aAAa;IAC/D,MAAM,YAAY,uBAAuB,mBAAmB;IAC5D,MAAM,WAAW,eAAe;QAC9B;QACA;QACA,SAAS,OAAA,GAAU,OAAA,CAAU,MAAM,UAAU,QAAA,CAAS,QAAA,CAAS,GAAG,IAAI,CAAA,EAAG,GAAA;IAC3E,CAAC;IACD,OAAO;QACL,WAAW,UAAU,YAAA;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,4TAAK,2BAAA,EAAyB;YAC5B;YACA;YACA;YACA;YACA;YACA,UAAW,cAAc,GAAA,IAAkB;YAC3C,OAAQ,cAAc,GAAA,IAAkB;QAC1C,CAAC;QACD,OAAO,YAAY;YAAE,GAAG,mBAAA;YAAqB;QAAa,CAAC;QAC3D,iBAAiB;IACnB;AACF;AAKO,SAAS,oBACd,SAAA,EACA,oBAAA,EACqB;IACrB,OAAO;QACL,WAAW,UAAU,YAAA;QACrB,eAAe;QACf,WAAW;QACX,eAAe,wBAAwB;QACvC,QAAQ;QACR,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;QACT,gBAAgB;QAChB,uBAAuB;QACvB,UAAU,IAAM,QAAQ,OAAA,CAAQ,IAAI;QACpC,KAAK,IAAM;QACX,OAAO,YAAY,SAAS;QAC5B,iBAAiB;IACnB;AACF;AAKO,SAAS,2BACd,SAAA,EACA,KAAA,EACA,kBAAA,EACA,SAAA,EAC+B;IAC/B,MAAM,aAAa;QACjB,IAAI,mBAAmB,EAAA;QACvB,SAAS,mBAAmB,OAAA;QAC5B,UAAU,IAAM,QAAQ,OAAA,CAAQ,KAAK;QACrC,KAAK,IAAM;QACX,OAAO,YAAY,SAAS;QAC5B,iBAAiB;IACnB;IAMA,OAAQ,WAAW;QACjB,KAAK,UAAU,MAAA;YAAQ;gBACrB,MAAM,SAAS;gBACf,OAAO;oBACL,GAAG,UAAA;oBACH;oBACA,MAAM,OAAO,IAAA;oBACb,QAAQ,OAAO,MAAA;oBACf,QAAQ,OAAO,MAAA;oBACf,QAAQ,OAAO,OAAA,CAAQ,UAAA,CAAW,OAAO,IAAI,OAAO,OAAA,GAAU;oBAC9D,OAAO,OAAO,OAAA,CAAQ,UAAA,CAAW,MAAM,IAAI,OAAO,OAAA,GAAU;gBAC9D;YACF;QACA,KAAK,UAAU,YAAA;YAAc;gBAC3B,MAAM,SAAS;gBACf,OAAO;oBACL,GAAG,UAAA;oBACH;oBACA,MAAM,OAAO,IAAA;oBACb,QAAQ,OAAO,MAAA;oBACf,QAAQ,OAAO,MAAA;oBACf,WAAW,OAAO,OAAA;gBACpB;YACF;QACA,KAAK,UAAU,UAAA;YAAY;gBACzB,MAAM,SAAS;gBACf,OAAO;oBACL,GAAG,UAAA;oBACH;oBACA,QAAQ,OAAO,MAAA;oBACf,QAAQ,OAAO,OAAA;oBACf,UAAU,OAAO,QAAA;gBACnB;YACF;QACA;YACE,MAAM,IAAI,MAAM,CAAA,oBAAA,EAAuB,SAAS,EAAE;IACtD;AACF;AAKO,SAAS,6BACd,SAAA,EACA,SAAA,EACiC;IACjC,MAAM,aAAa;QACjB,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,KAAK,IAAM;QACX,UAAU,IAAM,QAAQ,OAAA,CAAQ,IAAI;QACpC,OAAO,YAAY,SAAS;QAC5B,iBAAiB;IACnB;IAEA,OAAQ,WAAW;QACjB,KAAK,UAAU,MAAA;YAAQ;gBACrB,OAAO;oBACL,GAAG,UAAA;oBACH;oBACA,MAAM;oBACN,QAAQ;oBACR,QAAQ;oBACR,QAAQ;oBACR,OAAO;gBACT;YACF;QACA,KAAK,UAAU,YAAA;YAAc;gBAC3B,OAAO;oBACL,GAAG,UAAA;oBACH;oBACA,MAAM;oBACN,QAAQ;oBACR,QAAQ;oBACR,WAAW;gBACb;YACF;QACA,KAAK,UAAU,UAAA;YAAY;gBACzB,OAAO;oBACL,GAAG,UAAA;oBACH;oBACA,QAAQ;oBACR,QAAQ;oBACR,UAAU;gBACZ;YACF;QACA;YACE,MAAM,IAAI,MAAM,CAAA,oBAAA,EAAuB,SAAS,EAAE;IACtD;AACF;AAKO,SAAS,yBAAiD;IAC/D,OAAO;QACL,iBAAiB;QACjB,WAAW;QACX,UAAU,IAAM,QAAQ,OAAA,CAAQ,IAAI;QACpC,KAAK,IAAM;QACX,OAAO,IAAA,CAAO,CAAC,CAAA;IACjB;AACF;AAWO,IAAM,6BAA6B,CAAoC,QAAc;IAG1F,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAA,EAAK,GAAG,KAAK,CAAA,GAAI;IAC1C,OAAO;AACT;AAMA,IAAM,iBAAiC,CAAA,WAAU;IAC/C,MAAM,EAAE,OAAA,EAAS,YAAA,EAAc,SAAA,CAAU,CAAA,GAAI,UAAU,CAAC;IAExD,OAAO,OAAO,UAAiC,CAAC,CAAA,KAAM;QACpD,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QAEA,IAAI,QAAQ,QAAA,EAAU;YACpB,OAAO,QAAQ,WAAW,QAAQ,QAAQ;QAC5C;QAEA,OAAO;IACT;AACF;AAKO,IAAM,uBAAuB,CAClC,KACA,EAAE,0BAA0B,IAAA,EAAM,GAAG,QAAQ,CAAA,KAC1C;IACH,MAAM,aAAa,mBAAmB,SAAS,IAAI,GAAA,CAAI,IAAA,EAAM,IAAI,OAAO;IAExE,IAAI,2BAA2B,WAAW,aAAA,KAAkB,WAAW;QACrE,OAAO,oBAAoB,SAAS,WAAW,aAAa;IAC9D;IAEA,OAAO;AACT;AAYO,SAAS,8BAA8B,EAC5C,UAAA,EACA,eAAe,UAAU,YAAA,EAC3B,EAGe;IACb,IAAI,iBAAiB,OAAO;QAC1B,OAAO;IACT;IAEA,IAAI,MAAM,OAAA,CAAQ,YAAY,GAAG;QAC/B,IAAI,CAAC,oBAAoB,WAAW,SAAA,EAAW,YAAY,GAAG;YAE5D,OAAO,uBAAuB;QAChC;QACA,OAAO;IACT;IAGA,IAAI,CAAC,oBAAoB,WAAW,SAAA,EAAW,YAAY,GAAG;QAC5D,IAAI,mBAAmB,YAAY,GAAG;YACpC,OAAO,6BAA6B,cAAc,WAAW,KAAK;QACpE;QACA,OAAO,oBAAoB,WAAW,KAAK;IAC7C;IAEA,OAAO;AACT;;A8E1bO,IAAM,aAAa;IACxB,UAAU;IACV,WAAW;IACX,WAAW;AACb;AA2EO,IAAM,kBAAkB;IAC7B,8BAA8B;IAC9B,mBAAmB;IACnB,gBAAgB;IAChB,0BAA0B;IAC1B,6BAA6B;IAC7B,2BAA2B;IAC3B,qBAAqB;IACrB,qBAAqB;IACrB,gCAAgC;IAChC,iBAAiB;IACjB,4BAA4B;IAC5B,8BAA8B;IAC9B,4BAA4B;IAC5B,mBAAmB;IACnB,iBAAiB;AACnB;AAsBO,SAAS,SAA8B,MAAA,EAAkE;IAC9G,MAAM,EAAE,mBAAA,EAAqB,UAAU,IAAI,QAAQ,CAAA,EAAG,KAAA,CAAM,CAAA,GAAI;IAEhE,MAAM,SAAU,CAAC,EAAE,0BAA0B,IAAA,CAAK,CAAA,GAAI,CAAC,CAAA,KAAM;QAC3D,IAAI,OAAO,SAAA,KAAc,UAAU,YAAA,EAAc;YAC/C,MAAM,EAAE,aAAA,CAAc,CAAA,GAAI;YAC1B,MAAM,aAAa,mBAAmB,qBAAqB,OAAO,aAAa;YAE/E,IAAI,2BAA2B,WAAW,aAAA,KAAkB,WAAW;gBACrE,OAAO,oBAAoB,KAAA,GAAW,WAAW,aAAa;YAChE;YAEA,OAAO;QACT;QAEA,MAAM,EAAE,WAAA,CAAY,CAAA,GAAI;QACxB,OAAO,2BAA2B,OAAO,SAAA,EAAW,OAAO,aAAa,mBAAmB;IAC7F;IAEA,OAAO;QACL,QAAQ,WAAW,QAAA;QACnB,QAAQ;QACR,SAAS;QACT,UAAU,oBAAoB,QAAA,IAAY;QAC1C,gBAAgB,oBAAoB,cAAA,IAAkB;QACtD,aAAa,oBAAoB,WAAA,IAAe;QAChD,QAAQ,oBAAoB,MAAA,IAAU;QACtC,WAAW,oBAAoB,SAAA,IAAa;QAC5C,WAAW,oBAAoB,SAAA,IAAa;QAC5C,gBAAgB,oBAAoB,cAAA,IAAkB;QACtD,gBAAgB,oBAAoB,cAAA,IAAkB;QACtD,YAAY;QACZ,iBAAiB;QACjB,WAAW,OAAO,SAAA;QAClB;QACA;QACA;IACF;AACF;AAOO,SAAS,UAA+B,MAAA,EAAqE;IAClH,MAAM,EAAE,mBAAA,EAAqB,UAAU,IAAI,QAAQ,CAAA,EAAG,MAAA,EAAQ,UAAU,EAAA,EAAI,SAAA,CAAU,CAAA,GAAI;IAE1F,MAAM,SAAU,MAAM;QACpB,IAAI,cAAc,UAAU,YAAA,EAAc;YACxC,OAAO,oBAAoB;gBAAE,GAAG,mBAAA;gBAAqB,QAAQ,WAAW,SAAA;gBAAW;gBAAQ;YAAQ,CAAC;QACtG;QAEA,OAAO,6BAA6B,WAAW;YAAE;YAAQ;YAAS;QAAQ,CAAC;IAC7E;IAEA,OAAO,iBAAiB;QACtB,QAAQ,WAAW,SAAA;QACnB;QACA;QACA,UAAU,oBAAoB,QAAA,IAAY;QAC1C,gBAAgB,oBAAoB,cAAA,IAAkB;QACtD,aAAa,oBAAoB,WAAA,IAAe;QAChD,QAAQ,oBAAoB,MAAA,IAAU;QACtC,WAAW,oBAAoB,SAAA,IAAa;QAC5C,WAAW,oBAAoB,SAAA,IAAa;QAC5C,gBAAgB,oBAAoB,cAAA,IAAkB;QACtD,gBAAgB,oBAAoB,cAAA,IAAkB;QACtD,YAAY;QACZ,iBAAiB;QACjB;QACA;QACA;QACA,OAAO;IACT,CAAC;AACH;AAEO,SAAS,UACd,mBAAA,EACA,MAAA,EACA,UAAU,EAAA,EACV,OAAA,EACgB;IAChB,OAAO,iBAAiB;QACtB,QAAQ,WAAW,SAAA;QACnB;QACA;QACA,gBAAgB,oBAAoB,cAAA,IAAkB;QACtD,aAAa,oBAAoB,WAAA,IAAe;QAChD,QAAQ,oBAAoB,MAAA,IAAU;QACtC,UAAU,oBAAoB,QAAA,IAAY;QAC1C,WAAW,oBAAoB,SAAA,IAAa;QAC5C,WAAW,oBAAoB,SAAA,IAAa;QAC5C,gBAAgB,oBAAoB,cAAA,IAAkB;QACtD,gBAAgB,oBAAoB,cAAA,IAAkB;QACtD,YAAY;QACZ,iBAAiB;QACjB,WAAW,UAAU,YAAA;QACrB,QAAQ,IAAM;QACd;QACA,OAAO;IACT,CAAC;AACH;AAEA,IAAM,mBAAmB,CACvB,iBACM;IACN,MAAM,UAAU,IAAI,QAAQ,aAAa,OAAA,IAAW,CAAC,CAAC;IAEtD,IAAI,aAAa,OAAA,EAAS;QACxB,IAAI;YACF,QAAQ,GAAA,CAAI,UAAU,OAAA,CAAQ,WAAA,EAAa,aAAa,OAAO;QACjE,EAAA,OAAQ,CAER;IACF;IAEA,IAAI,aAAa,MAAA,EAAQ;QACvB,IAAI;YACF,QAAQ,GAAA,CAAI,UAAU,OAAA,CAAQ,UAAA,EAAY,aAAa,MAAM;QAC/D,EAAA,OAAQ,CAER;IACF;IAEA,IAAI,aAAa,MAAA,EAAQ;QACvB,IAAI;YACF,QAAQ,GAAA,CAAI,UAAU,OAAA,CAAQ,UAAA,EAAY,aAAa,MAAM;QAC/D,EAAA,OAAQ,CAER;IACF;IAEA,aAAa,OAAA,GAAU;IAEvB,OAAO;AACT;;;AElRA,IAAM,WAAN,cAAuB,IAAI;IAClB,cAAc,KAAA,EAAqB;QACxC,OAAO,IAAA,CAAK,MAAA,KAAW,IAAI,IAAI,MAAM,QAAA,CAAS,CAAC,EAAE,MAAA;IACnD;AACF;AAeO,IAAM,iBAAiB,CAAA,GAAI,SAA2D;IAC3F,OAAO,IAAI,SAAS,GAAG,IAAI;AAC7B;;ADVA,IAAM,eAAN,cAA2B,QAAQ;IAI1B,YAAY,KAAA,EAA6C,IAAA,CAAoB;QAYlF,MAAM,MAAM,OAAO,UAAU,YAAY,SAAS,QAAQ,MAAM,GAAA,GAAM,OAAO,KAAK;QAClF,KAAA,CAAM,KAAK,QAAQ,OAAO,UAAU,WAAW,KAAA,IAAY,KAAK;QAChE,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,oBAAA,CAAqB,IAAI;QAC9C,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,YAAA,CAAa,IAAI;IACvC;IAEO,SAAS;QACd,OAAO;YACL,KAAK,IAAA,CAAK,QAAA,CAAS,IAAA;YACnB,QAAQ,IAAA,CAAK,MAAA;YACb,SAAS,KAAK,SAAA,CAAU,OAAO,WAAA,CAAY,IAAA,CAAK,OAAO,CAAC;YACxD,UAAU,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS;YACjC,SAAS,KAAK,SAAA,CAAU,OAAO,WAAA,CAAY,IAAA,CAAK,OAAO,CAAC;QAC1D;IACF;IAAA;;;GAAA,GAMQ,qBAAqB,GAAA,EAAc;QACzC,MAAM,aAAa,IAAI,IAAI,IAAI,GAAG;QAClC,MAAM,iBAAiB,IAAI,OAAA,CAAQ,GAAA,CAAI,UAAU,OAAA,CAAQ,cAAc;QACvE,MAAM,gBAAgB,IAAI,OAAA,CAAQ,GAAA,CAAI,UAAU,OAAA,CAAQ,aAAa;QACrE,MAAM,OAAO,IAAI,OAAA,CAAQ,GAAA,CAAI,UAAU,OAAA,CAAQ,IAAI;QACnD,MAAM,WAAW,WAAW,QAAA;QAE5B,MAAM,eAAe,IAAA,CAAK,uBAAA,CAAwB,aAAa,KAAK;QACpE,MAAM,mBAAmB,IAAA,CAAK,uBAAA,CAAwB,cAAc,KAAK,UAAU,QAAQ,QAAQ,EAAE;QACrG,MAAM,SAAS,gBAAgB,mBAAmB,GAAG,gBAAgB,CAAA,GAAA,EAAM,YAAY,EAAA,GAAK,WAAW,MAAA;QAEvG,IAAI,WAAW,WAAW,MAAA,EAAQ;YAChC,OAAO,eAAe,UAAU;QAClC;QACA,OAAO,eAAe,WAAW,QAAA,GAAW,WAAW,MAAA,EAAQ,MAAM;IACvE;IAEQ,wBAAwB,KAAA,EAAuB;QACrD,OAAO,OAAO,MAAM,GAAG,CAAA,CAAE,CAAC,CAAA;IAC5B;IAEQ,aAAa,GAAA,EAAc;QACjC,MAAM,uNAAgB,QAAA,EAAM,IAAA,CAAK,iBAAA,CAAkB,IAAI,OAAA,CAAQ,GAAA,CAAI,QAAQ,KAAK,EAAE,CAAC;QACnF,OAAO,IAAI,IAAI,OAAO,OAAA,CAAQ,aAAa,CAAC;IAC9C;IAEQ,kBAAkB,GAAA,EAAa;QACrC,OAAO,MAAM,IAAI,OAAA,CAAQ,oBAAoB,kBAAkB,IAAI;IACrE;AACF;AAEO,IAAM,qBAAqB,CAAA,GAAI,SAAmE;IACvG,OAAO,IAAA,CAAK,CAAC,CAAA,YAAa,eAAe,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,aAAa,GAAG,IAAI;AAC7E;;AEhFO,IAAM,gBAAgB,CAAC,oBAAoC;IAChE,OAAO,gBAAgB,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,EAAG,MAAM,GAAG,CAAA,CAAE,CAAC,CAAA;AACpD;AAEO,IAAM,iBAAiB,CAAC,oBAAoC;IACjE,OAAO,gBAAgB,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,EAAG,MAAM,GAAG,CAAA,CAAE,CAAC,CAAA;AACpD;;ACeA,IAAI,QAAyB,CAAC;AAC9B,IAAI,gBAAgB;AAEpB,SAAS,aAAa,GAAA,EAAa;IACjC,OAAO,KAAA,CAAM,GAAG,CAAA;AAClB;AAEA,SAAS,iBAAiB;IACxB,OAAO,OAAO,MAAA,CAAO,KAAK;AAC5B;AAEA,SAAS,WAAW,GAAA,EAAwB,eAAe,IAAA,EAAM;IAC/D,KAAA,CAAM,IAAI,GAAG,CAAA,GAAI;IACjB,gBAAgB,eAAe,KAAK,GAAA,CAAI,IAAI,CAAA;AAC9C;AAEA,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,aAAa;AAUZ,SAAS,sBAAsB,QAAA,EAA+B;IACnE,IAAI,CAAC,aAAa,WAAW,GAAG;QAC9B,IAAI,CAAC,UAAU;YACb,MAAM,wTAAI,0BAAA,CAAuB;gBAC/B,6TAAQ,+BAAA,CAA6B,cAAA;gBACrC,SAAS;gBACT,6TAAQ,+BAAA,CAA6B,eAAA;YACvC,CAAC;QACH;QAEA,MAAM,UAAU,SACb,OAAA,CAAQ,eAAe,EAAE,EACzB,OAAA,CAAQ,YAAY,EAAE,EACtB,OAAA,CAAQ,aAAa,EAAE,EACvB,OAAA,CAAQ,YAAY,EAAE,EACtB,OAAA,CAAQ,YAAY,EAAE,EACtB,OAAA,CAAQ,OAAO,GAAG,EAClB,OAAA,CAAQ,OAAO,GAAG;QAGrB,WACE;YACE,KAAK;YACL,KAAK;YACL,KAAK;YACL,GAAG;YACH,GAAG;QACL,GACA;IAEJ;IAEA,OAAO,aAAa,WAAW;AACjC;AA6CA,eAAsB,uBAAuB,EAC3C,SAAA,EACA,SAAS,OAAA,EACT,aAAa,WAAA,EACb,GAAA,EACA,aAAA,EACF,EAAuD;IACrD,IAAI,iBAAiB,gBAAgB,KAAK,CAAC,aAAa,GAAG,GAAG;QAC5D,IAAI,CAAC,WAAW;YACd,MAAM,yTAAI,yBAAA,CAAuB;gBAC/B,6TAAQ,+BAAA,CAA6B,cAAA;gBACrC,SAAS;gBACT,6TAAQ,+BAAA,CAA6B,qBAAA;YACvC,CAAC;QACH;QACA,MAAM,UAAU,IAAM,kBAAkB,QAAQ,WAAW,UAAU;QACrE,MAAM,EAAE,IAAA,CAAK,CAAA,GAAI,6TAAM,QAAA,EAAM,OAAO;QAEpC,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAA,EAAQ;YACzB,MAAM,IAAI,8UAAA,CAAuB;gBAC/B,6TAAQ,+BAAA,CAA6B,cAAA;gBACrC,SAAS;gBACT,6TAAQ,+BAAA,CAA6B,qBAAA;YACvC,CAAC;QACH;QAEA,KAAK,OAAA,CAAQ,CAAA,MAAO,WAAW,GAAG,CAAC;IACrC;IAEA,MAAM,MAAM,aAAa,GAAG;IAE5B,IAAI,CAAC,KAAK;QACR,MAAM,cAAc,eAAe;QACnC,MAAM,UAAU,YACb,GAAA,CAAI,CAAAC,OAAOA,KAAI,GAAG,EAClB,IAAA,CAAK,EACL,IAAA,CAAK,IAAI;QAEZ,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,QAAQ,CAAA,2EAAA,uTAA8E,+BAAA,CAA6B,cAAc,CAAA,uBAAA,CAAA;YACjI,SAAS,CAAA,2DAAA,EAA8D,GAAG,CAAA,oLAAA,EAAuL,OAAO,EAAA;YACxQ,6TAAQ,+BAAA,CAA6B,cAAA;QACvC,CAAC;IACH;IAEA,OAAO;AACT;AAEA,eAAe,kBAAkB,MAAA,EAAgB,GAAA,EAAa,UAAA,EAAoB;IAChF,IAAI,CAAC,KAAK;QACR,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,iBAAA;YACrC,SACE;YACF,6TAAQ,+BAAA,CAA6B,qBAAA;QACvC,CAAC;IACH;IAEA,MAAM,MAAM,IAAI,IAAI,MAAM;IAC1B,IAAI,QAAA,GAAW,UAAU,IAAI,QAAA,EAAU,YAAY,OAAO;IAE1D,MAAM,WAAW,2TAAM,UAAA,CAAQ,KAAA,CAAM,IAAI,IAAA,EAAM;QAC7C,SAAS;YACP,eAAe,CAAA,OAAA,EAAU,GAAG,EAAA;YAC5B,qBAAqB;YACrB,gBAAgB;YAChB,cAAc;QAChB;IACF,CAAC;IAED,IAAI,CAAC,SAAS,EAAA,EAAI;QAChB,MAAM,OAAO,MAAM,SAAS,IAAA,CAAK;QACjC,MAAM,wBAAwB,qBAAqB,MAAM,6TAAQ,6BAAA,CAA2B,gBAAgB;QAE5G,IAAI,uBAAuB;YACzB,MAAM,8TAAS,+BAAA,CAA6B,gBAAA;YAE5C,MAAM,wTAAI,0BAAA,CAAuB;gBAC/B,6TAAQ,+BAAA,CAA6B,cAAA;gBACrC,SAAS,sBAAsB,OAAA;gBAC/B;YACF,CAAC;QACH;QAEA,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,cAAA;YACrC,SAAS,CAAA,8BAAA,EAAiC,IAAI,IAAI,CAAA,WAAA,EAAc,SAAS,MAAM,EAAA;YAC/E,6TAAQ,+BAAA,CAA6B,qBAAA;QACvC,CAAC;IACH;IAEA,OAAO,SAAS,IAAA,CAAK;AACvB;AAEA,SAAS,kBAAkB;IAEzB,IAAI,kBAAkB,CAAA,GAAI;QACxB,OAAO;IACT;IAGA,MAAM,YAAY,KAAK,GAAA,CAAI,IAAI,iBAAiB,oCAAoC;IAEpF,IAAI,WAAW;QACb,QAAQ,CAAC;IACX;IAEA,OAAO;AACT;AAQA,IAAM,uBAAuB,CAAC,QAAuB,SAAiB;IACpE,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,OAAO,OAAO,IAAA,CAAK,CAAC,MAAqB,IAAI,IAAA,KAAS,IAAI;AAC5D;;AC9IA,eAAsB,YACpB,KAAA,EACA,OAAA,EAC4D;IAC5D,MAAM,EAAE,MAAM,aAAA,EAAe,MAAA,CAAO,CAAA,4TAAI,YAAA,EAAU,KAAK;IACvD,IAAI,QAAQ;QACV,OAAO;YAAE;QAAO;IAClB;IAEA,MAAM,EAAE,MAAA,CAAO,CAAA,GAAI;IACnB,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI;IAEhB,IAAI;QACF,IAAI;QAEJ,IAAI,QAAQ,MAAA,EAAQ;YAClB,MAAM,sBAAsB,QAAQ,MAAM;QAC5C,OAAA,IAAW,QAAQ,SAAA,EAAW;YAE5B,MAAM,MAAM,uBAAuB;gBAAE,GAAG,OAAA;gBAAS;YAAI,CAAC;QACxD,OAAO;YACL,OAAO;gBACL,QAAQ;oBACN,yTAAI,yBAAA,CAAuB;wBACzB,6TAAQ,+BAAA,CAA6B,cAAA;wBACrC,SAAS;wBACT,6TAAQ,+BAAA,CAA6B,kBAAA;oBACvC,CAAC;iBACH;YACF;QACF;QAEA,OAAO,+TAAM,YAAA,EAAU,OAAO;YAAE,GAAG,OAAA;YAAS;QAAI,CAAC;IACnD,EAAA,OAAS,OAAO;QACd,OAAO;YAAE,QAAQ;gBAAC,KAA+B;aAAA;QAAE;IACrD;AACF;AAQA,SAAS,oBACP,SAAA,EACA,GAAA,EACA,eAAA,EAC4D;IAC5D,2TAAI,0BAAA,EAAwB,GAAG,GAAG;QAChC,IAAI;QACJ,IAAI;QAEJ,OAAQ,IAAI,MAAA,EAAQ;YAClB,KAAK;gBACH,4TAAO,oCAAA,CAAkC,gBAAA;gBACzC,UAAU,IAAI,MAAA,CAAO,CAAC,CAAA,EAAG,WAAW;gBACpC;YACF,KAAK;gBACH,4TAAO,oCAAA,CAAkC,YAAA;gBACzC,UAAU;gBACV;YACF;gBACE,4TAAO,oCAAA,CAAkC,eAAA;gBACzC,UAAU;QACd;QAEA,OAAO;YACL,MAAM,KAAA;YACN;YACA,QAAQ;gBACN,yTAAI,gCAAA,CAA8B;oBAChC;oBACA;oBACA,QAAQ,IAAI,MAAA;gBACd,CAAC;aACH;QACF;IACF;IAEA,OAAO;QACL,MAAM,KAAA;QACN;QACA,QAAQ;YACN,yTAAI,gCAAA,CAA8B;gBAChC,SAAS;gBACT,2TAAM,oCAAA,CAAkC,eAAA;gBACxC,QAAQ,IAAI,MAAA;YACd,CAAC;SACH;IACF;AACF;AAEA,eAAe,mBACb,MAAA,EACA,OAAA,EAC8E;IAC9E,IAAI;QACF,MAAM,SAAS,uBAAuB,OAAO;QAC7C,MAAM,gBAAgB,MAAM,OAAO,aAAA,CAAc,YAAA,CAAa,MAAM;QACpE,OAAO;YAAE,MAAM;YAAe,WAAW,UAAU,YAAA;YAAc,QAAQ,KAAA;QAAU;IACrF,EAAA,OAAS,KAAU;QACjB,OAAO,oBAAoB,UAAU,YAAA,EAAc,KAAK,yBAAyB;IACnF;AACF;AAEA,eAAe,iBACb,WAAA,EACA,OAAA,EACqF;IACrF,IAAI;QACF,MAAM,SAAS,uBAAuB,OAAO;QAC7C,MAAM,gBAAgB,MAAM,OAAO,mBAAA,CAAoB,iBAAA,CAAkB,WAAW;QACpF,OAAO;YAAE,MAAM;YAAe,WAAW,UAAU,UAAA;YAAY,QAAQ,KAAA;QAAU;IACnF,EAAA,OAAS,KAAU;QACjB,OAAO,oBAAoB,UAAU,UAAA,EAAY,KAAK,uBAAuB;IAC/E;AACF;AAEA,eAAe,aACb,MAAA,EACA,OAAA,EACwE;IACxE,IAAI;QACF,MAAM,SAAS,uBAAuB,OAAO;QAC7C,MAAM,gBAAgB,MAAM,OAAO,OAAA,CAAQ,YAAA,CAAa,MAAM;QAC9D,OAAO;YAAE,MAAM;YAAe,WAAW,UAAU,MAAA;YAAQ,QAAQ,KAAA;QAAU;IAC/E,EAAA,OAAS,KAAU;QACjB,OAAO,oBAAoB,UAAU,MAAA,EAAQ,KAAK,mBAAmB;IACvE;AACF;AAQA,eAAsB,uBAAuB,KAAA,EAAe,OAAA,EAA6B;IACvF,IAAI,MAAM,UAAA,CAAW,gBAAgB,GAAG;QACtC,OAAO,mBAAmB,OAAO,OAAO;IAC1C;IACA,IAAI,MAAM,UAAA,CAAW,kBAAkB,GAAG;QACxC,OAAO,iBAAiB,OAAO,OAAO;IACxC;IACA,IAAI,MAAM,UAAA,CAAW,cAAc,GAAG;QACpC,OAAO,aAAa,OAAO,OAAO;IACpC;IAEA,MAAM,IAAI,MAAM,4BAA4B;AAC9C;;ACnPA,eAAe,mBAAmB,KAAA,EAAe,EAAE,GAAA,CAAI,CAAA,EAAuD;IAC5G,MAAM,EAAE,MAAM,OAAA,EAAS,MAAA,CAAO,CAAA,4TAAI,YAAA,EAAU,KAAK;IACjD,IAAI,QAAQ;QACV,MAAM,MAAA,CAAO,CAAC,CAAA;IAChB;IAEA,MAAM,EAAE,MAAA,EAAQ,OAAA,CAAQ,CAAA,GAAI;IAG5B,MAAM,EAAE,GAAA,EAAK,GAAA,CAAI,CAAA,GAAI;IAErB,CAAA,GAAA,oTAAA,CAAA,mBAAA,EAAiB,GAAG;IACpB,CAAA,GAAA,oTAAA,CAAA,wBAAA,EAAsB,GAAG;IAEzB,MAAM,EAAE,MAAM,cAAA,EAAgB,QAAQ,eAAA,CAAgB,CAAA,GAAI,OAAM,4UAAA,EAAkB,SAAS,GAAG;IAC9F,IAAI,iBAAiB;QACnB,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,uBAAA;YACrC,SAAS,CAAA,iCAAA,EAAoC,eAAA,CAAgB,CAAC,CAAC,EAAA;QACjE,CAAC;IACH;IAEA,IAAI,CAAC,gBAAgB;QACnB,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,qBAAA;YACrC,SAAS;QACX,CAAC;IACH;IAEA,OAAO;AACT;AAMA,eAAsB,qBACpB,KAAA,EACA,OAAA,EACkC;IAClC,MAAM,EAAE,SAAA,EAAW,MAAA,EAAQ,UAAA,EAAY,gBAAA,EAAkB,MAAA,EAAQ,aAAA,CAAc,CAAA,GAAI;IAEnF,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,GAAI,qUAAA,EAAU,KAAK;IACxC,IAAI,QAAQ;QACV,MAAM,MAAA,CAAO,CAAC,CAAA;IAChB;IAEA,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI,KAAK,MAAA;IAErB,IAAI;IAEJ,IAAI,QAAQ;QACV,MAAM,sBAAsB,MAAM;IACpC,OAAA,IAAW,WAAW;QAEpB,MAAM,MAAM,uBAAuB;YAAE;YAAW;YAAQ;YAAY;YAAK;YAAkB;QAAc,CAAC;IAC5G,OAAO;QACL,MAAM,yTAAI,yBAAA,CAAuB;YAC/B,6TAAQ,+BAAA,CAA6B,cAAA;YACrC,SAAS;YACT,6TAAQ,+BAAA,CAA6B,kBAAA;QACvC,CAAC;IACH;IAEA,OAAO,MAAM,mBAAmB,OAAO;QACrC;IACF,CAAC;AACH;AAEO,IAAM,mBAAN,MAAuB;IAK5B,YACE,mBAAA,EACA,OAAA,EACA,mBAAA,CACA;QACA,IAAA,CAAK,mBAAA,GAAsB;QAC3B,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,mBAAA,GAAsB;IAC7B;IAAA;;;;;;;GAAA,GAUA,gCAAyC;QACvC,MAAM,EAAE,MAAA,EAAQ,YAAA,CAAa,CAAA,GAAI,IAAA,CAAK,mBAAA;QAItC,IAAI,iBAAiB,cAAc,iBAAiB,UAAU;YAC5D,OAAO;QACT;QAEA,IAAI,CAAC,gBAAgB,QAAQ,WAAW,WAAW,GAAG;YACpD,OAAO;QACT;QAEA,OAAO;IACT;IAAA;;;;;GAAA,GAQA,yBAAyB,MAAA,EAAyB;QAChD,IAAI,CAAC,IAAA,CAAK,mBAAA,EAAqB,UAAU;YACvC,MAAM,IAAI,MAAM,yCAAyC;QAC3D;QAEA,MAAM,cAAc,IAAA,CAAK,uBAAA,CAAwB,IAAA,CAAK,mBAAA,CAAoB,QAAQ;QAClF,MAAM,wBAAwB,IAAA,CAAK,mBAAA,CAAoB,WAAA,CAAY,OAAA,CAAQ,iBAAiB,EAAE;QAE9F,MAAM,UAAU,IAAA,CAAK,mBAAA,CAAoB,QAAA,GACrC,IAAA,CAAK,mBAAA,CAAoB,QAAA,CAAS,OAAA,CAAQ,OAAO,EAAE,IACnD,CAAA,QAAA,EAAW,qBAAqB,EAAA;QAEpC,MAAM,MAAM,IAAI,IAAI,GAAG,OAAO,CAAA,oBAAA,CAAsB;QACpD,IAAI,YAAA,CAAa,MAAA,CAAO,gBAAgB,aAAa,QAAQ,EAAE;QAC/D,IAAI,YAAA,CAAa,MAAA,CAAO,uBAAuB,sBAAsB;QACrE,IAAI,YAAA,CAAa,MAAA,CACf,UAAU,eAAA,CAAgB,eAAA,EAC1B,IAAA,CAAK,mBAAA,CAAoB,mBAAA,CAAoB,EAAE,QAAA,CAAS;QAE1D,IAAI,YAAA,CAAa,MAAA,CAAO,UAAU,eAAA,CAAgB,eAAA,EAAiB,MAAM;QAEzE,IAAI,IAAA,CAAK,mBAAA,CAAoB,YAAA,KAAiB,iBAAiB,IAAA,CAAK,mBAAA,CAAoB,eAAA,EAAiB;YACvG,IAAI,YAAA,CAAa,MAAA,CAAO,UAAU,eAAA,CAAgB,UAAA,EAAY,IAAA,CAAK,mBAAA,CAAoB,eAAe;QACxG;QAEA,MAAM,aAAa,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,mBAAA,CAAoB,QAAA,EAAU,IAAA,CAAK,mBAAmB;QAC7G,IAAI,YAAY;YACd,MAAM,SAAS,IAAA,CAAK,8BAAA,CAA+B,UAAU;YAC7D,OAAO,OAAA,CAAQ,CAAC,OAAO,QAAQ;gBAC7B,IAAI,YAAA,CAAa,MAAA,CAAO,KAAK,KAAK;YACpC,CAAC;QACH;QAEA,OAAO,IAAI,QAAQ;YAAE,CAAC,UAAU,OAAA,CAAQ,QAAQ,CAAA,EAAG,IAAI,IAAA;QAAK,CAAC;IAC/D;IAAA;;;GAAA,GAMA,MAAa,0BAA6C;QACxD,MAAM,eAAyB,CAAC,CAAA;QAEhC,IAAI,IAAA,CAAK,mBAAA,CAAoB,cAAA,EAAgB;YAC3C,IAAI;gBACF,MAAM,mBAAmB,MAAM,IAAA,CAAK,mBAAA,CAAoB,SAAA,EAAW,QAAQ,oBAAoB;oBAC7F,OAAO,IAAA,CAAK,mBAAA,CAAoB,cAAA;gBAClC,CAAC;gBACD,IAAI,kBAAkB;oBACpB,aAAa,IAAA,CAAK,GAAG,iBAAiB,UAAU;gBAClD;YACF,EAAA,OAAS,OAAO;gBACd,QAAQ,KAAA,CAAM,6DAA6D,KAAK;YAClF;QACF,OAAA,IAAW,IAAA,CAAK,mBAAA,CAAoB,cAAA,EAAgB;YAClD,MAAM,mBAAmB,MAAM,qBAC7B,IAAA,CAAK,mBAAA,CAAoB,cAAA,EACzB,IAAA,CAAK,mBAAA;YAEP,IAAI,oBAAoB,MAAM,OAAA,CAAQ,iBAAiB,SAAS,GAAG;gBACjE,aAAa,IAAA,CAAK,GAAG,iBAAiB,SAAS;YACjD;QACF;QAEA,OAAO;IACT;IAAA;;;;GAAA,GAOA,MAAM,mBAA4D;QAChE,MAAM,UAAU,IAAI,QAAQ;YAC1B,+BAA+B;YAC/B,oCAAoC;QACtC,CAAC;QAED,MAAM,eAAe,MAAM,IAAA,CAAK,uBAAA,CAAwB;QAExD,IAAI,eAAe;QACnB,aAAa,OAAA,CAAQ,CAAC,MAAc;YAClC,QAAQ,MAAA,CAAO,cAAc,CAAC;YAC9B,IAAI,cAAc,CAAC,EAAE,UAAA,CAAW,UAAU,OAAA,CAAQ,OAAO,GAAG;gBAC1D,eAAe,eAAe,CAAC;YACjC;QACF,CAAC;QAED,IAAI,IAAA,CAAK,mBAAA,CAAoB,YAAA,KAAiB,eAAe;YAC3D,MAAM,SAAS,IAAI,IAAI,IAAA,CAAK,mBAAA,CAAoB,QAAQ;YACxD,OAAO,YAAA,CAAa,MAAA,CAAO,UAAU,eAAA,CAAgB,SAAS;YAC9D,OAAO,YAAA,CAAa,MAAA,CAAO,UAAU,eAAA,CAAgB,aAAa;YAClE,QAAQ,MAAA,CAAO,UAAU,OAAA,CAAQ,QAAA,EAAU,OAAO,QAAA,CAAS,CAAC;YAC5D,QAAQ,GAAA,CAAI,UAAU,OAAA,CAAQ,YAAA,EAAc,UAAU;QACxD;QAEA,IAAI,iBAAiB,IAAI;YACvB,OAAO,UAAU;gBACf,WAAW,UAAU,YAAA;gBACrB,qBAAqB,IAAA,CAAK,mBAAA;gBAC1B,QAAQ,gBAAgB,mBAAA;gBACxB,SAAS;gBACT;YACF,CAAC;QACH;QAEA,MAAM,EAAE,IAAA,EAAM,QAAQ,CAAC,KAAK,CAAA,GAAI,CAAC,CAAA,CAAE,CAAA,GAAI,MAAM,YAAY,cAAc,IAAA,CAAK,mBAAmB;QAC/F,IAAI,MAAM;YACR,OAAO,SAAS;gBACd,WAAW,UAAU,YAAA;gBACrB,qBAAqB,IAAA,CAAK,mBAAA;gBAC1B,eAAe;gBACf;gBACA,OAAO;YACT,CAAC;QACH;QAEA,IACE,IAAA,CAAK,mBAAA,CAAoB,YAAA,KAAiB,iBAAA,CACzC,OAAO,gUAAW,+BAAA,CAA6B,YAAA,IAC9C,OAAO,gUAAW,+BAAA,CAA6B,iBAAA,IAC/C,OAAO,gUAAW,+BAAA,CAA6B,mBAAA,GACjD;YAEA,MAAM,mBAAmB,yTAAI,yBAAA,CAAuB;gBAClD,QAAQ,MAAM,MAAA;gBACd,SAAS,MAAM,OAAA;gBACf,QAAQ,MAAM,MAAA;YAChB,CAAC;YAED,iBAAiB,YAAA,GAAe;YAEhC,QAAQ,KAAA,CACN,CAAA;;;;;;AAAA,EAMN,iBAAiB,cAAA,CAAe,CAAC,EAAA;YAG7B,MAAM,EAAE,MAAM,WAAA,EAAa,QAAQ,CAAC,UAAU,CAAA,GAAI,CAAC,CAAA,CAAE,CAAA,GAAI,MAAM,YAAY,cAAc;gBACvF,GAAG,IAAA,CAAK,mBAAA;gBACR,eAAe;YACjB,CAAC;YACD,IAAI,aAAa;gBACf,OAAO,SAAS;oBACd,WAAW,UAAU,YAAA;oBACrB,qBAAqB,IAAA,CAAK,mBAAA;oBAC1B,eAAe;oBACf;oBACA,OAAO;gBACT,CAAC;YACH;YAEA,MAAM,IAAI,MAAM,YAAY,WAAW,gCAAgC;QACzE;QAEA,MAAM,IAAI,MAAM,OAAO,WAAW,0BAA0B;IAC9D;IAAA;;;;GAAA,GAOA,0CAA0C,KAAA,EAAqC;QAO7E,IAAI,MAAM,MAAA,0TAAW,+BAAA,CAA6B,qBAAA,EAAuB;YACvE,MAAM,MAAM,CAAA,kJAAA,CAAA;YACZ,MAAM,IAAI,MAAM,GAAG;QACrB;QACA,MAAM,IAAI,MAAM,CAAA,4CAAA,EAA+C,MAAM,cAAA,CAAe,CAAC,CAAA,CAAA,CAAG;IAC1F;IAAA;;;;GAAA,GAOA,0BAA0B,OAAA,EAA2B;QACnD,IAAI,IAAA,CAAK,mBAAA,CAAoB,4BAAA,KAAiC,GAAG;YAC/D,OAAO;QACT;QAEA,MAAM,kBAAkB,IAAA,CAAK,mBAAA,CAAoB,4BAAA,GAA+B;QAChF,MAAM,aAAa,UAAU,OAAA,CAAQ,aAAA;QACrC,QAAQ,MAAA,CAAO,cAAc,GAAG,UAAU,CAAA,CAAA,EAAI,eAAe,CAAA,mCAAA,CAAqC;QAClG,OAAO;IACT;IAEQ,wBAAwB,GAAA,EAAe;QAC7C,MAAM,aAAa,IAAI,IAAI,GAAG;QAC9B,WAAW,YAAA,CAAa,MAAA,CAAO,UAAU,eAAA,CAAgB,UAAU;QACnE,WAAW,YAAA,CAAa,MAAA,CAAO,UAAU,eAAA,CAAgB,gBAAgB;QACzE,OAAO;IACT;IAEQ,0BAA0B,GAAA,EAAU,QAAA,EAA8D;QACxG,OAAO,SAAS,UAAA,CAAW,GAAG;IAChC;IAEQ,+BAA+B,UAAA,EAAyD;QAC9F,MAAM,MAAM,aAAA,GAAA,IAAI,IAAI;QACpB,IAAI,WAAW,IAAA,KAAS,mBAAmB;YACzC,IAAI,GAAA,CAAI,mBAAmB,EAAE;QAC/B;QACA,IAAI,WAAW,IAAA,KAAS,gBAAgB;YACtC,IAAI,WAAW,cAAA,EAAgB;gBAC7B,IAAI,GAAA,CAAI,mBAAmB,WAAW,cAAc;YACtD;YACA,IAAI,WAAW,gBAAA,EAAkB;gBAC/B,IAAI,GAAA,CAAI,mBAAmB,WAAW,gBAAgB;YACxD;QACF;QACA,OAAO;IACT;AACF;;AC5VO,IAAM,sBAAN,MAA0B;IAI/B,YAAY,OAAA,CAAmC;QAC7C,IAAA,CAAK,mBAAA,GAAsB,IAAA,CAAK,aAAA,CAAc,SAAS,oBAAoB;QAC3E,IAAA,CAAK,sBAAA,GAAyB,IAAA,CAAK,aAAA,CAAc,SAAS,uBAAuB;IACnF;IAEQ,cAAc,OAAA,EAA0C;QAC9D,IAAI,CAAC,QAAS,CAAA,OAAO;QACrB,IAAI;YACF,WAAO,2TAAA,EAAM,OAAO;QACtB,EAAA,OAAS,GAAG;YACV,MAAM,IAAI,MAAM,CAAA,iBAAA,EAAoB,OAAO,CAAA,GAAA,EAAM,CAAC,EAAE;QACtD;IACF;IAEA,WAAW,GAAA,EAAyC;QAClD,MAAM,YAAY,IAAA,CAAK,sBAAA,CAAuB,GAAG;QACjD,IAAI,UAAW,CAAA,OAAO;QAEtB,OAAO,IAAA,CAAK,yBAAA,CAA0B,GAAG;IAC3C;IAEQ,uBAAuB,GAAA,EAAyC;QACtE,IAAI,CAAC,IAAA,CAAK,mBAAA,CAAqB,CAAA,OAAO;QAEtC,IAAI;YACF,MAAM,SAAS,IAAA,CAAK,mBAAA,CAAoB,IAAI,QAAQ;YACpD,IAAI,CAAC,UAAU,CAAA,CAAE,YAAY,MAAA,EAAS,CAAA,OAAO;YAE7C,MAAM,SAAS,OAAO,MAAA;YACtB,IAAI,OAAO,EAAA,CAAI,CAAA,OAAO;gBAAE,MAAM;gBAAgB,gBAAgB,OAAO,EAAA;YAAG;YACxE,IAAI,OAAO,IAAA,CAAM,CAAA,OAAO;gBAAE,MAAM;gBAAgB,kBAAkB,OAAO,IAAA;YAAK;YAE9E,OAAO;QACT,EAAA,OAAS,GAAG;YACV,QAAQ,KAAA,CAAM,yCAAyC,CAAC;YACxD,OAAO;QACT;IACF;IAEQ,0BAA0B,GAAA,EAAyC;QACzE,IAAI,CAAC,IAAA,CAAK,sBAAA,CAAwB,CAAA,OAAO;QAEzC,IAAI;YACF,MAAM,SAAS,IAAA,CAAK,sBAAA,CAAuB,IAAI,QAAQ;YACvD,OAAO,SAAS;gBAAE,MAAM;YAAkB,IAAI;QAChD,EAAA,OAAS,GAAG;YACV,QAAQ,KAAA,CAAM,6CAA6C,CAAC;YAC5D,OAAO;QACT;IACF;AACF;;ACpCO,IAAM,0BAA0B;IACrC,qBAAqB;IACrB,mBAAmB;IACnB,qBAAqB;IACrB,kBAAkB;IAClB,qBAAqB;IACrB,qBAAqB;IACrB,iCAAiC;IACjC,oCAAoC;IACpC,YAAY;IACZ,oBAAoB;IACpB,qBAAqB;AACvB;AAEA,SAAS,sBAAsB,SAAA,EAA+B,GAAA,EAA0C;IACtG,IAAI,CAAC,aAAa,oVAAA,EAA2B,GAAG,GAAG;QACjD,MAAM,IAAI,MAAM,CAAA,4EAAA,CAA8E;IAChG;AACF;AAEA,SAAS,uBAAuB,gBAAA,EAAsC;IACpE,IAAI,CAAC,kBAAkB;QACrB,MAAM,IAAI,MAAM,CAAA,4FAAA,CAA8F;IAChH;AACF;AAEA,SAAS,+BAA+B,UAAA,EAAoB,MAAA,EAAgB;IAC1E,IAAI;IACJ,IAAI;QACF,YAAY,IAAI,IAAI,UAAU;IAChC,EAAA,OAAQ;QACN,MAAM,IAAI,MAAM,CAAA,kDAAA,CAAoD;IACtE;IAEA,IAAI,UAAU,MAAA,KAAW,QAAQ;QAC/B,MAAM,IAAI,MAAM,CAAA,gFAAA,CAAkF;IACpG;AACF;AAEA,SAAS,4BACP,GAAA,EACA,mBAAA,EACA,OAAA,EACA;IACA,OACE,IAAI,MAAA,KAAW,oVAAA,CAA6B,YAAA,IAC5C,CAAC,CAAC,oBAAoB,oBAAA,IACtB,QAAQ,MAAA,KAAW;AAEvB;AAEA,SAAS,uBACP,eAAA,EACA,YAAA,EACA,mBAAA,EAC+C;IAC/C,MAAM,WAAW,CAAC,oBAAoB,iBAAiB,YAAY;IACnE,IAAI,UAAU;QACZ,OAAO,UAAU;YACf,WAAW;YACX;YACA,QAAQ,gBAAgB,iBAAA;QAC1B,CAAC;IACH;IACA,OAAO;AACT;AAkCO,IAAM,sBAA4C,OACvD,SACA,YACqC;IACrC,MAAM,sBAAsB,MAAM,0BAA0B,mBAAmB,OAAO,GAAG,OAAO;IAChG,qBAAqB,oBAAoB,SAAS;IAGlD,MAAM,eAAe,QAAQ,YAAA,IAAgB,UAAU,YAAA;IAEvD,IAAI,oBAAoB,WAAA,EAAa;QACnC,sBAAsB,oBAAoB,SAAA,EAAW,oBAAoB,SAAS;QAClF,IAAI,oBAAoB,SAAA,IAAa,oBAAoB,MAAA,EAAQ;YAC/D,+BAA+B,oBAAoB,SAAA,EAAW,oBAAoB,MAAM;QAC1F;QACA,uBAAuB,oBAAoB,QAAA,IAAY,oBAAoB,MAAM;IACnF;IAEA,MAAM,sBAAsB,IAAI,oBAAoB,QAAQ,uBAAuB;IACnF,MAAM,mBAAmB,IAAI,iBAC3B,qBACA;QAAE,yBAAyB,QAAQ,uBAAA;IAAwB,GAC3D;IAGF,eAAe,aACbC,oBAAAA,EACuE;QAEvE,IAAI,CAAC,QAAQ,SAAA,EAAW;YACtB,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,OAAO;wBAAE,QAAQ,wBAAwB,gBAAA;oBAAiB;gBAC5D;YACF;QACF;QACA,MAAM,EAAE,cAAc,mBAAA,EAAqB,sBAAsBC,aAAAA,CAAa,CAAA,GAAID;QAClF,IAAI,CAAC,qBAAqB;YACxB,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,OAAO;wBAAE,QAAQ,wBAAwB,mBAAA;oBAAoB;gBAC/D;YACF;QACF;QACA,IAAI,CAACC,eAAc;YACjB,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,OAAO;wBAAE,QAAQ,wBAAwB,mBAAA;oBAAoB;gBAC/D;YACF;QACF;QAEA,MAAM,EAAE,MAAM,YAAA,EAAc,QAAQ,aAAA,CAAc,CAAA,4TAAI,YAAA,EAAU,mBAAmB;QACnF,IAAI,CAAC,gBAAgB,eAAe;YAClC,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,OAAO;wBAAE,QAAQ,wBAAwB,+BAAA;wBAAiC,QAAQ;oBAAc;gBAClG;YACF;QACF;QAEA,IAAI,CAAC,cAAc,SAAS,KAAK;YAC/B,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,OAAO;wBAAE,QAAQ,wBAAwB,kCAAA;oBAAmC;gBAC9E;YACF;QACF;QAEA,IAAI;YAEF,MAAM,WAAW,MAAM,QAAQ,SAAA,CAAU,QAAA,CAAS,cAAA,CAAe,aAAa,OAAA,CAAQ,GAAA,EAAK;gBACzF,QAAQ;gBACR,kBAAkBD,qBAAoB,mBAAA,CAAoB;gBAC1D,eAAe,uBAAuB;gBACtC,eAAeC,iBAAgB;gBAC/B,gBAAgBD,qBAAoB,QAAA,CAAS,MAAA;gBAAA,gGAAA;gBAE7C,iBAAiB,OAAO,WAAA,CAAY,MAAM,IAAA,CAAK,QAAQ,OAAA,CAAQ,OAAA,CAAQ,CAAC,EAAE,GAAA,CAAI,CAAC,CAAC,GAAG,CAAC,CAAA,GAAM;wBAAC;wBAAG;4BAAC,CAAC;yBAAC;qBAAC,CAAC;YACrG,CAAC;YACD,OAAO;gBAAE,MAAM,SAAS,OAAA;gBAAS,OAAO;YAAK;QAC/C,EAAA,OAAS,KAAU;YACjB,IAAI,KAAK,QAAQ,QAAQ;gBACvB,IAAI,IAAI,MAAA,CAAO,CAAC,CAAA,CAAE,IAAA,KAAS,oBAAoB;oBAC7C,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,SAAS,CAAA,sBAAA,CAAA;4BACT,OAAO;gCAAE,QAAQ,wBAAwB,UAAA;gCAAY,QAAQ,IAAI,MAAA;4BAAO;wBAC1E;oBACF;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,OAAO;wBACL,SAAS,IAAI,MAAA,CAAO,CAAC,CAAA,CAAE,IAAA;wBACvB,OAAO;4BAAE,QAAQ,IAAI,MAAA,CAAO,CAAC,CAAA,CAAE,IAAA;4BAAM,QAAQ,IAAI,MAAA;wBAAO;oBAC1D;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,MAAM;oBACN,OAAO;wBACL,SAAS,CAAA,4BAAA,CAAA;wBACT,OAAO;4BAAE,QAAQ,wBAAwB,mBAAA;4BAAqB,QAAQ;gCAAC,GAAG;6BAAA;wBAAE;oBAC9E;gBACF;YACF;QACF;IACF;IAEA,eAAe,eACbA,oBAAAA,EAIA;QACA,MAAM,EAAE,MAAM,YAAA,EAAc,KAAA,CAAM,CAAA,GAAI,MAAM,aAAaA,oBAAmB;QAC5E,IAAI,CAAC,gBAAgB,aAAa,MAAA,KAAW,GAAG;YAC9C,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;QAEA,MAAM,UAAU,IAAI,QAAQ;QAC5B,IAAI,eAAe;QACnB,aAAa,OAAA,CAAQ,CAAC,MAAc;YAClC,QAAQ,MAAA,CAAO,cAAc,CAAC;YAC9B,IAAI,cAAc,CAAC,EAAE,UAAA,CAAW,UAAU,OAAA,CAAQ,OAAO,GAAG;gBAC1D,eAAe,eAAe,CAAC;YACjC;QACF,CAAC;QAGD,MAAM,EAAE,MAAM,UAAA,EAAY,MAAA,CAAO,CAAA,GAAI,MAAM,YAAY,cAAcA,oBAAmB;QACxF,IAAI,QAAQ;YACV,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,SAAS,CAAA,gDAAA,CAAA;oBACT,OAAO;wBAAE,QAAQ,wBAAwB,mBAAA;wBAAqB;oBAAO;gBACvE;YACF;QACF;QACA,OAAO;YAAE,MAAM;gBAAE;gBAAY;gBAAc;YAAQ;YAAG,OAAO;QAAK;IACpE;IAEA,SAAS,2BACPA,oBAAAA,EACA,MAAA,EACA,OAAA,EACA,OAAA,EACiD;QACjD,IAAI,CAAC,iBAAiB,6BAAA,CAA8B,GAAG;YACrD,OAAO,UAAU;gBACf,WAAW,UAAU,YAAA;gBACrB,qBAAAA;gBACA;gBACA;YACF,CAAC;QACH;QAIA,MAAM,mBAAmB,WAAW,iBAAiB,wBAAA,CAAyB,MAAM;QAIpF,IAAI,iBAAiB,GAAA,CAAI,UAAU,OAAA,CAAQ,QAAQ,GAAG;YACpD,iBAAiB,GAAA,CAAI,UAAU,OAAA,CAAQ,YAAA,EAAc,UAAU;QACjE;QAKA,MAAM,iBAAiB,iBAAiB,yBAAA,CAA0B,gBAAgB;QAClF,IAAI,gBAAgB;YAClB,MAAM,MAAM,CAAA,8NAAA,CAAA;YACZ,QAAQ,GAAA,CAAI,GAAG;YACf,OAAO,UAAU;gBACf,WAAW,UAAU,YAAA;gBACrB,qBAAAA;gBACA;gBACA;YACF,CAAC;QACH;QAEA,OAAO,UAAUA,sBAAqB,QAAQ,SAAS,gBAAgB;IACzE;IAWA,SAAS,qCACPA,oBAAAA,EACA,IAAA,EACwC;QACxC,MAAM,yBAAyB,oBAAoB,UAAA,CAAWA,qBAAoB,QAAQ;QAC1F,IAAI,CAAC,wBAAwB;YAC3B,OAAO;QACT;QACA,IAAI,eAAe;QACnB,IAAI,uBAAuB,IAAA,KAAS,gBAAgB;YAElD,IAAI,uBAAuB,gBAAA,IAAoB,uBAAuB,gBAAA,KAAqB,KAAK,OAAA,EAAS;gBACvG,eAAe;YACjB;YAEA,IAAI,uBAAuB,cAAA,IAAkB,uBAAuB,cAAA,KAAmB,KAAK,KAAA,EAAO;gBACjG,eAAe;YACjB;QACF;QAEA,IAAI,uBAAuB,IAAA,KAAS,qBAAqB,KAAK,KAAA,EAAO;YACnE,eAAe;QACjB;QACA,IAAI,CAAC,cAAc;YACjB,OAAO;QACT;QACA,IAAIA,qBAAoB,4BAAA,GAA+B,GAAG;YAKxD,QAAQ,IAAA,CACN;YAEF,OAAO;QACT;QACA,MAAM,iBAAiB,2BACrBA,sBACA,gBAAgB,0BAAA,EAChB;QAEF,IAAI,eAAe,MAAA,KAAW,aAAa;YAEzC,OAAO;QACT;QACA,OAAO;IACT;IAEA,eAAe,uCAAuC;QACpD,MAAM,EAAE,aAAA,CAAc,CAAA,GAAI;QAE1B,IAAI;YAEF,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,GAAI,MAAM,YAAY,eAAgB,mBAAmB;YAC9E,IAAI,QAAQ;gBACV,MAAM,MAAA,CAAO,CAAC,CAAA;YAChB;YAEA,OAAO,SAAS;gBACd,WAAW,UAAU,YAAA;gBACrB;gBACA,eAAe;gBACf,SAAS,IAAI,QAAQ;gBAAA,oEAAA;gBAErB,OAAO;YACT,CAAC;QACH,EAAA,OAAS,KAAK;YACZ,OAAO,wBAAwB,KAAK,QAAQ;QAC9C;IACF;IAEA,eAAe,uCAAuC;QACpD,MAAM,kBAAkB,oBAAoB,SAAA;QAC5C,MAAM,kBAAkB,CAAC,CAAC,oBAAoB,oBAAA;QAC9C,MAAM,qBAAqB,CAAC,CAAC,oBAAoB,eAAA;QAKjD,IAAI,oBAAoB,cAAA,IAAkB,oBAAoB,cAAA,EAAgB;YAC5E,IAAI;gBACF,OAAO,MAAM,iBAAiB,gBAAA,CAAiB;YACjD,EAAA,OAAS,OAAO;gBAYd,IAAI,sUAAiB,yBAAA,IAA0B,oBAAoB,YAAA,KAAiB,eAAe;oBACjG,iBAAiB,yCAAA,CAA0C,KAAK;gBAClE,OAAO;oBACL,QAAQ,KAAA,CAAM,uCAAuC,KAAK;gBAC5D;YACF;QACF;QAIA,IACE,oBAAoB,YAAA,KAAiB,iBACrC,oBAAoB,QAAA,CAAS,YAAA,CAAa,GAAA,CAAI,UAAU,eAAA,CAAgB,UAAU,GAClF;YACA,OAAO,2BAA2B,qBAAqB,gBAAgB,cAAA,EAAgB,EAAE;QAC3F;QAEA,MAAM,sCACJ,oBAAoB,WAAA,IAAe,oBAAoB,YAAA,KAAiB;QAK1E,IAAI,oBAAoB,YAAA,KAAiB,gBAAgB,qCAAqC;YAC5F,OAAO,2BAA2B,qBAAqB,gBAAgB,2BAAA,EAA6B,EAAE;QACxG;QAGA,IACE,oBAAoB,YAAA,KAAiB,iBACrC,uCACA,CAAC,oBAAoB,QAAA,CAAS,YAAA,CAAa,GAAA,CAAI,UAAU,eAAA,CAAgB,WAAW,GACpF;YAKA,MAAM,cAAc,IAAI,IAAI,oBAAoB,SAAU;YAC1D,YAAY,YAAA,CAAa,MAAA,CACvB,UAAU,eAAA,CAAgB,gBAAA,EAC1B,oBAAoB,QAAA,CAAS,QAAA,CAAS;YAExC,MAAM,UAAU,IAAI,QAAQ;gBAAE,CAAC,UAAU,OAAA,CAAQ,QAAQ,CAAA,EAAG,YAAY,QAAA,CAAS;YAAE,CAAC;YACpF,OAAO,2BAA2B,qBAAqB,gBAAgB,2BAAA,EAA6B,IAAI,OAAO;QACjH;QAGA,MAAM,cAAc,IAAI,IAAI,oBAAoB,QAAQ,EAAE,YAAA,CAAa,GAAA,CACrE,UAAU,eAAA,CAAgB,gBAAA;QAG5B,IAAI,oBAAoB,YAAA,KAAiB,iBAAiB,CAAC,oBAAoB,WAAA,IAAe,aAAa;YAEzG,MAAM,6BAA6B,IAAI,IAAI,WAAW;YAEtD,IAAI,oBAAoB,eAAA,EAAiB;gBACvC,2BAA2B,YAAA,CAAa,MAAA,CACtC,UAAU,eAAA,CAAgB,UAAA,EAC1B,oBAAoB,eAAA;YAExB;YACA,2BAA2B,YAAA,CAAa,MAAA,CAAO,UAAU,eAAA,CAAgB,WAAA,EAAa,MAAM;YAE5F,MAAM,UAAU,IAAI,QAAQ;gBAAE,CAAC,UAAU,OAAA,CAAQ,QAAQ,CAAA,EAAG,2BAA2B,QAAA,CAAS;YAAE,CAAC;YACnG,OAAO,2BAA2B,qBAAqB,gBAAgB,wBAAA,EAA0B,IAAI,OAAO;QAC9G;QAKA,IAAI,oBAAoB,YAAA,KAAiB,iBAAiB,CAAC,oBAAoB;YAC7E,OAAO,2BAA2B,qBAAqB,gBAAgB,iBAAA,EAAmB,EAAE;QAC9F;QAEA,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;YACxC,OAAO,UAAU;gBACf,WAAW,UAAU,YAAA;gBACrB;gBACA,QAAQ,gBAAgB,yBAAA;YAC1B,CAAC;QACH;QAGA,IAAI,CAAC,mBAAmB,iBAAiB;YACvC,OAAO,2BAA2B,qBAAqB,gBAAgB,4BAAA,EAA8B,EAAE;QACzG;QAEA,IAAI,mBAAmB,CAAC,iBAAiB;YACvC,OAAO,2BAA2B,qBAAqB,gBAAgB,4BAAA,EAA8B,EAAE;QACzG;QAGA,MAAM,EAAE,MAAM,YAAA,EAAc,QAAQ,aAAA,CAAc,CAAA,4TAAI,YAAA,EAAU,oBAAoB,oBAAqB;QAEzG,IAAI,eAAe;YACjB,OAAO,wBAAwB,aAAA,CAAc,CAAC,CAAA,EAAG,QAAQ;QAC3D;QAEA,IAAI,aAAa,OAAA,CAAQ,GAAA,GAAM,oBAAoB,SAAA,EAAW;YAC5D,OAAO,2BAA2B,qBAAqB,gBAAgB,8BAAA,EAAgC,EAAE;QAC3G;QAEA,IAAI;YAEF,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,GAAI,MAAM,YAAY,oBAAoB,oBAAA,EAAuB,mBAAmB;YACzG,IAAI,QAAQ;gBACV,MAAM,MAAA,CAAO,CAAC,CAAA;YAChB;YAEA,MAAM,uBAAuB,SAAS;gBACpC,WAAW,UAAU,YAAA;gBACrB;gBACA,eAAe;gBACf,SAAS,IAAI,QAAQ;gBAAA,oEAAA;gBAErB,OAAO,oBAAoB,oBAAA;YAC7B,CAAC;YAED,MAAM,aAAa,qBAAqB,MAAA,CAAO;YAE/C,IAAI,WAAW,MAAA,EAAQ;gBACrB,MAAM,wBAAwB,qCAAqC,qBAAqB,UAAU;gBAClG,IAAI,uBAAuB;oBACzB,OAAO;gBACT;YACF;YAEA,OAAO;QACT,EAAA,OAAS,KAAK;YACZ,OAAO,wBAAwB,KAAK,QAAQ;QAC9C;QAGA,OAAO,UAAU;YACf,WAAW,UAAU,YAAA;YACrB;YACA,QAAQ,gBAAgB,eAAA;QAC1B,CAAC;IACH;IAEA,eAAe,wBACb,GAAA,EACA,YAAA,EAC0D;QAC1D,IAAI,CAAA,CAAE,oUAAe,yBAAA,GAAyB;YAC5C,OAAO,UAAU;gBACf,WAAW,UAAU,YAAA;gBACrB;gBACA,QAAQ,gBAAgB,eAAA;YAC1B,CAAC;QACH;QAEA,IAAI;QAEJ,IAAI,4BAA4B,KAAK,qBAAqB,OAAO,GAAG;YAClE,MAAM,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,GAAI,MAAM,eAAe,mBAAmB;YAChE,IAAI,MAAM;gBACR,OAAO,SAAS;oBACd,WAAW,UAAU,YAAA;oBACrB;oBACA,eAAe,KAAK,UAAA;oBACpB,SAAS,KAAK,OAAA;oBACd,OAAO,KAAK,YAAA;gBACd,CAAC;YACH;YAGA,IAAI,OAAO,OAAO,QAAQ;gBACxB,eAAe,MAAM,KAAA,CAAM,MAAA;YAC7B,OAAO;gBACL,eAAe,wBAAwB,kBAAA;YACzC;QACF,OAAO;YACL,IAAI,QAAQ,MAAA,KAAW,OAAO;gBAC5B,eAAe,wBAAwB,iBAAA;YACzC,OAAA,IAAW,CAAC,oBAAoB,oBAAA,EAAsB;gBACpD,eAAe,wBAAwB,mBAAA;YACzC,OAAO;gBAEL,eAAe;YACjB;QACF;QAEA,IAAI,YAAA,GAAe;QAEnB,MAAM,oBAAoB;iUACxB,+BAAA,CAA6B,YAAA;iUAC7B,+BAAA,CAA6B,iBAAA;iUAC7B,+BAAA,CAA6B,mBAAA;SAC/B,CAAE,QAAA,CAAS,IAAI,MAAM;QAErB,IAAI,mBAAmB;YACrB,OAAO,2BACL,qBACA,qDAAqD;gBAAE,YAAY,IAAI,MAAA;gBAAQ;YAAa,CAAC,GAC7F,IAAI,cAAA,CAAe;QAEvB;QAEA,OAAO,UAAU;YACf,WAAW,UAAU,YAAA;YACrB;YACA,QAAQ,IAAI,MAAA;YACZ,SAAS,IAAI,cAAA,CAAe;QAC9B,CAAC;IACH;IAEA,SAAS,mBAAmB,SAAA,EAA6B,GAAA,EAAsD;QAC7G,IAAI,CAAA,CAAE,oUAAe,gCAAA,GAAgC;YACnD,OAAO,UAAU;gBACf;gBACA;gBACA,QAAQ,gBAAgB,eAAA;YAC1B,CAAC;QACH;QAEA,OAAO,UAAU;YACf;YACA;YACA,QAAQ,IAAI,IAAA;YACZ,SAAS,IAAI,cAAA,CAAe;QAC9B,CAAC;IACH;IAEA,eAAe,8CAA8C;QAC3D,MAAM,EAAE,aAAA,CAAc,CAAA,GAAI;QAE1B,IAAI,CAAC,eAAe;YAClB,OAAO,wBAAwB,IAAI,MAAM,yBAAyB,GAAG,QAAQ;QAC/E;QAGA,IAAI,CAAC,uBAAuB,aAAa,GAAG;YAC1C,OAAO,UAAU;gBACf,WAAW;gBACX;gBACA,QAAQ,gBAAgB,iBAAA;gBACxB,SAAS;YACX,CAAC;QACH;QAEA,MAAM,kBAAkB,oBAAoB,aAAa;QACzD,MAAM,gBAAgB,uBAAuB,iBAAiB,cAAc,mBAAmB;QAC/F,IAAI,eAAe;YACjB,OAAO;QACT;QAEA,MAAM,EAAE,IAAA,EAAM,SAAA,EAAW,MAAA,CAAO,CAAA,GAAI,MAAM,uBAAuB,eAAe,mBAAmB;QACnG,IAAI,QAAQ;YACV,OAAO,mBAAmB,WAAW,MAAA,CAAO,CAAC,CAAC;QAChD;QACA,OAAO,SAAS;YACd;YACA;YACA,aAAa;YACb,OAAO;QACT,CAAC;IACH;IAEA,eAAe,0CAA0C;QACvD,MAAM,EAAE,aAAA,CAAc,CAAA,GAAI;QAE1B,IAAI,CAAC,eAAe;YAClB,OAAO,wBAAwB,IAAI,MAAM,yBAAyB,GAAG,QAAQ;QAC/E;QAGA,IAAI,uBAAuB,aAAa,GAAG;YACzC,MAAM,kBAAkB,oBAAoB,aAAa;YACzD,MAAM,gBAAgB,uBAAuB,iBAAiB,cAAc,mBAAmB;YAC/F,IAAI,eAAe;gBACjB,OAAO;YACT;YAEA,MAAM,EAAE,MAAAE,KAAAA,EAAM,SAAA,EAAW,QAAAC,OAAAA,CAAO,CAAA,GAAI,MAAM,uBAAuB,eAAe,mBAAmB;YACnG,IAAIA,SAAQ;gBACV,OAAO,mBAAmB,WAAWA,OAAAA,CAAO,CAAC,CAAC;YAChD;YAEA,OAAO,SAAS;gBACd;gBACA;gBACA,aAAaD;gBACb,OAAO;YACT,CAAC;QACH;QAGA,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,GAAI,MAAM,YAAY,eAAe,mBAAmB;QAC7E,IAAI,QAAQ;YACV,OAAO,wBAAwB,MAAA,CAAO,CAAC,CAAA,EAAG,QAAQ;QACpD;QAEA,OAAO,SAAS;YACd,WAAW,UAAU,YAAA;YACrB;YACA,eAAe;YACf,OAAO;QACT,CAAC;IACH;IAEA,IAAI,oBAAoB,aAAA,EAAe;QACrC,IAAI,iBAAiB,OAAO;YAC1B,OAAO,wCAAwC;QACjD;QAEA,IAAI,iBAAiB,UAAU,YAAA,EAAc;YAC3C,OAAO,qCAAqC;QAC9C;QAEA,OAAO,4CAA4C;IACrD;IAGA,IACE,iBAAiB,UAAU,UAAA,IAC3B,iBAAiB,UAAU,MAAA,IAC3B,iBAAiB,UAAU,YAAA,EAC3B;QACA,OAAO,UAAU;YACf,WAAW;YACX;YACA,QAAQ;QACV,CAAC;IACH;IAEA,OAAO,qCAAqC;AAC9C;AAKO,IAAM,oBAAoB,CAAC,WAAyB;IACzD,MAAM,EAAE,UAAA,EAAY,eAAA,EAAiB,QAAA,EAAU,MAAA,EAAQ,OAAA,EAAS,cAAA,EAAgB,WAAA,EAAa,MAAA,CAAO,CAAA,GAAI;IACxG,OAAO;QAAE;QAAY;QAAiB;QAAU;QAAQ;QAAS;QAAgB;QAAa;IAAO;AACvG;AAEA,IAAM,uDAAuD,CAAC,EAC5D,UAAA,EACA,YAAA,EACF,KAGc;IACZ,OAAQ,YAAY;QAClB,0TAAK,+BAAA,CAA6B,YAAA;YAChC,OAAO,GAAG,gBAAgB,mBAAmB,CAAA,SAAA,EAAY,YAAY,EAAA;QACvE,0TAAK,+BAAA,CAA6B,iBAAA;YAChC,OAAO,gBAAgB,eAAA;QACzB,0TAAK,+BAAA,CAA6B,mBAAA;YAChC,OAAO,gBAAgB,0BAAA;QACzB;YACE,OAAO,gBAAgB,eAAA;IAC3B;AACF;;ACnvBA,IAAM,iBAAiB;IACrB,WAAW;IACX,QAAQ;IACR,QAAQ,KAAA;IACR,YAAY,KAAA;IACZ,UAAU;IACV,gBAAgB;IAChB,aAAa;IACb,QAAQ;IACR,UAAU;AACZ;AAaO,SAAS,0BAA0B,MAAA,EAA0C;IAClF,MAAM,mBAAmB,uBAAuB,gBAAgB,OAAO,OAAO;IAC9E,MAAM,YAAY,OAAO,SAAA;IAEzB,MAAME,uBAA2C,CAAC,SAAkB,UAA0B,CAAC,CAAA,KAAM;QACnG,MAAM,EAAE,MAAA,EAAQ,UAAA,CAAW,CAAA,GAAI;QAC/B,MAAM,iBAAiB,uBAAuB,kBAAkB,OAAO;QACvE,OAAO,oBAA4B,SAAS;YAC1C,GAAG,OAAA;YACH,GAAG,cAAA;YAAA,iFAAA;YAAA,0CAAA;YAGH;YACA;YACA;QACF,CAAC;IACH;IAEA,OAAO;QACL,qBAAAA;QACA;IACF;AACF;;AChDO,IAAM,8BAA8B,OACzC,KACA,SACA,SAC8B;IAC9B,MAAM,EAAE,WAAA,EAAa,QAAA,EAAU,gBAAA,CAAiB,CAAA,GAAI,QAAQ,CAAC;IAC7D,MAAM,EAAE,MAAA,EAAQ,SAAA,EAAW,KAAA,CAAM,CAAA,GAAI;IAErC,MAAM,EAAE,QAAA,EAAU,KAAA,EAAO,aAAA,CAAc,CAAA,GAAI,uBAAuB;QAAE,GAAG,IAAA;IAAK,CAAC;IAE7E,MAAM,CAAC,aAAa,UAAU,gBAAgB,CAAA,GAAI,MAAM,QAAQ,GAAA,CAAI;QAClE,eAAe,YAAY,SAAS,UAAA,CAAW,SAAS,IAAI,QAAQ,OAAA,CAAQ,KAAA,CAAS;QACrF,YAAY,SAAS,MAAM,OAAA,CAAQ,MAAM,IAAI,QAAQ,OAAA,CAAQ,KAAA,CAAS;QACtE,oBAAoB,QAAQ,cAAc,eAAA,CAAgB;YAAE,gBAAgB;QAAM,CAAC,IAAI,QAAQ,OAAA,CAAQ,KAAA,CAAS;KACjH;IAED,MAAM,YAAY,2BAA2B;QAC3C,SAAS;QACT,MAAM;QACN,cAAc;IAChB,CAAC;IACD,OAAO,OAAO,MAAA,CAAO,KAAK,SAAS;AACrC;AAKO,SAAS,2BAA4D,UAAA,EAAkB;IAC5F,MAAM,OAAO,WAAW,IAAA,GAAO;QAAE,GAAG,WAAW,IAAA;IAAK,IAAI,WAAW,IAAA;IACnE,MAAM,eAAe,WAAW,YAAA,GAAe;QAAE,GAAG,WAAW,YAAA;IAAa,IAAI,WAAW,YAAA;IAC3F,qBAAqB,IAAI;IACzB,qBAAqB,YAAY;IACjC,OAAO;QAAE,GAAG,UAAA;QAAY;QAAM;IAAa;AAC7C;AAEA,SAAS,qBAAqB,QAAA,EAAwE;IAEpG,IAAI,UAAU;QACZ,IAAI,qBAAqB,UAAU;YACjC,OAAO,QAAA,CAAS,iBAAiB,CAAA;QACnC;QACA,IAAI,sBAAsB,UAAU;YAClC,OAAO,QAAA,CAAS,kBAAkB,CAAA;QACpC;IACF;IAEA,OAAO;AACT", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94]}}, {"offset": {"line": 4926, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 4955, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/jwt/legacyReturn.ts"], "sourcesContent": ["import type { JwtReturnType } from './types';\n\n// TODO(dimkl): Will be probably be dropped in next major version\nexport function withLegacyReturn<T extends (...args: any[]) => Promise<JwtReturnType<any, any>>>(cb: T) {\n  return async (...args: Parameters<T>): Promise<NonNullable<Awaited<ReturnType<T>>['data']>> | never => {\n    const { data, errors } = await cb(...args);\n    if (errors) {\n      throw errors[0];\n    }\n    return data;\n  };\n}\n\n// TODO(dimkl): Will be probably be dropped in next major version\nexport function withLegacySyncReturn<T extends (...args: any[]) => JwtReturnType<any, any>>(cb: T) {\n  return (...args: Parameters<T>): NonNullable<Awaited<ReturnType<T>>['data']> | never => {\n    const { data, errors } = cb(...args);\n    if (errors) {\n      throw errors[0];\n    }\n    return data;\n  };\n}\n"], "names": [], "mappings": ";;;;;AAGO,SAAS,iBAAiF,EAAA,EAAO;IACtG,OAAO,OAAA,GAAU,SAAsF;QACrG,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,GAAI,MAAM,GAAG,GAAG,IAAI;QACzC,IAAI,QAAQ;YACV,MAAM,MAAA,CAAO,CAAC,CAAA;QAChB;QACA,OAAO;IACT;AACF;AAGO,SAAS,qBAA4E,EAAA,EAAO;IACjG,OAAO,CAAA,GAAI,SAA6E;QACtF,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,GAAI,GAAG,GAAG,IAAI;QACnC,IAAI,QAAQ;YACV,MAAM,MAAA,CAAO,CAAC,CAAA;QAChB;QACA,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 4986, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@clerk+backend@2.1.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/@clerk/backend/src/index.ts"], "sourcesContent": ["import type { TelemetryCollectorOptions } from '@clerk/shared/telemetry';\nimport { TelemetryCollector } from '@clerk/shared/telemetry';\nimport type { SDKMetadata } from '@clerk/types';\n\nimport type { ApiClient, CreateBackendApiOptions } from './api';\nimport { createBackendApiClient } from './api';\nimport { withLegacyReturn } from './jwt/legacyReturn';\nimport type { CreateAuthenticateRequestOptions } from './tokens/factory';\nimport { createAuthenticateRequest } from './tokens/factory';\nimport { verifyToken as _verifyToken } from './tokens/verify';\n\nexport const verifyToken = withLegacyReturn(_verifyToken);\n\nexport type ClerkOptions = CreateBackendApiOptions &\n  Partial<\n    Pick<\n      CreateAuthenticateRequestOptions['options'],\n      'audience' | 'jwtKey' | 'proxyUrl' | 'secretKey' | 'publishableKey' | 'domain' | 'isSatellite'\n    >\n  > & { sdkMetadata?: SDKMetadata; telemetry?: Pick<TelemetryCollectorOptions, 'disabled' | 'debug'> };\n\n// The current exported type resolves the following issue in packages importing createClerkClient\n// TS4023: Exported variable 'clerkClient' has or is using name 'AuthErrorReason' from external module \"/packages/backend/dist/index\" but cannot be named.\nexport type ClerkClient = {\n  telemetry: TelemetryCollector;\n} & ApiClient &\n  ReturnType<typeof createAuthenticateRequest>;\n\nexport function createClerkClient(options: ClerkOptions): ClerkClient {\n  const opts = { ...options };\n  const apiClient = createBackendApiClient(opts);\n  const requestState = createAuthenticateRequest({ options: opts, apiClient });\n  const telemetry = new TelemetryCollector({\n    ...options.telemetry,\n    publishableKey: opts.publishableKey,\n    secretKey: opts.secretKey,\n    samplingRate: 0.1,\n    ...(opts.sdkMetadata ? { sdk: opts.sdkMetadata.name, sdkVersion: opts.sdkMetadata.version } : {}),\n  });\n\n  return {\n    ...apiClient,\n    ...requestState,\n    telemetry,\n  };\n}\n\n/**\n * General Types\n */\nexport type { OrganizationMembershipRole } from './api/resources';\nexport type { VerifyTokenOptions } from './tokens/verify';\n/**\n * JSON types\n */\nexport type {\n  ActorTokenJSON,\n  AccountlessApplicationJSON,\n  ClerkResourceJSON,\n  TokenJSON,\n  AllowlistIdentifierJSON,\n  BlocklistIdentifierJSON,\n  ClientJSON,\n  CnameTargetJSON,\n  DomainJSON,\n  EmailJSON,\n  EmailAddressJSON,\n  ExternalAccountJSON,\n  IdentificationLinkJSON,\n  InstanceJSON,\n  InstanceRestrictionsJSON,\n  InstanceSettingsJSON,\n  InvitationJSON,\n  JwtTemplateJSON,\n  OauthAccessTokenJSON,\n  OAuthApplicationJSON,\n  OrganizationJSON,\n  OrganizationDomainJSON,\n  OrganizationDomainVerificationJSON,\n  OrganizationInvitationJSON,\n  OrganizationSettingsJSON,\n  PublicOrganizationDataJSON,\n  OrganizationMembershipJSON,\n  OrganizationMembershipPublicUserDataJSON,\n  PhoneNumberJSON,\n  ProxyCheckJSON,\n  RedirectUrlJSON,\n  SessionJSON,\n  SignInJSON,\n  SignInTokenJSON,\n  SignUpJSON,\n  SignUpVerificationJSON,\n  SignUpVerificationsJSON,\n  SMSMessageJSON,\n  UserJSON,\n  VerificationJSON,\n  WaitlistEntryJSON,\n  Web3WalletJSON,\n  DeletedObjectJSON,\n  PaginatedResponseJSON,\n  TestingTokenJSON,\n  WebhooksSvixJSON,\n} from './api/resources/JSON';\n\n/**\n * Resources\n */\nexport type {\n  ActorToken,\n  AccountlessApplication,\n  AllowlistIdentifier,\n  BlocklistIdentifier,\n  Client,\n  CnameTarget,\n  Domain,\n  EmailAddress,\n  ExternalAccount,\n  Instance,\n  InstanceRestrictions,\n  InstanceSettings,\n  Invitation,\n  JwtTemplate,\n  OauthAccessToken,\n  OAuthApplication,\n  Organization,\n  OrganizationDomain,\n  OrganizationDomainVerification,\n  OrganizationInvitation,\n  OrganizationMembership,\n  OrganizationMembershipPublicUserData,\n  OrganizationSettings,\n  PhoneNumber,\n  Session,\n  SignInToken,\n  SignUpAttempt,\n  SMSMessage,\n  Token,\n  User,\n  TestingToken,\n} from './api/resources';\n\n/**\n * Webhooks event types\n */\nexport type {\n  EmailWebhookEvent,\n  OrganizationWebhookEvent,\n  OrganizationDomainWebhookEvent,\n  OrganizationInvitationWebhookEvent,\n  OrganizationMembershipWebhookEvent,\n  RoleWebhookEvent,\n  PermissionWebhookEvent,\n  SessionWebhookEvent,\n  SMSWebhookEvent,\n  UserWebhookEvent,\n  WaitlistEntryWebhookEvent,\n  WebhookEvent,\n  WebhookEventType,\n} from './api/resources/Webhooks';\n\n/**\n * Auth objects\n */\nexport type { AuthObject, InvalidTokenAuthObject } from './tokens/authObjects';\nexport type { SessionAuthObject, MachineAuthObject } from './tokens/types';\n"], "names": ["verifyToken"], "mappings": ";;;;;;;;;;;;AACA,SAAS,0BAA0B;;;;;;;AAU5B,IAAMA,wUAAc,mBAAA,uUAAiB,cAAY;AAiBjD,SAAS,kBAAkB,OAAA,EAAoC;IACpE,MAAM,OAAO;QAAE,GAAG,OAAA;IAAQ;IAC1B,MAAM,qVAAY,yBAAA,EAAuB,IAAI;IAC7C,MAAM,wVAAe,4BAAA,EAA0B;QAAE,SAAS;QAAM;IAAU,CAAC;IAC3E,MAAM,YAAY,IAAI,wUAAA,CAAmB;QACvC,GAAG,QAAQ,SAAA;QACX,gBAAgB,KAAK,cAAA;QACrB,WAAW,KAAK,SAAA;QAChB,cAAc;QACd,GAAI,KAAK,WAAA,GAAc;YAAE,KAAK,KAAK,WAAA,CAAY,IAAA;YAAM,YAAY,KAAK,WAAA,CAAY,OAAA;QAAQ,IAAI,CAAC,CAAA;IACjG,CAAC;IAED,OAAO;QACL,GAAG,SAAA;QACH,GAAG,YAAA;QACH;IACF;AACF", "ignoreList": [0]}}]}