{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules__pnpm_04490921._.js", "server/edge/chunks/[root-of-the-server]__9f45d194._.js", "server/edge/chunks/apps_web_edge-wrapper_a462b325.js", "server/edge/chunks/_31b668e4._.js", "server/edge/chunks/68ae3_@clerk_shared_dist_e1abcbbc._.js", "server/edge/chunks/b3412_@clerk_backend_dist_334fdccb._.js", "server/edge/chunks/9aefa_@clerk_nextjs_dist_esm_33e6bb40._.js", "server/edge/chunks/node_modules__pnpm_fac582cc._.js", "server/edge/chunks/[root-of-the-server]__9ae05967._.js", "server/edge/chunks/apps_web_edge-wrapper_d40864d9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mzIinH1YWNyMD7WtBtBW66vi+sBEHUpA1+aIXbmr4q8=", "__NEXT_PREVIEW_MODE_ID": "9d5d6454185e391bad554a48cc6e60fe", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e9ee739faccf101189a019f68492804a2793d59fc25d9d0e72ca09cb6680ec8b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f03460108b099591c4517053270956990e96baf028bd5867574f56ebc698d1f2"}}}, "sortedMiddleware": ["/"], "functions": {}}