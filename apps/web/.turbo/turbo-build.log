

> web@0.1.0 build /Users/<USER>/Downloads/Coding/Personal/BuddyChipUtimate/apps/web
> next build

   [1m[38;2;173;127;168m▲ Next.js 15.3.0[39m[22m
   - Environments: .env
   - Experiments (use with caution):
     · clientTraceMetadata

 [37m[1m [22m[39m Creating an optimized production build ...
[33m[@sentry/nextjs] Could not find `onRequestError` hook in instrumentation file. This indicates outdated configuration of the Sentry SDK. Use `Sentry.captureRequestError` to instrument the `onRequestError` hook: https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#errors-from-nested-react-server-components[39m
[33mwarn[39m  - It seems like you don't have a global error handler set up. It is recommended that you add a [36mglobal-error.js[39m file with Sentry instrumentation so that React rendering errors are reported to Sentry. Read more: https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/#react-render-errors-in-app-router (you can suppress this warning by setting SENTRY_SUPPRESS_GLOBAL_ERROR_HANDLER_FILE_WARNING=1 as environment variable)
[@sentry/nextjs] DEPRECATION WARNING: It is recommended renaming your `sentry.client.config.ts` file, or moving its content to `instrumentation-client.ts`. When using Turbopack `sentry.client.config.ts` will no longer work. Read more about the `instrumentation-client.ts` file: https://nextjs.org/docs/app/api-reference/file-conventions/instrumentation-client
